<cfparam name="attributes.data" default="#structNew()#">

<cfif thisTag.executionMode neq "start">
	<cfexit>
</cfif>

<cfset dataStruct = attributes.data.actionStruct>
<cfset calendarSettings = attributes.data.calendarSettings>
<cfset baseQueryString = attributes.data.baseQueryString>
<cfset arrEvents = attributes.data.actionStruct.arrEvents>

<!--- common js --->
<cfinclude template="../commonBaseView.cfm">
<!--- Nav Bar --->
<cfinclude template="calendarNavbar.cfm">

<!--- show/hide on current month list view only --->
<cfset local.ShowHidepastEventsFlag = false />
<cfif month(dataStruct.firstOfTheMonth) is month(now()) and year(dataStruct.firstOfTheMonth) is year(now())>
	<cfset local.ShowHidepastEventsFlag = true>
</cfif>

<cfif structKeyExists(url,"showAll")>
	<cfif url.showAll eq '1'>
		<cfset local.urlToShow = attributes.data.linkForPaging />
		<cfset local.urlToHide = replaceNoCase(attributes.data.linkForPaging,"showAll=1","showAll=0") />
	<cfelse>
		<cfset local.urlToShow = replaceNoCase(attributes.data.linkForPaging,"showAll=0","showAll=1") />
		<cfset local.urlToHide = attributes.data.linkForPaging />
	</cfif>
<cfelse>
	<cfset local.urlToShow = toString(attributes.data.linkForPaging) & '&showAll=1' />
	<cfset local.urlToHide = toString(attributes.data.linkForPaging) & '&showAll=0' />
</cfif>

<!--- main content --->
<cfif arrayLen(arrEvents) or local.ShowHidepastEventsFlag>
	<cfoutput>
	<table border="0" cellpadding="3" cellspacing="0" width="100%">
	<tr bgcolor="##DEDEDE">
		<td class="tsAppBodyText tsAppBB tsAppBT" width="125"><b>Category</b></td>
		<td class="tsAppBodyText tsAppBB tsAppBT"><b>Event</b></td>
	</tr>
	<cfif isDefined("attributes.data.prevPage")>
		<cfif (attributes.data.prevPage NEQ 0) OR (((attributes.data.nextPage-1)*attributes.data.rowsize + 1) LTE arrayLen(arrEvents))>
			<tr>
				<td colspan="2" class="tsAppBodyText" align="right">
					<cfif attributes.data.prevPage NEQ 0>
						<a href="#attributes.data.linkForPaging#&PageNum=#attributes.data.prevPage#"><< Previous Page</a>
					</cfif>
					<cfif (attributes.data.prevPage NEQ 0) AND (((attributes.data.nextPage-1)*attributes.data.rowsize + 1) LTE arrayLen(arrEvents))> | </cfif>
					<cfif ((attributes.data.nextPage-1)*attributes.data.rowsize + 1) lte arrayLen(arrEvents)>
						<a href="#attributes.data.linkForPaging#&PageNum=#attributes.data.nextPage#">Next Page &gt;&gt;</a>
					</cfif>
				</td>
			</tr>
		</cfif>	
	</cfif>
	
	<!--- show/hide on current month list view only --->
	<cfif local.ShowHidepastEventsFlag>
		<tr id="HidepastEventMessage" <cfif structKeyExists(url,"showAll") and url.showAll eq 1>style="display:none;"</cfif>>
			<td colspan="2" class="tsAppBodyText" align="right">
				Note: This month's past events are hidden. <a href="#local.urlToShow#">Click to view all events</a>.
			</td>
		</tr>
		<tr id="ShowpastEventMessage" <cfif not structKeyExists(url,"showAll") or (structKeyExists(url,"showAll") and url.showAll neq 1)>style="display:none;"</cfif>>
			<td colspan="2" class="tsAppBodyText" align="right">
				<a href="#local.urlToHide#">Click to hide past events</a>.
			</td>
		</tr>
	</cfif>
	
	<!--- set variables for loop based on pagination --->
	<cfif isDefined("attributes.data.currPage")>
		<cfset local.fromNum=((attributes.data.currPage-1)*attributes.data.rowsize)+1>
		<cfif ((attributes.data.currPage-1)*attributes.data.rowsize)+attributes.data.rowsize gt arrayLen(arrEvents)>
			<cfset local.toNum=arrayLen(arrEvents)>
		<cfelse>
			<cfset local.toNum=((attributes.data.currPage-1)*attributes.data.rowsize)+attributes.data.rowsize>
		</cfif>
	<cfelse>
		<cfset local.fromNum=1>
		<cfset local.toNum=arrayLen(arrEvents)>
	</cfif>
	
	<cfset local.eventList = []>
	<cfloop index="x" from="#local.fromNum#" to="#local.toNum#">
		<cfset thisEvent = arrEvents[x]>
		
		<!-- Prepare categories array -->
		<cfset local.categories = []>
		<cfloop list="#thisEvent.categoryIDList#" index="categoryID">
			<cfset arrayAppend(local.categories, {
				"categoryID": categoryID,
				"categoryName": ListGetAt(thisEvent.categoryNameList, ListFind(thisEvent.categoryIDList, categoryID), '|')
			})>
		</cfloop>
		
		<!-- Add event details to local.eventList -->
		<cfset arrayAppend(local.eventList, {
			"eventID": thisEvent.eventID,
			"title": encodeForHTML(thisEvent.eventTitle),
			"categories": local.categories
		})>
	
		<tr class="catev category#Replace(thisEvent.categoryIDList,'|',' category','ALL')#" valign="top">
			<td class="tsAppBodyText" width="125">
				#Replace(thisEvent.categoryNameList,"|","<br/>","ALL")#
			</td>
			<td class="tsAppBodyText">
				<a style="font-weight:bold;" href="javascript:gotoEvent(#thisEvent.eventID#,#thisEvent.isSWL#);">#encodeForHTML(thisEvent.eventTitle)#</a><br>
				<cfif len(thisEvent.eventSubTitle)><b>#encodeForHTML(thisEvent.eventSubTitle)#</b><br></cfif>
				#thisEvent.formattedDate#
			</td>
		</tr>
	</cfloop>
	<cfif isDefined("attributes.data.prevPage")>
		<cfif (attributes.data.prevPage NEQ 0) OR (((attributes.data.nextPage-1)*attributes.data.rowsize + 1) LTE arrayLen(arrEvents))>
			<tr>
				<td colspan="2" class="tsAppBodyText" align="right">
					<cfif attributes.data.prevPage NEQ 0>
						<a href="#attributes.data.linkForPaging#&PageNum=#attributes.data.prevPage#"><< Previous Page</a>
					</cfif>
					<cfif (attributes.data.prevPage NEQ 0) AND (((attributes.data.nextPage-1)*attributes.data.rowsize + 1) LTE arrayLen(arrEvents))> | </cfif>
					<cfif ((attributes.data.nextPage-1)*attributes.data.rowsize + 1) lte arrayLen(arrEvents)>
						<a href="#attributes.data.linkForPaging#&PageNum=#attributes.data.nextPage#">Next Page &gt;&gt;</a>
					</cfif>
				</td>
			</tr>
		</cfif>	
	</cfif>
	</table>
	<cfif not arrayLen(arrEvents)>	
		<p class="tsAppBodyText">There are no remaining events scheduled for this month.</p>	
	</cfif>
	<script language="javascript">
		$(function() {			
			<cfset local.eventsJSON = SerializeJSON(local.eventList)>
			<cfif IsJSON(local.eventsJSON)>
				MCLoader.loadJS('/assets/common/javascript/mcapps/events/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {
					try {
						triggerEventsViewItemList('#attributes.data.pageName#',#local.eventsJSON#, 'List');
					} catch (error) {
						console.error("Error parsing JSON:", error.message);
					}
				});
			</cfif>
		});
	</script>
	</cfoutput>
<cfelse>
	<cfoutput><p class="tsAppBodyText">There are no events scheduled for this month.</p></cfoutput>
</cfif>

<!--- footer code --->
<cfsavecontent variable="footerCode">
	<cfinclude template="commonBaseViewFooter.cfm">
</cfsavecontent>
<cfoutput>#application.objCommon.minText(footerCode)#</cfoutput>
