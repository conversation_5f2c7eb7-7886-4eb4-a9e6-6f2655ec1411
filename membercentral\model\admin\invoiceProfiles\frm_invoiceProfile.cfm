<cfsavecontent variable="local.invoiceProfileJS">
	<cfoutput>
	<script language="javascript">
		function validateInvoiceProfileForm() {
			var arrReq = new Array();
			mca_hideAlert('err_invoiceprofile');

			if ($('##profileName').val().length == 0) arrReq[arrReq.length] = 'Enter a name for this Invoice Profile.';
			if ($('##invoiceImage').val().length > 0 && !/(\.gif|\.png)$/i.test($('##invoiceImage').val())) arrReq[arrReq.length] = 'The invoice logo is an invalid image type. Only GIF or PNG files are accepted.';
		
			if ($('##numDaysDelinquent').val().length > 0) {
				var numDays = parseInt($('##numDaysDelinquent').val());
				if (isNaN(numDays)) numDays = 0;
				if (numDays <= 0) arrReq[arrReq.length] = 'Enter a valid number of days for Delinquent invoices. It must either be blank or more than 0.';
			}
			if ($('##enableProcessingFeeDonation').val() == 1 && $('##solicitationMessageID').val() == 0) arrReq.push('Select the Solicitation Title & Message.');

			if (arrReq.length > 0) {
				mca_showAlert('err_invoiceprofile', arrReq.join('<br/>'), true);
				return false;
			}
			
			$('##frmInvoiceProfile').submit();
			top.$("##btnMCModalSave").prop('disabled',true).html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Saving...');
			return true;
		}
		function onChangeProcessFeeDonationSettings() {
			if ($('##enableProcessingFeeDonation').val() == 1) $('##procFeeDonationSettings').removeClass('d-none');
			else $('##procFeeDonationSettings').addClass('d-none');
		}
		
		$(function() {
			mca_setupCustomFileControls('frmInvoiceProfile');
			mca_setupTagsInput('notifyEmail', 'err_invoiceprofile', "#application.regEx.email#", 'email address', true);

			<cfif arguments.event.getValue('err','') is 1>
				mca_showAlert('err_invoiceprofile', 'There was an error processing the uploaded image.<br/>Be sure the image is a PNG or GIF and is exactly 400x150 pixels.');
			<cfelseif arguments.event.getValue('err','') is 2>
				mca_showAlert('err_invoiceprofile', 'The Invoice Profile name you entered is already in use. Try again.');
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.invoiceProfileJS)#">

<cfoutput>
<form name="frmInvoiceProfile" id="frmInvoiceProfile" action="#this.link.save#" method="post"  enctype="multipart/form-data">
<input type="hidden" name="profileID"  id="profileID" value="#arguments.event.getValue('profileID')#">

<div id="err_invoiceprofile" class="alert alert-danger mb-2 d-none"></div>
<div class="px-2">
	<div class="form-group">
		<div class="form-label-group">
			<input type="text" name="profileName" id="profileName" value="#local.invoiceProfile.profileName#" class="form-control" autocomplete="off" maxlength="50">					
			<label for="profileName">Profile Name *</label>
		</div>
	</div>
	<div class="form-group row">
		<label for="invoiceImage" class="col-sm-auto col-form-label-sm font-size-md">Invoice Image</label>
		<div class="col-sm mt-1">			
			<div class="custom-file form-control-sm mr-2">
				<input type="file" name="invoiceImage" id="invoiceImage" class="custom-file-input importFileControl">
				<label for="importFileName" class="custom-file-label">Choose File</label>
			</div>
			<div class="mt-1 mb-3 text-dim small">
				We accept GIF or PNG image files that are 400 pixels wide x 150 pixels high.<br/>It is best if the images have a white background.
			</div>
			<cfif val(local.invoiceProfile.profileID) gt 0 and FileExists('#application.paths.RAIDUserAssetRoot.path#common/invoices/#local.invoiceProfile.profileID#.#local.invoiceProfile.imageExt#')>
				<div>
					<img class="pendingImage border" src="/userassets/common/invoices/#local.invoiceProfile.profileID#.#local.invoiceProfile.imageExt#?rand=#randrange(1,1000)#" data-imgsrc="/userassets/common/invoices/#local.invoiceProfile.profileID#.#local.invoiceProfile.imageExt#?rand=#randrange(1,1000)#">
					<div class="mt-1">
						<div class="form-check">
							<input type="checkbox" name="deleteImage" id="deleteImage" value="true" class="form-check-input">
							<label for="deleteImage" class="form-check-label">Delete Current Image</label>
						</div>
					</div>
				</div>
				
			</cfif>
		</div>
	</div>
	<div class="form-group mb-4">
		<div class="small">This invoice profile belongs to the following Organization Identity:</div>
		#local.strOrgIdentitySelector.html#	
	</div>
	<div class="card card-box mt-4">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Payment Settings</div>	
		</div>
		<div class="card-body">
			<div class="form-group">
				<div class="form-label-group">
					<select name="enableAutoPay" id="enableAutoPay" class="custom-select">
						<option value="1" <cfif not len(local.invoiceProfile.enableAutoPay) or local.invoiceProfile.enableAutoPay>selected</cfif>>Yes - Invoices with Pay Method Associated should be auto paid on invoice due date</option>
						<option value="0" <cfif len(local.invoiceProfile.enableAutoPay) and local.invoiceProfile.enableAutoPay is 0>selected</cfif>>No - Invoices with Pay Method Associated should NOT be auto paid. Must be manually run.</option>		
					</select>
					<label for="enableAutoPay">Invoice Autopay</label>
				</div>
			</div>
			<div class="form-group">
				<div class="form-label-group">
					<select name="enforcePayOldest" id="enforcePayOldest" class="custom-select">
						<option value="1" <cfif local.invoiceProfile.enforcePayOldest is 1>selected</cfif>>Yes - Enforce paying oldest due invoices first on front end</option>
						<option value="0" <cfif local.invoiceProfile.enforcePayOldest is not 1>selected</cfif>>No - Do not enforce paying oldest due invoices first on front end</option>
					</select>
					<label for="enforcePayOldest">Pay Oldest</label>
				</div>
			</div>
			<div class="form-group">
				<div class="form-label-group">
					<select name="allowPartialPayment" id="allowPartialPayment" class="custom-select">
						<option value="1" <cfif local.invoiceProfile.allowPartialPayment is 1>selected</cfif>>Yes - Allow members to make partial payments on front end</option>
						<option value="0" <cfif local.invoiceProfile.allowPartialPayment is not 1>selected</cfif>>No - Do not allow members to make partial payments on front end</option>
					</select>
					<label for="allowPartialPayment">Allow Partial Payments</label>
				</div>
			</div>	
			<div class="form-group">
				<div class="form-label-group">
					<input type="text" name="notifyEmail" id="notifyEmail" value="#local.invoiceProfile.notifyEmail#" class="form-control"  maxlength="100">					
					<label for="notifyEmail">Optional Email addresses to notify when members pay via #application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname#/invoices</label>
				</div>
			</div>
			<div class="form-group row mt-2">
				<div class="col">
					#createObject("component","model.admin.common.modules.paymentProfileSelector.paymentProfileSelector").getPaymentProfileSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="invpMPProfiles", selectedPaymentProfileIDList=valueList(local.qrySelectedMerchantProfiles.merchantProfileID), fieldLabel="Associated invoices may only be paid using these payment profiles:", colCount=2, allowOffline=true, allowPayLater=false).html#
				</div>
			</div>
		</div>
	</div>

	<div class="card card-box mt-4">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Delinquent Settings</div>	
		</div>
		<div class="card-body">
			<div class="form-group row">
				<div class="col-sm">			
					<div>
						Delinquent Invoices are those that are 
						<input type="text" id="numDaysDelinquent" name="numDaysDelinquent" value="#local.invoiceProfile.numDaysDelinquent#" class="form-control form-control-sm d-inline" style="width:100px;">
						days or more past due.
					</div>
					<div class="mt-1 text-dim small"><i>Optional: If left blank, invoices using this invoice profile will not have a Delinquent status.</i></div>
				</div>	
			</div>	
		</div>
	</div>

	<cfif local.qryPayProfilesSupportingProcessingFees.recordCount>
		<div class="card card-box mt-4">
			<div class="card-header bg-light">
				<div class="card-header--title font-weight-bold font-size-md">Voluntary Processing Fee Donation</div>	
			</div>
			<div class="card-body">
				<div class="card-text mb-3">
					You may override MemberCentral Payments payment profile settings when paying for revenue using this Invoice Profile. This applies only to payments made via 
					<cfloop query="local.qryPayProfilesSupportingProcessingFees">
						#local.qryPayProfilesSupportingProcessingFees.profileName#<cfif local.qryPayProfilesSupportingProcessingFees.recordCount is not local.qryPayProfilesSupportingProcessingFees.currentrow>, <cfelse>.</cfif>
					</cfloop>
				</div>
				<div class="form-group">
					<div class="form-label-group">
						<select name="enableProcessingFeeDonation" id="enableProcessingFeeDonation" class="custom-select" onchange="onChangeProcessFeeDonationSettings();" autocomplete="off">
							<option value=""></option>
							<option value="0" <cfif len(local.invoiceProfile.enableProcessingFeeDonation) AND local.invoiceProfile.enableProcessingFeeDonation is 0>selected</cfif>>No, do not ask members to voluntarily pay Processing Fee Donation when paying via this profile</option>
							<option value="1" <cfif len(local.invoiceProfile.enableProcessingFeeDonation) AND local.invoiceProfile.enableProcessingFeeDonation is 1>selected</cfif>>Yes, ask members to voluntarily pay Processing Fee Donation when paying via this profile</option>
						</select>
						<label for="enableProcessingFeeDonation">Enable Voluntary Processing Fee Donation</label>
					</div>
				</div>
				<div id="procFeeDonationSettings"<cfif val(local.invoiceProfile.enableProcessingFeeDonation) is 0> class="d-none"</cfif>>
					<div class="form-group">
						<cfset local.strSolicitationMsgSelector = createObject("component","model.admin.common.modules.solicitationMessageSelector.solicitationMessageSelector").getMessageSelector(
							siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="solicitationMessageID", selectedValueID=val(local.invoiceProfile.solicitationMessageID), usageMode='invoiceProfile')>
						<div class="pb-0 pl-3 small">Solicitation Title and Message Shown During the Payment Process</div>
						<div>#local.strSolicitationMsgSelector.html#</div>
					</div>
					<div class="form-group">
						<div class="form-label-group">
							<select name="processFeeDonationDefaultSelect" id="processFeeDonationDefaultSelect" class="custom-select" autocomplete="off">
								<option value="0" <cfif local.invoiceProfile.processFeeDonationDefaultSelect is 0>selected</cfif>>No default selection, but member is required to select Yes or No option when paying.</option>
								<option value="1" <cfif local.invoiceProfile.processFeeDonationDefaultSelect is 1>selected</cfif>>Pre-select the YES option by default but members may still opt-out.</option>
							</select>
							<label for="processFeeDonationDefaultSelect">Default Selection</label>
						</div>
					</div>
				</div>
			</div>
		</div>
	</cfif>

</div>
<!--- hidden submit triggered from parent --->
<button type="button" name="btnSubmit" id="btnSubmit" class="d-none" onClick="return validateInvoiceProfileForm();"></button>

</form>
</cfoutput>