<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = "controller">
	<cfset variables.communitySettings = structNew()/>
	<cfset variables.applicationReservedURLParams = "commAction">
	
	<cffunction name="init" access="package" returntype="void" output="false">
		<cfargument name="languageID" type="numeric" required="true">
		<cfset variables.communitySettings = getCommunitySettings(this.appInstanceID, languageID)/>
	</cffunction>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		<cfset local.rc = arguments.event.getCollection()>
		<cfset init(local.rc.mc_pageDefinition.pageLanguageID)>
		<cfset local.returnStruct = structNew()>
		<cfset local.viewToUse = "community/notFound"/>
		<cfset local.returnStruct.communitySettings = variables.communitySettings/>
		<cfset local.returnStruct.actionStruct = structNew()>
		
		<cfset local.pageToLoad = arguments.event.getValue("commpg",variables.communitySettings.defaultCommunityPageName)>

		<cfif not len(local.pageToLoad)>
			<cfset local.pageToLoad = listFirst(valueList(variables.communitySettings.communityPages.pageName))>
		<cfelseif not listFindNoCase(valueList(variables.communitySettings.communityPages.pageName),local.pageToLoad)>
			<cfset local.pageToLoad = "">
			<cfset arguments.event.removeValue("commpg")>
		</cfif>
		
		<cfif not len(local.pageToLoad)>
			<cfset arguments.event.setValue("mc_trigger404page",1)>
		<cfelse>
			<cfswitch expression="#arguments.event.getValue('commAction', '')#">
				<cfcase value="joinGroup">
					<!--- Send email  --->
					<cfset local.qrySiteAdminEmails = application.objSiteInfo.getSiteAdminEmails(orgID=variables.communitySettings.orgid)>
					<cfset local.orgMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.communitySettings.orgid)>

					<cfif local.qrySiteAdminEmails.recordCount>
						<!--- Email to the admin --->
						<cfsavecontent variable="local.emailContent">
							<cfoutput>
							<p>#session.cfcuser.memberData.firstname# #session.cfcuser.memberData.lastname# would like to join the community #variables.communitySettings.communityName#.<br />
							To approve, add this member to the group that has rights to view this community.</p>
							</cfoutput>
						</cfsavecontent>

						<cfset local.arrEmailTo = []>
						<cfloop query="local.qrySiteAdminEmails">
							<cfif len(local.qrySiteAdminEmails.email)>
								<cfset ArrayAppend(local.arrEmailTo, { name:"", email:local.qrySiteAdminEmails.email })>
							</cfif>
						</cfloop>
						<cfset local.communitySiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Community', siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
						
						<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
							emailto=local.arrEmailTo,
							emailreplyto=arguments.event.getValue('mc_siteInfo.supportProviderEmail'),
							emailsubject="Request to Join #variables.communitySettings.communityName#",
							emailtitle="#arguments.event.getValue('mc_siteinfo.sitename')# Request to Join Community",
							emailhtmlcontent=local.emailContent,
							emailAttachments=[],
							siteID=arguments.event.getValue('mc_siteinfo.siteid'),
							memberID=local.orgMemberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="JOINCOMMUNITY"),
							sendingSiteResourceID=local.communitySiteResourceID)>
					</cfif>
					<cfset arguments.event.setValue('commAction', '')>
				</cfcase>
			</cfswitch>

			<!--- Update PageSlug for passing to Google Analytics. --->
			<cfset local.mc_toplevelPageDefinition = arguments.event.getValue("mc_toplevelPageDefinition")>
			<cfset local.mc_toplevelPageDefinition.pageSlug = local.mc_toplevelPageDefinition.pageSlug & '/commpg/' & rereplace(lcase(local.pageToLoad),"[^\w_\-/]+","-","all") />


			<cfset local.returnStruct.actionStruct.pageHTML = application.objResourceRenderer.processSubPage(
					arguments.event,
					variables.communitySettings.siteID,
					local.pageToLoad,
					session.mcstruct.languageid,
					application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.communitySettings.orgID),
					'')>


			<!--- record app hit --->
			<cfset application.objPlatformStats.recordAppHit(appname="community",appsection="")>
		</cfif>
		<cfreturn returnAppStruct(local.returnStruct,"community/page")>
	</cffunction>
	
	<cffunction name="getCommunitySettings" access="package" returntype="struct">
		<cfargument name="applicationInstanceID" type="numeric" required="yes"/>
		<cfargument name="languageID" type="numeric" required="yes"/>
		
		<cfset var local = structNew()>
		<cfif structCount(variables.communitySettings)>
			<cfreturn variables.communitySettings>
		<cfelse>
			<cfquery name="local.getCommSettings" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select ai.siteresourceID, comm.communityID, comm.communityCenterAppInstanceID, ai.applicationInstanceID, ai.applicationTypeID, comm.CommunityName, comm.defaultCommunityPageName, srt.resourceType as pageResourceType, grandParentType.resourceType as grandParentType, o.orgID, o.orgCode, s.siteID, s.siteCode
				from dbo.comm_Communities comm
				inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = comm.applicationInstanceID
					and ai.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.applicationInstanceID#">
				inner join dbo.cms_siteResources sr on ai.siteResourceID = sr.siteResourceID
				inner join dbo.cms_siteResources pageResource on sr.parentSiteResourceID = pageResource.siteResourceID
				inner join dbo.cms_siteResourceTypes srt on pageResource.resourceTypeID = srt.resourceTypeID
				inner join dbo.sites s on ai.siteID = s.siteID
				inner join dbo.organizations o on s.orgID = o.orgID
				left outer join dbo.cms_siteResources thisCommunityGrandParentResource
					inner join dbo.cms_siteResourceTypes grandParentType on thisCommunityGrandParentResource.resourceTypeID = grandParentType.resourceTypeID
				on thisCommunityGrandParentResource.siteResourceID = pageResource.parentSiteResourceID;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.commSettings = structNew()>
			<cfloop index="local.thisField" list="#local.getCommSettings.columnList#">
				<cfset local.commSettings[local.thisField] = local.getCommSettings[local.thisField]>
			</cfloop>
			
			<cfquery name="local.getInstanceSettings2" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @environmentID int, @environmentName varchar(50) = <cfqueryparam value="#application.MCEnvironment#" cfsqltype="CF_SQL_VARCHAR">;

				select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

				select sn.socialNetworkID, sn.masterSiteID, s.siteName, s.sitecode, s.orgID as masterOrgID, sh.hostname as mainHostName,
					ai.applicationInstanceName as SocialNetworkInstanceName
				from dbo.sn_socialNetworks childsn
				inner join dbo.cms_applicationInstances as childai on childai.applicationInstanceID = childsn.applicationInstanceID 
				inner join dbo.sn_socialNetworks sn on childsn.masterSocialNetworkID = sn.socialNetworkID
				inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = sn.applicationInstanceID 
				inner join dbo.sites s on s.siteID = sn.masterSiteID
				inner join dbo.siteEnvironments se on se.siteID = s.siteID and se.environmentID = @environmentID
				inner join dbo.siteHostnames as sh on sh.hostnameID = se.mainHostnameID
				where childai.siteid = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.getCommSettings.siteID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfloop index="local.thisField" list="#local.getInstanceSettings2.columnList#">
				<cfset local.commSettings[local.thisField] = local.getInstanceSettings2[local.thisField]>
			</cfloop>
						
			<cfstoredproc procedure="comm_getCommunityStructure" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.commSettings.siteID#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.commSettings.communityID#">
				<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.languageID#">
				<cfprocresult name="local.commSettings.communityPages" resultset="1">
				<cfprocresult name="local.commSettings.subCommunities" resultset="2">
				<cfprocresult name="local.commSettings.parentCommunity" resultset="3">
			</cfstoredproc>		

			<cfreturn local.commSettings>
		</cfif>
	</cffunction>
	<cffunction name="isInstanceValid" access="package" output="false" returntype="boolean">
		<cfreturn (structKeyExists(variables.communitySettings,"applicationInstanceID") and isnumeric(variables.communitySettings.applicationInstanceID))>
	</cffunction>

	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">	
		<cfargument name="returnToAppAdmin" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.objPageAdmin = createObject("component","model.admin.pages.pageAdmin")>

		<cfif cgi.request_method eq "POST">
			<!--- validate page names --->
			<cfset local.arrCommunities = listToArray(arguments.event.getValue('comms',''),chr(10))>
			<cfset local.arrPageNames = []>
			<cfset local.arrReservedWords = []>
			<cfset local.arrExistingPageNames = []>

			<cfloop array="#local.arrCommunities#" index="local.thisComm">
				<cfif listLen(local.thisComm) is 2>
					<cfset local.pageName = REReplaceNoCase(trim(GetToken(local.thisComm,1,',')),"[^A-Z0-9\-_]+","","ALL")>
					<cfset local.isReserved = local.objPageAdmin.checkForReservedWords(newWord=local.pageName)>
					<cfif local.isReserved>
						<cfset local.arrReservedWords.append(local.pageName)>
					<cfelse>
						<cfset local.arrPageNames.append(local.pageName)>
					</cfif>
				</cfif>
			</cfloop>

			<cfif arrayLen(local.arrPageNames)>
				<cfquery name="local.qryExistingPageNames" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
					DECLARE @tmpPages TABLE (pageName varchar(100));

					<cfloop array="#local.arrPageNames#" index="local.thisPage">
						INSERT INTO @tmpPages (pageName) VALUES ('#local.thisPage#');
					</cfloop>

					SELECT p.pageName
					from dbo.cms_pages as p
					inner join dbo.cms_siteResources as sr on sr.siteID = @siteID 
						and sr.siteResourceID = p.siteResourceID
						and sr.siteResourceStatusID in (1,2)
					inner join @tmpPages as tmp on tmp.pageName = p.pageName
					where p.siteID = @siteID;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfset local.arrExistingPageNames = queryColumnData(local.qryExistingPageNames,'pageName')>
			</cfif>
			
			<cfif arrayLen(local.arrReservedWords) OR arrayLen(local.arrExistingPageNames)>
				<cfset arguments.event.setValue('mc_err_arrexistingpages',local.arrReservedWords.merge(local.arrExistingPageNames))>
				<cfset local.appInfo = CreateObject("component","model.admin.pages.appCreationProcess").getAppInfo(siteID=arguments.event.getValue('mc_siteInfo.siteID'), applicationTypeID=arguments.event.getValue('appTypeID'))>
				<cfset showAppInstanceForm(event=arguments.event, appInfo=arguments.appInfo)>
			<cfelse>
				<cftry>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @siteID int, @pageSectionID int, @searchFieldSetID int, @resultsFieldSetID int, 
									@detailFieldSetID int, @GLAccountID int, @incAnnouncements bit, @incCalendar bit, 
									@incFileShare2 bit, @incLists bit, @incDirectory bit, @incPhotos bit, @incVideos bit, 
									@eventCategoryName varchar(50), @eventCategoryShortName varchar(20), @eventCategoryColor varchar(15),
									@thispageName varchar(100), @thispageTitle varchar(200);
							DECLARE @commList TABLE (pageName varchar(100), pageTitle varchar(200));

							<cfloop array="#local.arrCommunities#" index="local.thisComm">
								<cfif listLen(local.thisComm) is 2>
									INSERT INTO @commlist (pageName, pageTitle) VALUES ('#REReplaceNoCase(trim(GetToken(local.thisComm,1,',')),"[^A-Z0-9\-_]+","","ALL")#', '#trim(GetToken(local.thisComm,2,','))#');
								</cfif>
							</cfloop>

							set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
							set @pageSectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('sectionID')#">;
							set @searchFieldSetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('SearchFieldSet',0)#">;
							set @resultsFieldSetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('ResultsFieldSet',0)#">;
							set @detailFieldSetID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('DetailFieldSet',0)#">;
							set @GLAccountID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('defaultGLAccountID',0)#">;
							set @incAnnouncements = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('incAnnouncements',0)#">;
							set @incCalendar = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('incCalendar',0)#">;
							set @incFileShare2 = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('incFileShare2',0)#">;
							set @incLists = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('incLists',0)#">;
							set @incDirectory = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('incDirectory',0)#">;
							set @incPhotos = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('incPhotos',0)#">;
							set @incVideos = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('incVideos',0)#">;

							set @eventCategoryName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('eventCategoryName')#">;
							set @eventCategoryShortName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('eventCategoryShortName')#">;
							set @eventCategoryColor = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('eventCategoryColor')#">;

							-- loop over each in the list
							SELECT @thisPageName = min(pageName) from @commList;
							WHILE @thisPageName is not null BEGIN
								SELECT @thisPageTitle = pageTitle from @commlist where pageName = @thisPageName;

								EXEC dbo.cms_createCommunity @siteID=@siteID, @pageSectionID=@pageSectionID, @pageName=@thisPageName, @pageTitle=@thisPageTitle, 
									@incDirectory=@incDirectory, @incLists=@incLists, @incFileShare2=@incFileShare2, @incEvents=@incCalendar, 
									@incAnnouncements=@incAnnouncements, @incPhotos=@incPhotos, @incVideos=@incVideos, @searchFieldSetID=@searchFieldSetID, 
									@resultsFieldSetID=@resultsFieldSetID, @detailFieldSetID=@detailFieldSetID, @eventGLAccountID=@GLAccountID,
									@eventCategoryName=@eventCategoryName, @eventCategoryShortName=@eventCategoryShortName, @eventCategoryColor=@eventCategoryColor;

								SELECT @thisPageName = min(pageName) from @commList where pageName > @thisPageName;
							END

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
				</cfcatch>
				</cftry>
				<cfif arguments.returnToAppAdmin><cfoutput>
						<script language="javascript">
							top.reloadCommunityTable();
							top.MCModalUtils.hideModal();
						</script>
					</cfoutput>
				<cfelse>
					<cfoutput>
						<script language="javascript">
							top.reloadPageTable();
							top.MCModalUtils.hideModal();
						</script>
					</cfoutput>
				</cfif>	
			</cfif>
		<cfelse>
			<cfset showAppInstanceForm(event=arguments.event, appInfo=arguments.appInfo)>
		</cfif>		
	</cffunction>

	<cffunction name="showAppInstanceForm" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="appInfo" type="query">

		<cfset var local = structNew()>

		<cfscript>
			local.allow = false;
			if(arguments.appInfo.instancesCreated EQ 0 OR arguments.appInfo.maxInstancesPerSite - arguments.appInfo.instancesCreated GT 0 )
				local.allow = true;
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
		</cfscript>
		
		<cfif local.allow>
			<cfset local.getSections = CreateObject("component","model.system.platform.section").getRecursiveSections(siteID=local.siteID)>
			<cfset local.objFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector")>
			<cfset local.strFieldSetDirectorySearchSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="SearchFieldSet", allowBlankOption=true, fieldLabel="Directory Search Field Set")>
			<cfset local.strFieldSetDirectoryResultsSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="ResultsFieldSet", allowBlankOption=true, fieldLabel="Directory Results Field Set")>
			<cfset local.strFieldSetDirectoryDetailSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="DetailFieldSet", allowBlankOption=true)>
			<cfset local.glSelectorLink = CreateObject("component","model.admin.admin").buildLinkToTool(toolType="GLAccountSelector",mca_ta="showSelector")>
			<cfset local.pageAdminTool = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='PageAdmin',mca_ta='addAppInstance')>

			<!--- Setup GL Account Widget for Revenue GL --->
			<cfset local.strRevenueGLAcctWidgetData = {
				"label": "Revenue Account for New Events",
				"btnTxt": "Choose GL Account",
				"glatid": 3,
				"widgetMode": "GLSelector",
				"idFldName": "defaultGLAccountID",
				"idFldValue": val(arguments.event.getValue('defaultGLAccountID',0)),
				"pathFldValue": arguments.event.getValue('defaultGLAccountPath',''),
				"pathNoneTxt": "(No account selected)"
			}>
			<cfset local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData)>

			<cfsavecontent variable="local.js">
				<cfoutput>
					
				#local.strRevenueGLAcctWidget.js#
				<script language="javascript">
					function closeBox() { $.colorbox.close(); }

					$(document).ready(function() {	
						$('input[type="checkbox"][name="incCalendar"]').change(function() {
							if(this.checked) $('##incCalendarTbody').show();
							else $('##incCalendarTbody').hide();
						});
                        
						$('input[type="checkbox"][name="incDirectory"]').change(function() {
							if(this.checked) $('##incDirectoryTbody').show();
							else $('##incDirectoryTbody').hide();
						});

                        $("##frmCreateApp").submit(function(e) {
							var arrReq = new Array();

                            if ($('##comms').val() == '') {
								arrReq[arrReq.length] = 'List the Page Name and Page Title for each community.';
							}
							if ($('##incCalendar').is(":checked")){
								var evReqFields = [];
								if($('##defaultGLAccountID').val() == '0' || $('##defaultGLAccountID').val() == '') evReqFields.push('GL Account');
								if($('##eventCategoryName').length && $.trim($('##eventCategoryName').val()).length == 0) evReqFields.push('Default Category Name');
								if($('##eventCategoryShortName').length && $.trim($('##eventCategoryShortName').val()).length == 0) evReqFields.push('Default Category Short Name');
								if($('##eventCategoryColor').length && $.trim($('##eventCategoryColor').val()).length == 0) evReqFields.push('Default Category Color');

								if(evReqFields.length)
									arrReq[arrReq.length] = 'Please provide the following information if you wish to include Calendar and Events : '+ evReqFields.join(', ') +'.';
							}
							if ($('##incDirectory').is(":checked") && ($('##SearchFieldSet').val() == '0' || $('##ResultsFieldSet').val() == '0' || $('##DetailFieldSet').val() == '0')) {
								arrReq[arrReq.length] = 'Choose a fieldset in each box if you wish to include Member Directory.';
							}
							
							if (arrReq.length > 0) {
								e.preventDefault();
								mca_showAlert('err_createapp', arrReq.join('<br/>'), true);
								$(".steps-finish-btn").prop('disabled',false);
							} else {
								mca_hideAlert('err_createapp');
								$('.steps-finish-btn').text('Saving...Please Wait').attr('disabled','disabled');
							}								
						});					
					});						
				</script>			
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
			
			<cfoutput>
            <div class="mt-4">
            	<cfif arguments.event.valueExists('mc_err_arrexistingpages') AND isArray(arguments.event.getValue('mc_err_arrexistingpages'))>
					<div id="err_createapp" class="alert alert-danger my-2">
						Following Page Name(s) are already in use.<br />
						<ul>
						<cfloop array="#arguments.event.getValue('mc_err_arrexistingpages')#" index="local.thisPageName">
							<li>#local.thisPageName#</li>
						</cfloop>
						</ul>
					</div>
				<cfelse>
					<div id="err_createapp" class="alert alert-danger my-2 d-none"></div>
				</cfif>
                <cfform action="/?#cgi.QUERY_STRING#" method="POST" name="frmCreateApp" id="frmCreateApp">
                    <cfinput type="hidden" name="lid" id="lid" value="#arguments.event.getValue('mc_siteInfo.defaultLanguageID')#">
                    <cfinput type="hidden" name="pageTemplateID" id="pageTemplateID" value="0">
                    <cfinput type="hidden" name="allowReturnAfterLogin" id="allowReturnAfterLogin" value="1">
					
					<div class="alert alert-info"> 
						List the Page Name and Page Title for each community you need to create, separated by a comma. It is recommended that Page Name and Page Title are each 30 characters or less.<br/>
						Enter each community on its own line.<br/>
						<i>Example: MyPageName,My Community Name</i>
					</div>

					<div class="form-group">
						<div class="form-label-group">
							<textarea name="comms" id="comms" class="form-control" cols="60" rows="4"></textarea>    
							<label for="comms">Communities to Create*</label>
						</div>
					</div>

					<div class="form-group">
						<div class="form-label-group">
							<select name="sectionID" class="form-control" >
                                <cfloop query="local.getSections">
                                    <option value="#local.getSections.sectionID#">#local.getSections.thePathExpanded#</option>
                                </cfloop>
                            </select> 
							<label for="sectionID">Root Page Section</label>
						</div>
					</div>

					<div class="card card-box">
						<div class="card-header bg-light"><div class="card-header--title font-weight-bold font-size-md">Sub Pages to Create in Each Community</div></div>
						<div class="card-body">
							<div class="form-check">
                                <cfinput type="checkbox" name="incAnnouncements" id="incAnnouncements" value="1" checked="true" class="form-check-input">
                                <label class="form-check-label" for="incAnnouncements">
                                    Include Announcements
                                </label>
                            </div>
                            <div class="form-check">
                                <cfinput type="checkbox" name="incCalendar" id="incCalendar" value="1" checked="true" class="form-check-input">
                                <label class="form-check-label" for="incCalendar">
                                    Include Calendar and Events
                                </label>
                            </div>
							<div class="mt-2 mb-4 ml-3 card card-box" id="incCalendarTbody">
								<div class="card-body">
									#local.strRevenueGLAcctWidget.html#

									<h6 class="mt-3 font-weight-bold">Default Category</h6>
									<div class="form-group">
										<div class="form-label-group">
											<input type="text" name="eventCategoryName" id="eventCategoryName" value="" class="form-control" autocomplete="off" maxlength="50">
											<label for="eventCategoryName">Category Name *</label>
										</div>
									</div>
									<div class="form-group">
										<div class="form-label-group">
											<input type="text" name="eventCategoryShortName" id="eventCategoryShortName" value="" class="form-control" autocomplete="off" maxlength="20">
											<label for="eventCategoryShortName">Category Short Name *</label>
										</div>
									</div>

									<div class="form-group">
										<div class="form-label-group">
											<input type="color" name="eventCategoryColor" id="eventCategoryColor" value="" class="form-control" autocomplete="off" maxlength="7" style="width:200px;">
											<label for="eventCategoryColor">Category Color * </label>
										</div>
									</div>
								</div>
                            </div>

							<div class="form-check">
                                <cfinput type="checkbox" name="incFileShare2" id="incFileShare2" value="1" checked="true" class="form-check-input">
                                <label class="form-check-label" for="incFileShare2">
                                    Include FileShare 2
                                </label>
                            </div>
                            <div class="form-check">
                                <cfinput type="checkbox" name="incLists" id="incLists" value="1" checked="true" class="form-check-input">
                                <label class="form-check-label" for="incLists">
                                    Include Lists
                                </label>
                            </div>
                            <div class="form-check">
                                <cfinput type="checkbox" name="incDirectory" id="incDirectory" value="1" checked="true" class="form-check-input">
                                <label class="form-check-label" for="incDirectory">
                                    Include Member Directory
                                </label>
                            </div>

							<div id="incDirectoryTbody" class="mt-2 ml-3 mb-4 card card-box">
								<div class="card-body">
									<div class="form-check mb-2">
										<label class="form-check-label" for="SearchFieldSet">
											<i>Member Fieldset for Directory Search:</i>
										</label>
										#local.strFieldSetDirectorySearchSelector.html#
									</div>
									<div class="form-check mb-2">
										<label class="form-check-label" for="ResultsFieldSet">
											<i>Member Fieldset for Directory Results:</i>
										</label>
										#local.strFieldSetDirectoryResultsSelector.html#
									</div>
									<div class="form-check">
										<label class="form-check-label" for="DetailFieldSet">
											<i>Member Fieldset for Directory Detail:</i>
										</label>
										#local.strFieldSetDirectoryDetailSelector.html#
									</div>
								</div>
                            </div>
							<div class="form-check">
                                <cfinput type="checkbox" name="incPhotos" id="incPhotos" value="1" checked="true" class="form-check-input">
                                <label class="form-check-label" for="incPhotos">
                                    Include Photo Gallery
                                </label>
                            </div>
                            <div class="form-check">
                                <cfinput type="checkbox" name="incVideos" id="incVideos" value="1" checked="true" class="form-check-input">
                                <label class="form-check-label" for="incVideos">
                                    Include Video Gallery
                                </label>
                            </div>
						</div>
					</div>

                    <div class="form-group text-right mt-4">
                        <button type="submit" name="btnSaveEventDetails" id="btnSaveEventDetails" class="btn btn-sm btn-primary d-none">Create Communities</button>
                    </div>
                </cfform>
            </div>			
			</cfoutput>
		<cfelse>
			<cfoutput>
			<div class="alert alert-warning">
				<h4>Unable to add #local.appInfo.applicationTypeName#</h4>
				<p>You may not add this application to your website at this time.</p>
			</div>	
			</cfoutput>
		</cfif>
	</cffunction>

</cfcomponent>