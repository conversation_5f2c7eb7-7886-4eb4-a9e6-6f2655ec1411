<cfparam name="attributes.data" default="#structNew()#">

<cfif thisTag.executionMode neq "start">
	<cfexit>
</cfif>
<cfset dataStruct = attributes.data.actionStruct>
<cfset calendarSettings = attributes.data.calendarSettings>
<cfset baseQueryString = attributes.data.baseQueryString>
<!--- common js --->
<cfinclude template="../commonBaseView.cfm">
<!--- style for tooltips --->
<style type="text/css">
.yui-tt { text-align:left; padding:4px; }
.fc-event {cursor:pointer !important;}
</style>
<cfoutput>
	<script type='text/javascript' src='/assets/common/javascript/fullCalendar/6.1.8/index.global.min.js'></script>
	<script language="javascript">	
		
		document.addEventListener('DOMContentLoaded', function () {
			var calendarEl = document.getElementById('divCalendarContainer');
			 calendar = new FullCalendar.Calendar(calendarEl, {
				initialDate: '#dataStruct.year#-#dataStruct.month#-01',
				contentHeight: 'auto',
				timeZone: 'UTC',
				initialView: 'dayGridMonth',
				headerToolbar: {
				left: '',
				center: '',
				right: ''
				},
				eventDisplay: 'block',
				events:#dataStruct.calendarJSON#,
				eventDataTransform: function(event) {
					<!--- 
						if allDay = true, fullCalendar treats end date as exclusive [ start=2025-09-01T00:00:00Z, end=2025-09-05T23:59:59Z - end date will be 2025-09-04 ]. 
						If it's an allDay event AND the end looks like 23:59:59Z - add 1 day 
					--->
					if (event.allDay && event.end) {
						let endDate = new Date(event.end);
						<!--- Detect "end of day" timestamps (like 23:59:59) --->
						if (endDate.getUTCHours() === 23 && endDate.getUTCMinutes() === 59 && endDate.getUTCSeconds() === 59) {
							<!--- Add 1 day --->
							endDate.setUTCDate(endDate.getUTCDate() + 1);
							<!--- Replace with date-only string (keeps it as all-day) --->
							event.end = endDate.toISOString().split("T")[0];
						}
					}
					return event;
				},
				eventClick: function(info) {
					gotoEvent(info.event.extendedProps.eventID,info.event.extendedProps.isSWL);
					return false;
					
				},
				eventMouseEnter: function(info) {
					/*info.el.style.backgroundColor = '##3c44b1'; */
					/* Decode HTML entities*/
					var parser = new DOMParser();
					var firstPass = parser.parseFromString(info.event.extendedProps.description, 'text/html').body.textContent;
					var finalHtml = parser.parseFromString(firstPass, 'text/html').body.innerHTML;

					Tip(finalHtml, BALLOON,true, FOLLOWMOUSE,false, STICKY,true, OFFSETX,5, CLICKCLOSE,true);
					return false;
				},
				eventMouseLeave: function(info) {
					UnTip();
					return false;
				},
				loading: function(bool) {
					if (bool) $('##divCalendarLoading').center().removeClass('d-none');
					else $('##divCalendarLoading').addClass('d-none');
				},
				eventDidMount: function(info) {
					const timeEl = info.el.querySelector('.fc-event-time');
					if (timeEl) {
						timeEl.style.whiteSpace = 'normal';
						timeEl.style.overflow = 'visible';
						timeEl.style.textOverflow = 'unset';
					}
					const titleE1 = info.el.querySelector('.fc-event-title-container');
					if (titleE1) {
						titleE1.style.whiteSpace = 'normal';
						titleE1.style.wordBreak = 'break-word';
						titleE1.style.textOverflow = 'unset';
					}

					const anchorE1 = info.el.querySelector('a');
					if (anchorE1) {
						anchorE1.style.borderWidth = '1px 5px';
					}
				},
				viewDidMount: function(viewInfo) {
					/* Called after the entire view is mounted and all events rendered*/
					allEventsRendered = true;
					
					<cfif IsJSON(dataStruct.calendarJSON)>
						MCLoader.loadJS('/assets/common/javascript/mcapps/events/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
						.then( () => {
							try {
								var calendarJSON = #dataStruct.calendarJSON#;
								triggerEventsViewItemList('#attributes.data.pageName#', calendarJSON, 'Calendar');
							} catch (error) {
								console.error("Error parsing JSON:", error.message);
							}
						});
					</cfif>
				
				}
			});

		calendar.render();
		});

	
	</script>	
<div class="container-fluid">
	<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/wz_tooltip.js"></script>
	<script type="text/javascript" src="/assets/common/javascript/tooltips/5.3.1/tip_balloon.js"></script>
	
<!--- nav bar --->
<cfinclude template="calendarNavbar.cfm">
	<div id="divCalendarLoading" class="d-none"><div class="spinner-border m-2 text-dark"></div></div>
	<div id="divCalendarContainer" class="w-100"></div>
</div>

</cfoutput>	
<!--- footer code --->
<cfsavecontent variable="footerCode">
	<cfinclude template="commonBaseViewFooter.cfm">
</cfsavecontent>
<cfoutput>#application.objCommon.minText(footerCode)#</cfoutput>