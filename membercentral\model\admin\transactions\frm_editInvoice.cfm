<cfsavecontent variable="local.invJS">
	<cfoutput>
	<script language="javascript">
		<cfif (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 and val(local.qryInvoice.invoiceID) gt 0) OR (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1 and val(local.qryInvoice.invoiceID) is 0)>
			function showMemSelector() {
				hideAlert();
				$('##divInvoiceForm').hide();
				$('##memberSelectorArea').html(mca_getLoadingHTML())
					.load('#this.link.memSelectGotoLink#&closeModal=0&mode=stream&autoClose=0&dispTitle=#URLEncodedFormat("Choose Member for Invoice Assignment")#&retFunction=selectAssignedTo&closeFunction=closeMemSelector&fldName=assignedToMemberID');
				$('##memberSelector').show();
				top.$('##btnMCModalSave').prop('disabled',true);
			}
			function selectAssignedTo(fldID,mID,mNum,mName) {
				$('##assignedToMemberID').val(mID);
				$('##assignedToMemberIDName').text(mName + ' (' + mNum + ')');
				closeMemSelector();
			}
			function closeMemSelector() {
				$('##memberSelector').hide();
				$('##divInvoiceForm').show();
				top.$('##btnMCModalSave').prop('disabled',false);
			}
		</cfif>

		function hideAlert() { $('##inverr').html('').addClass('d-none'); };
		function showAlert(msg) { $('##inverr').html(msg).removeClass('d-none'); };

		function checkInvForm() {
			hideAlert();
			top.$('##btnMCModalSave').prop('disabled',true);
			var arrReq = new Array();
			<cfif (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 and val(local.qryInvoice.invoiceID) gt 0) OR (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1 and val(local.qryInvoice.invoiceID) is 0)>
				if ($('##assignedToMemberID').val() == '0' || $('##assignedToMemberID').val().length == 0) arrReq[arrReq.length] = 'Select which member this invoice will be assigned to.';
				if ($('##invoiceProfileID').val() == '0' || $('##invoiceProfileID').val().length == 0) arrReq[arrReq.length] = 'Select which Invoice Profile to use.';
			</cfif>

			if (arrReq.length > 0) {
				var msg = '<b>The following requires your attention:</b><br/>';
				for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
				showAlert(msg);
				top.$('##btnMCModalSave').prop('disabled',false);
				return false;
			}

			top.$('##btnMCModalSave').remove();
			return true;
		}
		<cfif local.showProcessingFees>
			function updateProcessingFees() {
				let invDue = roundDueAmt(#local.invDue#);
				let payProcessFee = 0;
				let processFeePercent = 0;
				let processFeeMsg = '';
				let processFeeLabel = '';

				if ($('##crdAssoc').length && $('##crdAssoc').val() != 0) {
					let payProfileOptGrp = $('##crdAssoc option:selected').parent('optgroup');
					if (payProfileOptGrp.length) {
						payProcessFee = Number(payProfileOptGrp.data('enableprocessingfeedonation'));
						processFeePercent = Number(payProfileOptGrp.data('processfeedonationfeepercent'));
						processFeeMsg = payProfileOptGrp.data('processfeedonationfemsg');
						processFeeLabel = payProfileOptGrp.data('processfeelabel');
					}
				}

				if (invDue > 0 && payProcessFee == 1 && processFeePercent > 0) {
					$('##processFeeMsg').html(processFeeMsg);
					let processingFee = roundDueAmt(parseFloat(invDue * (processFeePercent / 100)));
					$('##processFeeLabel').html(processFeeLabel.replace('{{AMOUNT}}','$' + formatCurrency(processingFee).replace('.00','')));
					$('##processFeePercentDsp').html(formatCurrency(processFeePercent).replace('.00',''));
					$('##divProcessFeeWrapper').removeClass('d-none');
				} else {
					$('##processFeeMsg').html('');
					$('##processFeeLabel').html('');
					$('##processFeePercentDsp').html(0);
					$('##payProcessFee').prop('checked',false);
					$('##divProcessFeeWrapper').addClass('d-none');
				}
			}
			function roundDueAmt(amt) {
				return Number(formatCurrency(amt).replace(/\$|\,/g,''));
			}
		</cfif>
		function onChangeInvMPProfile() {
			if (! $('##crdAssoc').length) return false;

			$('##crdAssoc').find('optgroup').hide();

			$('input[name="assocPayProfile"]:checked').each(function() {
				let mpID = $(this).val();
				$('##crdAssoc').find('optgroup[data-mpprofileid='+mpID+']').show();
			});
		 }
		
		$(function() {
			<cfif (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 and val(local.qryInvoice.invoiceID) gt 0) OR (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1 and val(local.qryInvoice.invoiceID) is 0)>
				mca_setupMultipleDatePickerFields($('##frmInvoice'),'dateControl');
				mca_setupCalendarIcons('frmInvoice');
			</cfif>
			<cfif local.showProcessingFees AND NOT val(local.qryInvoice.payProcessFee)>
				updateProcessingFees();
			</cfif>
			onChangeInvMPProfile();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.invJS)#">

<cfsavecontent variable="local.openDots">
	<cfoutput>
	&bull; Transactions can be added/removed at any time.<br/>
	&bull; Payments cannot be made against this invoice.
	</cfoutput>
</cfsavecontent>
<cfsavecontent variable="local.pendingDots">
	<cfoutput>
	&bull; Transactions cannot be added or removed.<br/>
	&bull; Payments cannot be made against this invoice.
	</cfoutput>
</cfsavecontent>
<cfsavecontent variable="local.closedDots">
	<cfoutput>
	&bull; Transactions cannot be added or removed.<br/>
	&bull; Payments can be made against this invoice.
	</cfoutput>
</cfsavecontent>

<cfoutput>
<div id="memberSelector" class="p-3" style="display:none;">
	<cfif NOT val(local.qryInvoice.invoiceID)>
		<div class="mb-4">
			<a href="##" onclick="return closeMemSelector();">
				<i class="fa-solid fa-chevrons-left"></i> Return to Create Invoice
			</a>
		</div>
	</cfif>
	<div id="memberSelectorArea"></div>
</div>

<form name="frmInvoice" id="frmInvoice" class="p-3" action="#this.link.updateInvoice#" method="post" onsubmit="return checkInvForm()" autocomplete="off">
<input type="hidden" name="vid" id="vid" value="#val(local.qryInvoice.invoiceID)#">
<input type="hidden" name="assignedToMemberID" id="assignedToMemberID" value="#val(local.qryInvoice.memberid)#">

<div id="divInvoiceForm">

<!--- hidden submit triggered from parent --->
<button type="submit" class="d-none"></button>

<div id="inverr" class="alert alert-danger d-none mb-2"></div>

<div class="form-row">
	<div class="col">
		<div class="form-label-group mb-2">
			<input type="text" name="readOnlyDateCreated" value="#dateformat(local.qryInvoice.dateCreated,'m/d/yyyy')#" class="form-control" readonly>
			<label for="readOnlyDateCreated">Date Created</label>
		</div>
	</div>
	<div class="col">
		<div class="form-label-group mb-2">
			<cfif (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 and val(local.qryInvoice.invoiceID) gt 0) OR (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1 and val(local.qryInvoice.invoiceID) is 0)>
				<div class="input-group dateFieldHolder">
					<input type="text" name="dateBilled" id="dateBilled" value="#dateformat(local.qryInvoice.dateBilled,'m/d/yyyy')#" class="form-control dateControl" autocomplete="off" onfocus="hideAlert();">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="dateBilled"><i class="fa-solid fa-calendar"></i></span>
						<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('dateBilled');"><i class="fa-solid fa-circle-xmark"></i></a></span>
					</div>
					<label for="dateBilled">Date Billed</label>
				</div>
			<cfelse>
				<input type="text" name="readOnlyDateBilled" value="#dateformat(local.qryInvoice.dateBilled,'m/d/yyyy')#" class="form-control" readonly>
				<label for="readOnlyDateBilled">Date Billed</label>
			</cfif>
		</div>
	</div>
	<div class="col">
		<div class="form-label-group mb-2">
			<cfif (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 and val(local.qryInvoice.invoiceID) gt 0) OR (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1 and val(local.qryInvoice.invoiceID) is 0)>
				<div class="input-group dateFieldHolder">
					<input type="text" name="dateDue" id="dateDue" value="#dateformat(local.qryInvoice.dateDue,'m/d/yyyy')#" class="form-control dateControl" autocomplete="off" onfocus="hideAlert();">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="dateDue"><i class="fa-solid fa-calendar"></i></span>
						<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('dateDue');"><i class="fa-solid fa-circle-xmark"></i></a></span>
					</div>
					<label for="dateDue">Date Due</label>
				</div>
			<cfelse>
				<input type="text" name="readOnlyDateBilled" value="#dateformat(local.qryInvoice.dateDue,'m/d/yyyy')#" class="form-control" readonly>
				<label for="readOnlyDateBilled">Date Due</label>
			</cfif>
		</div>
	</div>
</div>
<div class="form-label-group mb-2">
	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1 and val(local.qryInvoice.invoiceID) is 0>
		<select name="invoiceProfileID" id="invoiceProfileID" class="custom-select">
			<cfif local.qryInvoiceProfiles.recordcount gt 1>
				<option value="0"></option>
			</cfif>
			<cfloop query="local.qryInvoiceProfiles">
				<option value="#local.qryInvoiceProfiles.profileID#" <cfif local.qryInvoiceProfiles.profileID eq local.qryInvoice.invoiceProfileID>selected</cfif>>#local.qryInvoiceProfiles.profileName#</option>
			</cfloop>
		</select>
	<cfelse>
		<select name="invoiceProfileID" id="invoiceProfileID" class="custom-select">
			<option value="#local.qryInvoice.invoiceProfileID#">#local.qryInvoice.invoiceprofilename#</option>
		</select>
	</cfif>
	<label for="invoiceProfileID">Invoice Profile</label>
</div>
<div class="row no-gutters mt-3">
	<label class="col-sm-3 font-weight-bold">Assigned To:</label>
	<div class="col-sm-9">
		<div id="assignedToMemberIDName">#encodeForHTML(local.qryInvoice.assignedTo)#</div>
		<cfif (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 and val(local.qryInvoice.invoiceID) gt 0) OR (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1 and val(local.qryInvoice.invoiceID) is 0)>
			<div id="assignedToMemberIDSelect" class="mt-1"><a href="javascript:showMemSelector()">Select Member</a></div>
		</cfif>
	</div>
</div>

<div class="section pt-3">
	<div class="card card-box shadow-none">
		<div class="card-title pl-2 pt-2">
			<i class="fa-regular fa-square-poll-horizontal mr-1"></i> Invoice Status
		</div>
		<div class="card-body py-0">
			<cfif val(local.qryInvoice.invoiceID) gt 0>
				<div>This invoice is <strong>#local.qryInvoice.invoiceStatus#</strong>.</div>
				<cfswitch expression="#local.qryInvoice.invoiceStatus#">
					<cfcase value="Open">
						<div class="form-check mt-1">
							<input type="radio" name="statusID" id="statusID_1" value="1" class="form-check-input" checked>
							<label for="statusID_1" class="form-check-label">Keep status as <strong>Open</strong>.</label>
							<div class="form-text">#local.openDots#</div>
						</div>

						<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceClose') is 1>
							<div class="form-check mt-1">
								<input type="radio" name="statusID" id="statusID_3" value="3" class="form-check-input">
								<label for="statusID_3" class="form-check-label">Change status to <strong>Closed</strong>.</label>
								<div class="form-text">#local.closedDots#</div>
							</div>
						<cfelse>
							<div class="form-check mt-1">
								<input type="radio" name="statusID" id="statusID_3" value="3" class="form-check-input" disabled>
								<label for="statusID_3" class="form-check-label text-muted">Change status to <strong>Closed</strong>.</label>
								<div class="form-text">#local.closedDots#</div>
								<div class="text-danger font-weight-bold font-size-sm mt-1">(You do not have the necessary permissions to close an invoice.)</div>
							</div>
						</cfif>
					</cfcase>
					<cfcase value="Pending">
						<div class="form-check mt-1">
							<input type="radio" name="statusID" id="statusID_2" value="2" class="form-check-input" checked>
							<label for="statusID_2" class="form-check-label">Keep status as <strong>Pending</strong>.</label>
							<div class="form-text">#local.pendingDots#</div>
						</div>

						<cfif arguments.event.getValue('mc_admintoolInfo.myRights.invoiceClose') is 1>
							<div class="form-check mt-1">
								<input type="radio" name="statusID" id="statusID_3" value="3" class="form-check-input">
								<label for="statusID_3" class="form-check-label">Change status to <strong>Closed</strong>.</label>
								<div class="form-text">#local.closedDots#</div>
							</div>
						<cfelse>
							<div class="form-check mt-1">
								<input type="radio" name="statusID" id="statusID_3" value="3" class="form-check-input" disabled>
								<label for="statusID_3" class="form-check-label">Change status to <strong>Closed</strong>.</label>
								<div class="form-text">#local.closedDots#</div>
								<div class="text-danger font-weight-bold font-size-sm mt-1">(You do not have the necessary permissions to close an invoice.)</div>
							</div>
						</cfif>
					</cfcase>
					<cfcase value="Closed,Delinquent" delimiters=",">
						#local.closedDots#
						<input type="hidden" name="statusID" id="statusID" value="#local.qryInvoice.statusID#">
					</cfcase>
				</cfswitch>
			<cfelse>
				This invoice will be <strong>Open</strong>.
				<div>#local.openDots#</div>
			</cfif>
		</div>
	</div>
</div>

<div class="section pt-3">
	<div class="card card-box shadow-none">
		<div class="card-title pl-2 pt-2">
			<i class="fa-regular fa-cash-register mr-1"></i> Allowed Payment Profiles
		</div>
		<div class="card-body py-0">
			<div class="small pb-2">When no specific payment profiles are selected, this invoice can be paid by any payment profile.</div>
			#createObject("component","model.admin.common.modules.paymentProfileSelector.paymentProfileSelector").getPaymentProfileSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), 
				selectorID="assocPayProfile", selectedPaymentProfileIDList=local.selectedInvMPProfileIDs, fieldLabel="Allowed Payment Profiles", colCount=2, readOnly=NOT local.allowUpdateCOF, allowOffline=true, allowPayLater=true,
				onChangeFuncName="onChangeInvMPProfile", lockedProfileIDList=local.lockedMPProfileIDs).html#
		</div>
	</div>
</div>

<cfif local.offerCOF and ((arguments.event.getValue('mc_admintoolInfo.myRights.invoiceEdit') is 1 and val(local.qryInvoice.invoiceID) gt 0) OR (arguments.event.getValue('mc_admintoolInfo.myRights.invoiceCreate') is 1 and val(local.qryInvoice.invoiceID) is 0))>
	<div class="section pt-3">
		<div class="card card-box shadow-none">
			<div class="card-title pl-2 pt-2">
				<i class="fa-regular fa-credit-card-front mr-1"></i> Auto Pay This Invoice
			</div>
			<div class="card-body py-0">

				<cfif NOT local.allowUpdateCOF AND len(local.qryInvoiceItemInfo.generatedBy)>
					<div class="alert alert-info">
						This invoice is associated with #local.qryInvoiceItemInfo.generatedByApp# <b>#local.qryInvoiceItemInfo.generatedBy#</b>.<br/>
						You'll need to modify any associated payment there.
					</div>
				</cfif>

				<div class="form-label-group">
					<select name="crdAssoc" id="crdAssoc" class="custom-select"<cfif local.showProcessingFees> onchange="updateProcessingFees();"</cfif><cfif NOT local.allowUpdateCOF> disabled</cfif>>
						<option value="0" <cfif val(local.qryInvoice.payProfileID) eq 0>selected</cfif>>&nbsp;</option>
						<cfoutput query="local.qryPayProfiles" group="profileName">
							<optgroup label="#local.qryPayProfiles.profileName#" data-mpprofileid="#local.qryPayProfiles.profileID#"<cfif local.showProcessingFees> data-enableprocessingfeedonation="#local.qryPayProfiles.enableProcessingFeeDonation#" data-processfeedonationfeepercent="#val(local.qryPayProfiles.processFeeDonationFeePercent)#" data-processfeedonationfemsg="#encodeForHTMLAttribute(local.qryPayProfiles.processFeeDonationFEMsg)#" data-processfeelabel="#encodeForHTMLAttribute(local.qryPayProfiles.processFeeOtherPaymentsFELabel)#"</cfif> style="display:none;">
							<cfoutput>
							<option value="#local.qryPayProfiles.profileID#-#local.qryPayProfiles.payProfileID#" data-profileid="#local.qryPayProfiles.profileID#" <cfif val(local.qryInvoice.merchantProfileID) EQ local.qryPayProfiles.profileID AND val(local.qryInvoice.payProfileID) eq local.qryPayProfiles.payProfileID>selected</cfif>>#local.qryPayProfiles.detail#</option>
							</cfoutput>
							</optgroup>
						</cfoutput>
					</select>
					<label for="crdAssoc">Automatically Pay Invoice Using Selected Pay Method on Date Due:</label>
				</div>

				<!--- Processing Fee --->
				<cfif local.showProcessingFees>
					<div id="divProcessFeeWrapper" class="mt-3 mb-2<cfif NOT val(local.qryInvoice.payProcessFee)> d-none</cfif>">
						<div id="processFeeMsg" class="mb-1">#local.processFeeMsg#</div>
						<div class="custom-control custom-checkbox">
							<input type="checkbox" class="custom-control-input" name="payProcessFee" id="payProcessFee" value="1" autocomplete="off"<cfif val(local.qryInvoice.payProcessFee)> checked</cfif><cfif NOT local.allowUpdateCOF> disabled</cfif>>
							<label class="custom-control-label" id="processFeeLabel" for="payProcessFee">#local.processFeeLabel#</label>
						</div>
						<div class="custom-control text-dim small mt-1">
							* Estimate based on paying <span id="processFeePercentDsp">#val(local.qryInvoice.processFeePercent)#</span>% of the current amount due (#dollarFormat(local.invDue)#).
						</div>
					</div>
				</cfif>
			</div>
		</div>
	</div>
</cfif>

</form>
</div>
</cfoutput>