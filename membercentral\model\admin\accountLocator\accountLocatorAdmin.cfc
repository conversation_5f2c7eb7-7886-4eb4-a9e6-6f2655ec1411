<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// use resourceID of the site for security -------------------------------------------------- ::
			this.siteResourceID = arguments.event.getValue('mc_siteInfo.siteSiteResourceID');
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(
				siteResourceID=this.siteResourceID, 
				memberID=session.cfcuser.memberdata.memberID, 
				siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
			// build quick links ------------------------------------------------------------------------ ::
			this.link.list 				= buildCurrentLink(arguments.event,"list");
			this.link.save				= buildCurrentLink(arguments.event,"save");
			this.link.addProfessionalLicense				= buildCurrentLink(arguments.event,"addProfessionalLicense")  & "&mode=direct";
			this.link.saveProfessionalLicense				= buildCurrentLink(arguments.event,"saveProfessionalLicense");
			
			// create MemberFieldSets objects
			this.objAccountLocator 			= CreateObject("component","model.system.user.accountLocater");
			// method to run ---------------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
	
	<!--- List Account Locator: Initial Landing Page--->	
	<cffunction name="list" access="public" output="false" returntype="struct" hint="Find Fields List Page">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.searchFieldSet = this.objAccountLocator.getLocatorFieldsetID(arguments.event.getValue('mc_siteInfo.siteID'),'search')>
		<cfset local.resultsFieldSet = this.objAccountLocator.getLocatorFieldsetID(arguments.event.getValue('mc_siteInfo.siteID'),'results')>
		<cfset local.newAcctFieldSet = this.objAccountLocator.getLocatorFieldsetID(arguments.event.getValue('mc_siteInfo.siteID'),'newacct')>
		<cfset local.memberSettingsLink = buildLinkToTool(toolType='MemberSettingsAdmin',mca_ta='edit')>
		<cfset local.siteResourceID = this.objAccountLocator.getToolSiteResourceID(arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.qryCurrentProfessionalLicenses = this.objAccountLocator.getCurrentProfessionalLicenses(siteResourceID=local.siteResourceID)>
		<cfset local.qrySettings = getAccountLocatorSettings(arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.strSearchFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=local.siteID,selectorID="SearchFieldSet", selectedValue=local.searchFieldSet, fieldLabel="Search Field Set")/>
		<cfset local.strResultsFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=local.siteID,selectorID="resultsFieldSet", selectedValue=local.resultsFieldSet, fieldLabel="Results Field Set")/>
		<cfset local.strNewAccthFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=local.siteID,selectorID="newAcctFieldSet", selectedValue=local.newAcctFieldSet, fieldLabel="New Account Field Set")/>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_list.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="save" access="public" output="false" returntype="void" hint="Save Account Locator Field Sets">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.saveAccountLocator">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY			
				
				DECLARE @siteResourceID int, @siteID int;
				SET @siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">;	
				
				BEGIN TRAN;
					SELECT @siteResourceID = siteResourceID
					from dbo.sites
					where siteID = @siteID;

					UPDATE dbo.ams_memberFieldUsage
					SET fieldSetID = <cfqueryparam value="#arguments.event.getValue('SearchFieldSet',0)#" cfsqltype="CF_SQL_INTEGER">
					WHERE siteResourceID = @siteResourceID
					AND area = 'search';

					UPDATE dbo.ams_memberFieldUsage
					SET fieldSetID = <cfqueryparam value="#arguments.event.getValue('resultsFieldSet',0)#" cfsqltype="CF_SQL_INTEGER">
					WHERE siteResourceID = @siteResourceID
					AND area = 'results';
					
					UPDATE dbo.ams_memberFieldUsage
					SET fieldSetID = <cfqueryparam value="#arguments.event.getValue('newAcctFieldSet',0)#" cfsqltype="CF_SQL_INTEGER">
					WHERE siteResourceID = @siteResourceID
					AND area = 'newacct';			

					UPDATE dbo.ams_accountLocator
					SET showNAForm = <cfqueryparam value="#arguments.event.getValue('showNAForm')#" cfsqltype="CF_SQL_BIT">,
					defaultSearchOption = <cfqueryparam value="#arguments.event.getValue('defaultSearchOption')#" cfsqltype="CF_SQL_VARCHAR">
					where siteID = @siteID;	

					EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('noResultsContentID')#">,
 						@languageID=1, @isHTML=1, @contentTitle='', @contentDesc='', 
 						@rawContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('noResultsContent')#">,
 						@memberID=#session.cfcUser.memberdata.memberID#;
					EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('oneResultContentID')#">,
 						@languageID=1, @isHTML=1, @contentTitle='', @contentDesc='', 
 						@rawContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('oneResultContent')#">,
 						@memberID=#session.cfcUser.memberdata.memberID#;
					EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('multipleResultContentID')#">,
 						@languageID=1, @isHTML=1, @contentTitle='', @contentDesc='', 
 						@rawContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('multipleResultContent')#">,
 						@memberID=#session.cfcUser.memberdata.memberID#;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
			
		<cflocation url="#this.link.list#&msg=1" addtoken="no">
	</cffunction>
	
	<cffunction name="addProfessionalLicense" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.siteResourceID = arguments.event.getValue('siteResourceID')>	

		<cfset local.data.qryCurrentProfessionalLicenses = this.objAccountLocator.getCurrentProfessionalLicenses(siteResourceID=local.siteResourceID)>
		<cfset local.data.qryProfessionalLicenses = this.objAccountLocator.getProfessionalLicenses(orgID=arguments.event.getValue('mc_siteInfo.orgID'),currentProfessionalLicenses=valueList(local.data.qryCurrentProfessionalLicenses.PLTypeID))>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_professionalLicense.cfm">
			</cfoutput>
		</cfsavecontent>	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveProfessionalLicense" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.saveFieldSets">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY	
				
				DECLARE @siteResourceID int, @PLTypeID int, @recordExists int;
				
				set @siteResourceID = <cfqueryparam value="#arguments.event.getValue('siteResourceID')#" cfsqltype="CF_SQL_INTEGER">;
				set @PLTypeID = <cfqueryparam value="#arguments.event.getValue('PLTypeID')#" cfsqltype="CF_SQL_INTEGER">;

				if len(@PLTypeID) > 0 begin
					select @recordExists = count(*)
					from dbo.ams_memberFieldUsageProfessionalLicenses
					where siteResourceID = @siteResourceID
					and PLTypeID =  @PLTypeID;
				
					if @recordExists = 0
						insert into ams_memberFieldUsageProfessionalLicenses (PLTypeID, siteResourceID)
						values(@PLTypeID, @siteResourceID);
				end			
				
				select @PLTypeID as id, plName as name
				from ams_memberProfessionalLicenseTypes 
				where PLTypeID = @PLTypeID

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
			
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="JavaScript">
				top.addProfLicence({id:#local.saveFieldSets.id#,name:'#local.saveFieldSets.name#'});
				top.MCModalUtils.hideModal();
			</script>			
			</cfoutput>
		</cfsavecontent>	
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	
	
	<cffunction name="deleteProfessionalLicense" access="public" output="false" returntype="struct">
		<cfargument name="PLTID" type="numeric">
		<cfargument name="sRID" type="numeric">

		<cfset var local = structNew() />

		<cfquery name="local.deleteProfessionalLicense" datasource="#application.dsn.membercentral.dsn#">
			delete from dbo.ams_memberFieldUsageProfessionalLicenses
			where siteResourceID = <cfqueryparam value="#arguments.sRID#" cfsqltype="cf_sql_integer">
			and PLTypeID = <cfqueryparam value="#arguments.PLTID#" cfsqltype="cf_sql_integer">		
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAccountLocatorSettings"  access="public" output="false" returntype="query" hint="I return accountlocator admin settings">
		<cfargument name="siteID" type="numeric" required="true">
		<cfset var local = StructNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySettings">
			select acct.noResultsContentID, acct.oneResultContentID, acct.multipleResultContentID, acct.showNAForm, acct.defaultSearchOption,
				nores.rawContent as noResultsContent, oneres.rawContent as oneResultContent, multres.rawContent as multipleResultContent
			from dbo.ams_accountLocator as acct
			cross apply dbo.fn_getContent(acct.noResultsContentID,1) as nores
			cross apply dbo.fn_getContent(acct.oneResultContentID,1) as oneres
			cross apply dbo.fn_getContent(acct.multipleResultContentID,1) as multres
			where acct.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfreturn local.qrySettings>
	</cffunction>

	<cffunction name="getCurrentProfLicenses" access="public" output="yes" returntype="struct">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfset var local = structNew()>
		<cfset local.qryCurrentProfLicenses = "">
		<cfset local.data = structNew()>
		<cfset local.data['success'] = false>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrentProfLicenses">
			select lt.PLTypeID, lt.PLName
			from dbo.ams_memberProfessionalLicenseTypes lt
			inner join dbo.ams_memberFieldUsageProfessionalLicenses pl on pl.PLTypeID = lt.PLTypeID
			where pl.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfset local.data['arrLicenses'] = arrayNew(1)>
		<cfloop query="local.qryCurrentProfLicenses">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['id'] = local.qryCurrentProfLicenses.PLTypeID>
			<cfset local.tmp['name'] = local.qryCurrentProfLicenses.PLName>
			<cfset arrayAppend(local.data['arrLicenses'], local.tmp)>
		</cfloop>
		<cfset local.data['success'] = true>
		<cfreturn local.data>
	</cffunction>

</cfcomponent>

