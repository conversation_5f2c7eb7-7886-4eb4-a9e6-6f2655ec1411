<cfset local.strData = attributes.data>
<cfset local.productList = []>
<cfif  StructKeyExists(local.strData,"cart") AND local.strData.cart.recordCount>
	<cfloop query="local.strData.cart">
		<cfset arrayAppend(local.productList, {
			"quantity":local.strData.cart.quantity,
			"cartItemID": local.strData.cart.cartItemID,
			"productID": local.strData.cart.productID,
			"productName": encodeForHTML(local.strData.cart.contentTitle),
			"rate": local.strData.cart.rate,
			"categoryName": local.strData.cart.categoryName
		})>
	</cfloop>	
</cfif>	
<cfset local.objStore = CreateObject('component', 'model.admin.store.store')>

<cfinclude template="topbar.cfm">
<cfinclude template="categoryStart.cfm">

<cfsavecontent variable="local.carthead">
	<link rel="stylesheet" href="/assets/common/javascript/tablesaw/tablesaw.css">	
	<script src="/assets/common/javascript/tablesaw/tablesaw.js"></script>
	<style type="text/css">
		#tblCart tr:first-child td { border-top:0px; }
		#tblBottom td { border-top:0px; }
		.h4nospace { font-family:"Trebuchet MS", Arial, Helvetica, Geneva, sans-serif; font-size:1.4em; line-height:1.1em; color:#0E568D; }
		.h6nospace { font-family:"Trebuchet MS", Arial, Helvetica, Geneva, sans-serif; font-size:1.2em; line-height:1.1em; color:#0E568D; }
		div#actionswrapper { width:260px; margin:6px 0px 10px 20px; }
		div#actionswrapper div.sidebox { border:1px solid #DEDEDE; }
		div#actionswrapper div.sidebox div.sideboxtitle { padding:4px; background-color:#DEDEDE; font-weight:bold; }
		div#actionswrapper div.sidebox div.sideboxbody { padding:4px; }
		.alert { background:#fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid #f00; border-bottom:2px solid #f00; }
		.BB { border-bottom:1px solid #333; }
		div.issuePanel { font-size:13px; padding-top:10px; }
		div.issuePanel table th, div.issuePanel table td { padding-top:10px; }
		span.showOverSoldItems, span.hideOverSoldItems { cursor:pointer; }

		/* mobile first for custom block */
		.tablesawCustomBlock {float:left;margin-left:30%;}
		@media (min-width: 40em) {
			.tablesawCustomBlock {float:left;margin-left:inherit;}
		}
		@media (max-width: 39.9375em) {
			.tablesaw td {border-top-width: 0px;}
		}
	</style>
	<cfoutput>
	<script language="javascript">
		var rate = #local.strData.qryItemsTotal.totalRate#;
		<cfif len(local.strData.shippingOption)>
			rate +=#local.strData.qryItemsTotal.shippingOptions[local.strData.shippingOption].PerShipment + local.strData.qryItemsTotal.shippingOptions[local.strData.shippingOption].totalItems#;
		</cfif>
		<cfset local.productJSON = SerializeJSON(local.productList)>
		<cfif  StructKeyExists(local.strData,"qryItemsTotal")>
		$(function() {
			
			<cfif IsJSON(local.productJSON)>
				MCLoader.loadJS('/assets/common/javascript/mcapps/store/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {
					try {					
						<cfif application.mcCacheManager.sessionValueExists(keyname='cartItemID' ) AND application.mcCacheManager.sessionGetValue(keyname='cartItemID') >
							<cfset local.addedCartItem = application.mcCacheManager.sessionGetValue(keyname='cartItemID', defaultValue=0)>
								triggerAddItem(#local.addedCartItem#,0);
							<cfset application.mcCacheManager.sessionDeleteValue(keyname='cartItemID')>	
						<cfelseif application.mcCacheManager.sessionValueExists(keyname='updateProductList' ) AND arrayLen(application.mcCacheManager.sessionGetValue(keyname='updateProductList')) >
							<cfset local.updateProductList =application.mcCacheManager.sessionGetValue(keyname='updateProductList')>
							<cfloop from="1" to="#arrayLen(local.updateProductList)#" index="local.thisItem" >
                                <cfif local.updateProductList[local.thisItem].strAction EQ "add">
									triggerAddItem(#local.updateProductList[local.thisItem].cartItemID#,#local.updateProductList[local.thisItem].updatedQuantity#);
								<cfelseif local.updateProductList[local.thisItem].strAction EQ "remove">
									triggerRemoveItem(#local.updateProductList[local.thisItem].cartItemID#,#local.updateProductList[local.thisItem].updatedQuantity#);
								</cfif>
                            </cfloop>
							<cfset application.mcCacheManager.sessionDeleteValue(keyname='updateProductList')>;	
						</cfif>
						triggerProductEventsCart(#local.productJSON#,rate, 'view');	
						
						
					} catch (error) {
						console.error("Error parsing JSON:", error.message);
					}
				});
			</cfif>
		});
		</cfif>
		function triggerAddItem(itemid, updatedQuantity){
			var addedProduct = #local.productJSON#.reduce((acc, product) => {
				if (product.cartItemID == itemid) {
					if(updatedQuantity > 0){
						product.quantity = updatedQuantity;
					}
						
					acc.push(product);
				} 
				return acc;
			}, []);
			
			triggerProductEventsCart(addedProduct,0, 'add');	
		}
		function triggerRemoveItem(itemid, updatedQuantity){
			var availableProducts = #local.productJSON#.reduce((acc, product) => {
					if (product.cartItemID == itemid) {
						if(updatedQuantity > 0){
							product.quantity = updatedQuantity;
						}
						acc.push(product);
					} 
					return acc;
				}, []);
				
			triggerProductEventsCart(availableProducts,0, 'remove');	
		}
		function removeItem(itemid) {
			triggerRemoveItem(itemid,0);	
			window.location.href = '#local.strData.mainurl#&sa=remove&ItemID=' + itemid;			
		}
		function proceedToCheckOut(){
			triggerProceedToCheckOutEvent(#local.productJSON#,rate);
			self.location.href='#local.strData.mainurl#&sa=Checkout';
		}
		function toggleOverSoldItemPanel() {
			$('div##overSoldItemAccordion div.issuePanel').toggle();
			$('div##overSoldItemAccordion span.showOverSoldItems').toggle();
			$('div##overSoldItemAccordion span.hideOverSoldItems').toggle();
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.carthead)#">


<cfoutput>
	<cfif local.strData.cart.recordCount>
		<cfform action="#local.strData.mainurl#&sa=UpdateCart" METHOD="POST" id="cartform" name="cartform">
		<div class="container-fluid">
			<div class="row-fluid">
				<div class="span10">
					<div class="lead">
						Your cart contains the following item<cfif local.strData.cart.recordCount is not 1>s</cfif>: 
					</div>

					<cfif local.strData.keyExists("oversoldItems")>#local.strData.oversoldItems#</cfif>
				</div>
				<div class="span2">
					<div>
						<div><b>Purchaser:</b></div>
						<div style="margin-left:20px;margin-top:6px;">
							<cfif local.strData.qryPurchaser.hasPrefix is 1 and len(local.strData.qryPurchaser.prefix)>#local.strData.qryPurchaser.prefix# </cfif>#local.strData.qryPurchaser.firstname#<cfif local.strData.qryPurchaser.hasMiddleName is 1 and len(local.strData.qryPurchaser.middlename)> #local.strData.qryPurchaser.middlename#</cfif> #local.strData.qryPurchaser.lastname# <cfif local.strData.qryPurchaser.hasSuffix is 1 and len(local.strData.qryPurchaser.suffix)>#local.strData.qryPurchaser.suffix#</cfif> <cfif local.strData.qryPurchaser.hasprofessionalsuffix is 1 and len(local.strData.qryPurchaser.professionalsuffix)>#local.strData.qryPurchaser.professionalsuffix#</cfif><br/>
							<cfif len(local.strData.qryPurchaser.company)>#htmleditformat(local.strData.qryPurchaser.company)#</cfif>
						</div>
						<br />
					</div>
				</div>
			</div>
		</div>

		<div class="container-fluid">
			<div class="row-fluid">
				<div class="span12">
				<table cellpadding="4" cellspacing="0" id="tblCart" class="table table-striped tablesaw" style="width:100%;" data-mode="stack">
					<thead>
						<tr> 
							<th class="BB"><b>Item</b></th>
							<th class="BB" align="right"><b>Qty</b></th>
							<th class="BB" align="right"><b>Per Item</b></th>
							<th class="BB" align="right"><b>Total</b></th>
							<th width="50" class="BB">&nbsp;</th>
						</tr>
					</thead>
					<cfloop query="local.strData.cart">
						<cfset local.qryDocuments = local.objStore.getStoreDocuments(formatID=local.strData.cart.formatID)>
						<tr valign="top">
							<td><div class="tablesawCustomBlock">
									<a href="#local.strData.mainurl#&sa=ViewDetails&ItemID=#local.strData.cart.ItemID#"><strong>#local.strData.cart.contentTitle#</strong></a>
									<div style="margin-left:15px;">Format: #local.strData.cart.formatName#</div>
									<cfif local.strData.cart.numAffirmationsIncluded gt 0>
										<div style="margin-left:15px;">#local.strData.cart.numAffirmationsIncluded# affirmations included</div>
									</cfif>
									<cfif local.qryDocuments.recordcount>
										<div style="margin-left:15px;">Documents electronically deliverable</div>
									</cfif>
								</div>
							</td>
							<td align="right">
								<cfinput type="text" class="input-small" name="Quantity_#local.strData.cart.cartItemID#" id="Quantity_#local.strData.cart.cartItemID#" value="#local.strData.cart.quantity#" required="True" message="You must supply a valid quantity for #replace(local.strData.cart.contentTitle,chr(34),'','all')# - #replace(local.strData.cart.formatName,chr(34),'','all')#" validate="integer" size="4">
								<cfinput type="hidden" name="QuantityPrev_#local.strData.cart.cartItemID#" id="QuantityPrev_#local.strData.cart.cartItemID#" value="#local.strData.cart.quantity#">
							</td>
							<cfif local.strData.cart.rateOverride eq "">
								<td align="right">#dollarFormat(local.strData.cart.rate)#<cfif local.strData.showCurrencyType is 1> #local.strData.defaultCurrencyType#</cfif></td>
								<td align="right">#DollarFormat(local.strData.cart.quantity * val(local.strData.cart.rate))#<cfif local.strData.showCurrencyType is 1> #local.strData.defaultCurrencyType#</cfif></td>
							<cfelse>
								<td align="right">#dollarFormat(local.strData.cart.rateOverride)#<cfif local.strData.showCurrencyType is 1> #local.strData.defaultCurrencyType#</cfif></td>
								<td align="right">#DollarFormat(local.strData.cart.quantity * val(local.strData.cart.rateOverride))#<cfif local.strData.showCurrencyType is 1> #local.strData.defaultCurrencyType#</cfif></td>
							</cfif>	
							<td>
                                <a class="btn btn-danger btn-mini" href="##" onclick="removeItem(#local.strData.cart.cartItemID#);return false;">Remove</a>
							</td>
						</tr>
					</cfloop>
				</table>
				</div>
			</div>		
			
			<div class="row-fluid">&nbsp;</div>		
			
			<div class="row-fluid">
				<div class="span5">
					<cfset local.shippingKeys = StructKeyList(local.strData.qryItemsTotal.shippingOptions)>	
					<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>			
					<cfif StructCount(local.strData.qryItemsTotal.shippingOptions) GT 1>
						<div id="actionswrapper">
							<div class="sidebox">
								<div class="sideboxtitle">Shipping Options</div>
								<div class="sideboxbody">
									<cfloop list="#local.shippingKeys#" index="shipKey">
										<cfif len(local.strData.shippingOption) eq 0>
											<cfset local.strData.shippingOption = shipKey>
											<cfset local.store[local.strData.appInstanceID].shippingOption = shipKey>
										</cfif>
										<input type="radio" name="shippingOption" value="#shipKey#" onClick="document.cartform.submit();" <cfif local.strData.shippingOption eq shipKey>checked</cfif> />
										<b>#local.strData.qryItemsTotal.shippingOptions[shipKey].name# - #dollarFormat(local.strData.qryItemsTotal.shippingOptions[shipKey].PerShipment + local.strData.qryItemsTotal.shippingOptions[shipKey].totalItems)#<cfif local.strData.showCurrencyType is 1> #local.strData.defaultCurrencyType#</cfif></b><br />
									</cfloop>
								</div>
							</div>
						</div>
					<cfelse>
						<cfloop list="#local.shippingKeys#" index="shipKey">
							<cfset local.strData.shippingOption = shipKey>
							<cfset local.store[local.strData.appInstanceID].shippingOption = shipKey>
							<cfinput type="hidden" name="shippingOption" id="shippingOption" value="#shipKey#">
						</cfloop>
					</cfif>
					<cfset application.mcCacheManager.sessionSetValue(keyname='store', value=local.store)>	
				</div>
				<div class="span7">
					<table cellpadding="4" cellspacing="0" id="tblBottom" class="table" >
						<tr> 
							<td valign="top" rowspan="5">
							</td>
							<td></td>
							<td align="right" style="border-top:1px solid ##ddd;"><b>Sub Total:</b></td>
							<td align="right"  style="border-top:1px solid ##ddd;" nowrap><b>#dollarFormat(local.strData.qryItemsTotal.totalRate)#<cfif local.strData.showCurrencyType is 1> #local.strData.defaultCurrencyType#</cfif></b></td>
							<td></td>
						</tr>
						<tr> 
							<td></td>
							<td align="right" nowrap><b>Shipping & Handling:</b></td>
							<td align="right" nowrap>
								<b>#dollarFormat(local.strData.qryItemsTotal.shippingOptions[local.strData.shippingOption].PerShipment + local.strData.qryItemsTotal.shippingOptions[local.strData.shippingOption].totalItems)#<cfif local.strData.showCurrencyType is 1> #local.strData.defaultCurrencyType#</cfif></b>
							</td>
							<td></td>
						</tr>
						<tr> 
							<td></td>
							<td align="right" style="border-top:1px solid ##ddd;"><b>Total:</b></td>
							<td align="right" style="border-top:1px solid ##ddd;" nowrap><b>#dollarFormat(local.strData.qryItemsTotal.totalRate+(local.strData.qryItemsTotal.shippingOptions[local.strData.shippingOption].PerShipment + local.strData.qryItemsTotal.shippingOptions[local.strData.shippingOption].totalItems))#<cfif local.strData.showCurrencyType is 1> #local.strData.defaultCurrencyType#</cfif></b></td>
							<td></td>
						</tr>
						<tr> 
							<td></td>
							<td align="right" colspan="2">(+ tax determined at checkout)</td>
							<td></td>
						</tr>
						<tr> 
							<td colspan="3">
								<br/>
								<div class="pull-right">
									<button class="btn" type="submit" name="btnUpdateCart" style="margin-bottom:10px;"><strong>Update Cart</strong></button>
									&nbsp;&nbsp;
									<button class="btn" type="button" onClick="proceedToCheckOut();" style="margin-bottom:10px;"><strong>Proceed to Checkout</strong></button>
								</div>
							</td>
							<td>&nbsp;</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
		</cfform>
	<cfelse>
		<div style="margin-top:3px;">
			<div class="text-error"><strong>Your cart is empty.</strong></div><br/><br/>
		</div>
		<cfif local.strData.keyExists("oversoldItems")>#local.strData.oversoldItems#</cfif>
	</cfif>
</cfoutput>

<cfinclude template="categoryEnd.cfm">