<cfset local.strData = attributes.data>
<cfset local.productList = []>
<cfif StructKeyExists(local.strData,"qryProductsInCategory") AND local.strData.qryProductsInCategory.recordCount >
	<cfloop query="local.strData.qryProductsInCategory">
		<cfset arrayAppend(local.productList, {
			"productID": local.strData.qryProductsInCategory.productID,
			"productName": local.strData.qryProductsInCategory.contentTitle,
			"categoryName": local.strData.qryProductsInCategory.CategoryName
		})>
	</cfloop>
</cfif>
<cfinclude template="topbar.cfm">
<cfinclude template="categoryStart.cfm">


<!--- go right to the product if it's the only one in the category --->
<cfif local.strData.qryProductsInCategory.totalcount IS 1 and not arrayLen(local.strData.arrSubCategories)>
	<cflocation addtoken="no" url="#local.strData.mainurl#&sa=ViewDetails&ItemID=#local.strData.qryProductsInCategory.ItemID#&cat=#local.strData.cat#">
</cfif>
<cfsavecontent variable="local.storeViewCategoryJS">
<cfoutput>
	<script language="javascript">
		$(function() {				
			<cfset local.productsJSON = SerializeJSON(local.productList)>
			<cfif IsJSON(local.productsJSON)>
				MCLoader.loadJS('/assets/common/javascript/mcapps/store/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {
					try {
						triggerProductsViewItemList('#local.strData.pageName#',#local.productsJSON#, 'Store');
					} catch (error) {
						console.error("Error parsing JSON:", error.message);
					}
				});
			</cfif>
		});
	</script>
</cfoutput>	
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.storeViewCategoryJS)#">	
<div class="tsAppHeading"><cfoutput>#local.strData.strCategory.CategoryName#</cfoutput></div>
<br/>
<cfif not local.strData.qryProductsInCategory.recordCount or not val(local.strData.qryProductsInCategory.totalcount)>
	<div class="tsAppBodyText">
		<cfif arrayLen(local.strData.arrAllCategories)>
			<b>Please select a category with items on the left.</b>
			<p style="padding-top:10px;">
				Categories with items for purchase have a count displayed to the right of the category.
			</p>
		<cfelse>
			<p>This category doesn't have any products.</p>
		</cfif>
	</div>
<cfelse>
	<div class="tsAppBodyText">
		<b>Please select your product below. <cfif arrayLen(local.strData.arrAllCategories)>You may also select from the list of categories with items on the left.</cfif></b>
	</div>
	<table class="tsAppBodyText" width="100%" cellspacing="0" cellpadding="4">
    <tr>
	    <td class="tsAppBB" width="*"><b>Product</b></td>
	</tr>
	<cfset local.rowNum = 0>
	<cfoutput query="local.strData.qryProductsInCategory" group="itemID">
		<cfset local.rowNum = local.rowNum + 1>
		<tr valign="top" <cfif local.rowNum mod 2 is 0>class="rowcolor0"</cfif>>
			<td>
				<a style="font-weight:bold;" href="#local.strData.mainurl#&sa=ViewDetails&ItemID=#local.strData.qryProductsInCategory.itemID#&cat=#local.strData.cat#">#htmleditformat(local.strData.qryProductsInCategory.contenttitle)#</a>
				<cfif LEN(local.strData.qryProductsInCategory.categoryList)>
					<div  style="padding-top:3px;margin-left:10px;"><b>Also Appears In:</b> #local.strData.qryProductsInCategory.categoryList#</div>
				</cfif>
				<div style="padding-top:3px;margin-left:10px;">
					<cfif local.strData.storeInfo.ShowProductID>
						Product ID: #local.strData.qryProductsInCategory.productID#<br/>
					</cfif>
					#REReplaceNoCase(local.strData.qryProductsInCategory.rawcontent,"<[^>]*>"," ","ALL")#<cfif len(local.strData.qryProductsInCategory.rawcontent) gt 200>...</cfif>
				</div>
			</td>
		</tr>
	</cfoutput>
	</table>

	<cfoutput>
	<table border="0" align="center" cellpadding="6" cellspacing="0">
	<tr class="tsAppBodyText">
		<cfif local.strData.start is not 1>
			<cfif local.strData.start GTE local.strData.disp>
				<cfset local.prev = local.strData.disp>
				<cfset local.prevrec = local.strData.start - local.strData.disp>
			<cfelse>
				<cfset local.prev = local.strData.start - 1>
				<cfset local.prevrec = 1>
			</cfif>
			<td>
				<a href="#local.strData.mainurl#&sa=ViewCategory&cat=#local.strData.strCategory.CategoryID#&start=#local.prevrec#">Previous #local.prev#</a>
			</td>
		</cfif>

		<cfif local.strData.disp LT val(local.strData.qryProductsInCategory.totalcount) + 1>
			<cfset local.PageCount = 1>
			<td>
				<cfloop index="local.Pages" from="1" to="#local.strData.qryProductsInCategory.totalcount#" step="#local.strData.disp#">
					<a href = "#local.strData.mainurl#&sa=ViewCategory&cat=#local.strData.strCategory.CategoryID#&start=#local.Pages#">#local.PageCount#</a>
					<cfset local.PageCount = local.PageCount + 1>
				</cfloop>
			</td>
		</cfif>

		<cfif local.strData.end LT local.strData.qryProductsInCategory.totalcount>
			<cfif local.strData.start + (local.strData.disp * 2) GTE local.strData.qryProductsInCategory.totalcount>
				<cfset local.next = local.strData.qryProductsInCategory.totalcount - local.strData.start - local.strData.disp + 1>
			<cfelse>
				<cfset local.next = local.strData.disp>
			</cfif>
			<td>
				<a href="#local.strData.mainurl#&sa=ViewCategory&cat=#local.strData.strCategory.CategoryID#&start=#local.strData.start + local.strData.disp#"> Next #local.next#</a>
			</td>
		</cfif>
	</tr>
	</table>
	</cfoutput>
</cfif>

<cfinclude template="categoryEnd.cfm">