<cfset local.strData = attributes.data>

<cfset local.productList = []>
<cfif  StructKeyExists(local.strData,"qryProduct") AND local.strData.qryProduct.recordCount>
	<cfset arrayAppend(local.productList, {
		"productID": local.strData.qryProduct.productID,
		"productName": encodeForHTML(local.strData.qryProduct.contentTitle),
		"categoryName": local.strData.qryProduct.categoryName
	})>
</cfif>

<cfinclude template="topbar.cfm">
<cfinclude template="categoryStart.cfm">

	<cfif not local.strData.qryProduct.recordCount>
		<div class="tsAppHeading">Sorry, that product is not available.</div>
	<cfelse>
		<cfsavecontent variable="local.prodhead">
			<style type="text/css">
				span.prod_heading { font-weight:bold; background-color:#DEDEDE; border:1px solid #ccc; padding:2px; }
				div.prod_infoarea { margin-bottom:20px; border-left:1px solid #DEDEDE; }
				div#prodTitle { margin-top:6px; }
				div#actionswrapper { float:right; width:200px; margin:6px 0px 10px 20px; }
				div#actionswrapper div.sidebox { border:1px solid #DEDEDE; }
				div#actionswrapper div.sidebox div.sideboxtitle { padding:4px; background-color:#DEDEDE; font-weight:bold; }
				div#actionswrapper div.sidebox div.sideboxbody { padding:4px; }
			</style>
			<cfoutput>
			<script language="javascript">
				<cfset local.productJSON = SerializeJSON(local.productList)>
				<cfif  StructKeyExists(local.strData,"qryProduct") AND local.strData.qryProduct.recordCount>
				$(function() {
					<cfif IsJSON(local.productJSON)>
						MCLoader.loadJS('/assets/common/javascript/mcapps/store/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
						.then( () => {
							try {
								triggerProductViewItem(#local.productJSON#);
							} catch (error) {
								console.error("Error parsing JSON:", error.message);
							}
						});
					</cfif>
				});
				</cfif>
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#application.objCommon.minText(local.prodhead)#">
	
		<cfoutput>
		<div id="prodTitle" class="tsAppHeading">#htmleditformat(local.strData.qryProduct.contenttitle)#</div>
		<br/><br/>	
		</cfoutput>
		<div id="actionswrapper" class="tsAppBodyText">
			<cfif ListFind(valueList(local.strData.cart.itemid),local.strData.qryProduct.itemId)>
				<div style="padding:3px;border:1px solid #f00;">
					<div class="tsAppBodyTextImportant">Reminder:</div>
					<cfoutput>This item is currently in your <a href="#local.strData.mainurl#&sa=view">shopping cart</a>.</cfoutput>
				</div>
				<br/>
			</cfif>
	
			<div class="sidebox">
				<div class="sideboxtitle">Purchase</div>
				<div class="sideboxbody">
					<div align="center">
						<cfif local.strData.offerAddToCart>
							<cfform method="post" action="/?pg=store">
								<cfinput type="hidden" name="sa"  id="sa" Value="regStoreUser">
								<cfinput type="hidden" name="Quantity"  id="Quantity" value="1">
								<cfinput type="hidden" name="ItemID"  id="ItemID" Value="#local.strData.qryProduct.ItemID#">
								<cfinput type="hidden" name="cat"  id="cat" value="#local.strData.cat#">
								<cfoutput><button type="submit" name="btnAddToCart" class="tsAppBodyButton">#local.strData.addToCartBtnLabel#</button></cfoutput>
							</cfform>
						<cfelse>
							<cfoutput><button type="button" name="btnSoldOut" class="tsAppBodyButton" disabled>#local.strData.addToCartBtnLabel#</button></cfoutput>
						</cfif>
					</div>
					<cfif ListFind(valueList(local.strData.qryPurchasedProducts.productItemID),local.strData.qryProduct.itemId)>
						<br/>
						<div align="center"><i>Note: You have previously purchased this product.</i></div>
					</cfif>
				</div>
			</div>
	
			<br/>
			<div class="sidebox">
				<div class="sideboxtitle">Pricing</div>
				<div class="sideboxbody">
					<table cellpadding="0" cellspacing="1">
					<cfoutput query="local.strData.qryProductPricing" group="formatID">
						<tr>
							<td class="tsAppBodyText" colspan="3">
								<b>#local.strData.qryProductPricing.name#</b>
								<cfif local.strData.storeInfo.OfferAffirmations is 1 and local.strData.qryProductPricing.OfferAffirmations is 1 and local.strData.qryProductPricing.quantity gt 0 and local.strData.qryProductPricing.isAffirmation is 0>
									<br/>(includes #local.strData.qryProductPricing.quantity# affirmation<cfif local.strData.qryProductPricing.quantity is not 1>s</cfif>)
								</cfif>
							</td>
						</tr>
						<cfoutput>
							<tr valign="top">
								<td class="tsAppBodyText" align="right">
									<cfif local.strData.qryProductPricing.rate is 0>FREE<cfelse>#dollarformat(local.strData.qryProductPricing.rate)#<cfif len(local.strData.displayedCurrencyType)> #local.strData.displayedCurrencyType#</cfif></cfif>
								</td>
								<td width="5"></td>
								<td class="tsAppBodyText">#local.strData.qryProductPricing.rateName#</td>
							</tr>
						</cfoutput>
						<tr style="line-height:8px;"><td>&nbsp;</td></tr>
					</cfoutput>
					</table>
				</div>
			</div>
	
		</div>
	
		<cfif len(local.strData.qryProduct.rawcontent)>
			<div class="tsAppBodyText prod_infoarea">
				<span class="prod_heading">Details</span><br/><br/>
				<cfoutput>
				<div style="margin:0 20px 6px 20px;">
					<table cellpadding="2" cellspacing="0">
					<tr>
						<td valign="top" class="tsAppBodyText">
							<cfif len(local.strData.qryProduct.productDate)>
								<b>Date:</b> #DateFormat(local.strData.qryProduct.productDate,"mm/dd/yyyy")#
								<br /><br />
							</cfif>
							#local.strData.qryProduct.rawcontent#
						</td>
					</tr>
					</table>
				</div>
				</cfoutput>
			</div>	
		</cfif>
	
		<cfif len(trim(local.strData.electronicDeliveryInfo))>
			<div class="tsAppBodyText prod_infoarea">
				<span class="prod_heading">Electronic File Delivery</span><br/><br/>
				<div style="margin:0 20px 6px 20px;">
					<cfoutput>#local.strData.electronicDeliveryInfo#</cfoutput>
				</div>	
			</div>	
		</cfif>
	
		<cfif local.strData.storeInfo.displayCredit is 1>
			<cfloop collection="#local.strData.strFormatCredits#" item="local.formatID">
				<cfif local.strData.strFormatCredits[local.formatID].creditInfo.creditCount gt 0>
					<div class="tsAppBodyText prod_infoarea">
						<span class="prod_heading"><cfoutput>Credit for #local.strData.strFormatCredits[local.formatID].formatName#</cfoutput></span><br/><br/>
						<div style="margin:0 20px 6px 20px;">
							<cfoutput>#local.strData.strFormatCredits[local.formatID].creditInfo.detail#</cfoutput>
						</div>	
					</div>	
				</cfif>
			</cfloop>
		</cfif>

	</cfif>

<cfinclude template="categoryEnd.cfm">