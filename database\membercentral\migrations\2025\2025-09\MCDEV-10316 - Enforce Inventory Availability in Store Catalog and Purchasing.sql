USE membercentral
GO

ALTER PROC dbo.store_getAvailableProductFormatsForCart
@storeID int, 
@itemID int,
@memberID int,
@qualifyFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @orgID int, @groupPrintID int;
	DECLARE @tmpProductFormats TABLE (formatID int PRIMARY KEY);
	DECLARE @tmpProductFormatInventoryCount TABLE (formatID int PRIMARY KEY, formatPurchasedCount int);

	select @siteID = siteID
	from dbo.store
	where storeID = @storeID;

	select @orgID = orgID, @groupPrintID = groupPrintID
	from dbo.ams_members
	where memberID = @memberID;

	INSERT INTO @tmpProductFormats (formatID)
	SELECT Formatid
	FROM dbo.store_ProductFormats
	WHERE itemid = @itemID
	AND [status] = 'A';

	INSERT INTO @tmpProductFormatInventoryCount (formatID, formatPurchasedCount)
	SELECT od.formatID, SUM(od.Quantity)
	FROM @tmpProductFormats AS tmp
	INNER JOIN dbo.store_orderDetails AS od ON od.formatID = tmp.formatID
	GROUP BY od.formatID;

	SELECT DISTINCT spf.formatid, fr.rateid, spf.Name, fr.rate, spf.formatOrder,
		ISNULL(spf.inventory,0) AS inventory, ISNULL(tmp.formatPurchasedCount,0) AS formatPurchasedCount,
		CASE WHEN spf.inventory IS NULL THEN 1
			WHEN ISNULL(spf.inventory,0) > 0 AND spf.inventory > ISNULL(tmp.formatPurchasedCount,0) THEN 1
			ELSE 0 END AS isAvailable,
		CASE WHEN s.offerAffirmations = 1 AND spf.offerAffirmations = 1 THEN spf.quantity
		ELSE 0 END AS numAffirmationsIncluded
	from dbo.store_Products as sp
	inner join dbo.store as s on s.siteID = @siteID 
		and s.storeID = @storeID
		and sp.showAvailable = 1
	inner join dbo.store_ProductFormats as spf on sp.itemid = spf.itemid 
		and spf.status = 'A'
	inner join dbo.store_rates as sr on spf.formatid = sr.formatid 
	inner join dbo.cms_siteResources as c on c.siteID = @siteID 
		and c.siteResourceID = sr.siteResourceID 
		and c.siteResourceStatusID = 1
	inner join dbo.cms_siteResourceRights as srr on c.siteResourceID = srr.resourceID 
		and srr.siteID = @siteID
	inner join dbo.ams_groups as g on g.orgID = @orgID 
		and g.groupid = srr.groupid
	inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
		and srfrp.siteresourceID = c.siteResourceID
		and srfrp.functionID = @qualifyFID
	inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
		and gprp.rightPrintID = srfrp.rightPrintID
		and gprp.groupPrintID = @groupPrintID
	left outer join @tmpProductFormatInventoryCount as tmp on tmp.formatID = spf.formatID
	cross apply dbo.fn_store_getBestFormatRate(spf.formatid,@qualifyFID,@memberID,@siteID) as fr
	where sp.storeID = @storeID
	and spf.itemid = @itemID
	and spf.status = 'A'
	and (sr.startDate is null or CAST( CONVERT( char(8), sr.startDate, 112) AS datetime) <= CAST( CONVERT( char(8), getDate(), 112) as datetime))
	and (sr.endDate is null or DATEADD (d , 1, CAST( CONVERT( char(8), sr.endDate, 112) AS datetime) ) > getdate())
	order by spf.formatOrder, spf.Name;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.store_getCartData
@storeID int, 
@orderNumber varchar(40),
@shippingid int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int;
	DECLARE @tmpProductFormats TABLE (formatID int PRIMARY KEY);
	DECLARE @tmpProductFormatInventoryCount TABLE (formatID int PRIMARY KEY, formatPurchasedCount int);

	select @orgID = ss.orgID 
	from dbo.store as s
	inner join dbo.sites as ss on ss.siteID = s.siteID
	where s.storeID = @storeID;

	INSERT INTO @tmpProductFormats (formatID)
	SELECT DISTINCT formatID
	FROM dbo.store_CartItems
	WHERE storeID = @storeID
	AND orderNumber = @orderNumber;

	INSERT INTO @tmpProductFormatInventoryCount (formatID, formatPurchasedCount)
	SELECT od.formatID, SUM(od.Quantity)
	FROM @tmpProductFormats AS tmp
	INNER JOIN dbo.store_orderDetails AS od ON od.formatID = tmp.formatID
	GROUP BY od.formatID;

	SELECT ci.cartItemID, ci.rateChanged, r.rateid, f.formatID, f.isAffirmation, p.ItemID, gl.rateGLAccountID, gl.shippingGLAccountID,
		case when store.offerAffirmations = 1 and f.offerAffirmations = 1 then f.quantity else 0 end as numAffirmationsIncluded,
		r.rate, r.rateName, f.name AS formatName, p.productID, prodcontent.contentTitle, ci.quantity, 

		(select top 1 rateOverride 
		from dbo.store_ProductRatesOverride spo 
		where spo.rateid = r.rateid
		and ((spo.startDate <= getDate() and spo.endDate >= getDate()) or spo.startDate is null or spo.endDate is null)
		order by rateOverride) as rateOverride,

		isNULL((select max(perItem) from dbo.store_ProductShipping as spp where spp.rateid = r.rateid and ps.shippingid = spp.shippingid),0) as perItem,

		(select isNULL( max(perItem),0) * ci.quantity
		from dbo.store_ProductShipping as spp  
		where spp.rateid = r.rateid 
		and ps.shippingid = spp.shippingid
		) as ItemShipping,				

		(select isNULL( max(perShipment), 0)
		from dbo.store_ProductShipping as spp  
		where spp.rateid = r.rateid
		and ps.shippingid = spp.shippingid 
		) as perShipment,

		(select (ci.quantity * isNULL( max(perItem),0)) + isNULL( max(perShipment), 0)
		from dbo.store_ProductShipping as spp  
		where spp.rateid = r.rateid 
		and ps.shippingid = spp.shippingid
		) as shipmentAmt,	

		(select top 1 shippingName
		from dbo.store_shippingMethods as sm
		inner join dbo.store_ProductShipping as spp on spp.shippingid = sm.shippingid and ps.shippingid = spp.shippingid	
		where spp.rateid = r.rateid 
		) as shippingName,
		
		case when exists (select 1 from dbo.store_CartItemSaleDetails where cartItemID = ci.cartItemID and saleID is not null) then 1 else 0 end as hasSaleAssociated, 
		case when exists (select 1 from dbo.store_CartItemSaleDetails where cartItemID = ci.cartItemID and saleID is not null and saleAmountChanged = 1) then 1 else 0 end as saleAmountChanged, 

		isNULL((select sum(cache_amountAfterAdjustment) 
				from dbo.store_CartItemSaleDetails as cisd
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.saleID = cisd.saleID
				inner join dbo.tr_applications as tra on tra.orgID = @orgID and ts.transactionID = tra.transactionID  
					and tra.applicationTypeID = 14
					and tra.itemType in ('Product','ProductPerItem','ProductPerShipment')
				and tra.status = 'A'
				where cisd.cartItemID = ci.cartItemID)
			,0) as cache_amountAfterAdjustment,
		
		isNULL((select sum(t.amount) 
				from dbo.store_CartItemSaleDetails as cisd
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.saleID = cisd.saleID
					and cisd.cartItemID = ci.cartItemID
				inner join dbo.tr_applications as tra on tra.orgID = @orgID and ts.transactionID = tra.transactionID  
					and tra.applicationTypeID = 14
					and tra.itemType = 'Product'
					and tra.status = 'A'
				inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = 1 and tr.appliedToTransactionID = tra.transactionID
				inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = tr.transactionID and t.statusID = 1
				inner join dbo.tr_transactionDiscounts as d on d.orgID = @orgID and d.transactionID = t.transactionID and d.isActive = 1
					and d.itemType = 'StoreOrder')
			,0) as itemDiscountExcTax,
		(
			select top 1 c.categoryID
			from dbo.store_categories c
			inner join dbo.store_ProductCategoryLinks pcl on pcl.categoryID = c.categoryID
				and pcl.itemID = p.itemID 
			where c.storeID = @storeID
			order by c.categoryID
		) as categoryID,
		(
			select top 1 c.CategoryName
			from dbo.store_categories c
			inner join dbo.store_ProductCategoryLinks pcl on pcl.categoryID = c.categoryID
				and pcl.itemID = p.itemID 
			where c.storeID = @storeID
			order by c.categoryID
		) as categoryName,

		ISNULL(f.inventory,0) AS inventory,
		CASE WHEN o.OrderCompleted = 0 AND ISNULL(f.inventory,0) > 0 AND ci.Quantity + ISNULL(tmp.formatPurchasedCount,0) > f.inventory 
			THEN CASE WHEN ISNULL(tmp.formatPurchasedCount,0) < f.inventory THEN ci.Quantity + ISNULL(tmp.formatPurchasedCount,0) - f.inventory
						ELSE ci.Quantity
				END
			ELSE 0 END AS oversoldQty

	FROM dbo.store_CartItems AS ci
	INNER JOIN dbo.store_Products AS p ON p.storeID = @storeID and ci.productItemID = p.ItemID AND p.showAvailable = 1
	INNER JOIN dbo.store on store.storeID = p.storeID
	INNER JOIN dbo.store_ProductFormats AS f ON ci.formatID = f.formatID
	INNER JOIN dbo.store_rates AS r ON ci.rateid = r.rateid AND ci.formatID = r.formatID
	inner join dbo.cms_contentLanguages as prodcontent on prodcontent.contentID = p.productContentID and prodcontent.languageID = 1
	CROSS APPLY dbo.fn_store_getRateGLAccountID(@storeID, r.rateid) as gl
	LEFT OUTER JOIN store_productShipping ps ON ps.rateid = r.rateid and ps.shippingid = @shippingid
	LEFT OUTER JOIN @tmpProductFormatInventoryCount AS tmp ON tmp.formatID = ci.formatID
	LEFT OUTER JOIN dbo.store_Orders AS o ON o.storeID = @storeID
		AND o.orderNumber = ci.orderNumber
	WHERE ci.storeID = @storeID
	AND ci.orderNumber = @orderNumber
	order by prodcontent.contentTitle, p.itemID, f.formatOrder;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO