use trialsmith
GO

-- delete all existing schedules
delete from dbo.memberCentralBillingBadgePrinting;
GO

ALTER PROC dbo.site_createDefaultBillingFeeSchedules
@orgcode varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @effectiveDate date = DATEFROMPARTS(year(getdate()), month(getdate()), 1);

	BEGIN TRAN;
		insert into dbo.memberCentralBillingHosting (orgCode, effectiveDate, adminRate, adminRateInc, 
			adminRateAddtl, monthlyMin, monthlyFeeAMS, monthlyFeeWeb, noFee)
		values (@orgcode, @effectiveDate, 450, 3, 150, 0, 0, 450, 0);

		insert into dbo.memberCentralBillingFundraising (orgCode, effectiveDate, monthlyFee, noFee)
		values (@orgcode, @effectiveDate, 150, 0);

		insert into dbo.memberCentralBillingLRIS (orgCode, effectiveDate, monthlyFee, noFee)
		values (@orgcode, @effectiveDate, 150, 0);

		insert into dbo.memberCentralBillingPublications (orgCode, effectiveDate, monthlyFee, noFee)
		values (@orgcode, @effectiveDate, 150, 0);

		insert into dbo.memberCentralBillingSolicitations (orgCode, effectiveDate, monthlyFee, noFee)
		values (@orgcode, @effectiveDate, 150, 0);

		insert into dbo.memberCentralBillingAPIAccess (orgCode, effectiveDate, monthlyFee, noFee, noofCallIncFee, overageFee, noofCallsInOverageFee)
		values (@orgcode, @effectiveDate, 150, 0, 0, 0, 0);

		insert into dbo.memberCentralBillingDistrict (orgCode, effectiveDate, billingRate, noFee)
		values (@orgcode, @effectiveDate, 0.05, 0);

		insert into dbo.memberCentralBillingEmailBlast (orgCode, effectiveDate, monthlyFee, monthlyComp, monthlyPerAdminComp, billingRate, includeOtherApps, noFee)
		values (@orgcode, @effectiveDate, 0, 0, 0, 0, 0, 1);

		insert into dbo.memberCentralBillingAddressUpdate (orgCode, effectiveDate, monthlyFee, billingRate, noFee)
		values (@orgcode, @effectiveDate, 0, 0.05, 0);

		insert into dbo.memberCentralBillingDedicatedServiceMgr (orgCode, effectiveDate, monthlyFee, noFee)
		values (@orgcode, @effectiveDate, 0, 1);

		insert into dbo.memberCentralBillingEmailHosting (orgCode, effectiveDate, monthlyFee, noFee)
		values (@orgcode, @effectiveDate, 0, 1);

		insert into dbo.memberCentralBillingPrivateListServerDomain (orgCode, effectiveDate, monthlyFee, noFee)
		values (@orgcode, @effectiveDate, 80, 0);

		insert into dbo.memberCentralBillingPrivateEmailSendingDomain (orgCode, effectiveDate, monthlyFee, noFee)
		values (@orgcode, @effectiveDate, 80, 0);

		insert into dbo.memberCentralBillingEntPlatformSecurity (orgCode, effectiveDate, monthlyFee, noFee)
		values (@orgcode, @effectiveDate, 50, 0);

		insert into dbo.memberCentralBillingBadgePrinting (orgCode, effectiveDate, monthlyFee, noFee, noofCallIncFee, overageFee, noofCallsInOverageFee)
		values (@orgcode, @effectiveDate, 0, 0, 0, 0.10, 1);
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

-- add new fee schedule starting Sep 2025
insert into dbo.memberCentralBillingBadgePrinting (orgCode, effectiveDate, monthlyFee, noFee, noofCallIncFee, overageFee, noofCallsInOverageFee)
SELECT orgcode, '9/1/2025', 0, 0, 0, 0.10, 1
from dbo.memberCentralBilling;
GO
