<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script language="javascript">
		function validateContributionAppInstanceForm() {
			mca_hideAlert('err_page');
			var thisForm = document.forms["frmContributionAppInstanceEdit"];
			var contributionAppInstanceName = thisForm['contributionAppInstanceName'].value || '';
			if (contributionAppInstanceName.length == 0) {
				mca_showAlert('err_page', 'Enter the Name of the Contribution Page', true);
				return false;
			}

			top.$('##btnUpdate').prop('disabled',true);
			thisForm.submit();
			return true;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<div id="err_page" class="alert alert-danger mb-2 mt-2 d-none"></div>
<div class="row-fluid p-3">
	<form id="frmContributionAppInstanceEdit" name="frmContributionAppInstanceEdit" method="post" action="#local.updateContributionAppInstanceLink#" onsubmit="return validateContributionAppInstanceForm();">
		<input type="hidden" id="aID" name="aID" value="#local.qryContributionAppInstance.applicationInstanceID#">
		
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="contributionAppInstanceName" id="contributionAppInstanceName" class="form-control" autocomplete="off" value="#local.qryContributionAppInstance.applicationInstanceName#"> 
					<label for="contributionAppInstanceName">Name of Contribution Page <span class="text-danger">*</span></label>
				</div>
			</div>
		</div>
		<button type="submit" class="btn btn-sm btn-primary d-none"></button>
	</form>
</div>
</cfoutput>