
<cfset local.storeAction = attributes.event.getValue('sa','')>

<cfoutput>
	<style type="text/css">
		.accordion-heading .accordion-toggle { display:inline-block!important; padding:8px 5px!important; }
	</style>
	<!--- Avoid displaying cart toolbar and category menu twice --->
	<cfif local.strData.showCategories eq true>

		<cfif local.strData.isRootCategory>
			<cfset local.categoryLabel = "Categories">
		<cfelse>
			<cfset local.categoryLabel = "Subcategories">
		</cfif>

		<cfif listFindNoCase("ViewCategory", local.storeAction) and (not local.strData.isRootCategory and structCount(local.strData.strParentCategory))>
			<div onclick="document.location.href='/?pg=store&sa=ViewCategory&Cat=#local.strData.strParentCategory.categoryID#';" class="well well-small" style="margin-bottom: 10px;">
				<strong><i class="icon-chevron-left"></i>#ucase(local.strData.strParentCategory.categoryName)# <cfif local.strData.strParentCategory.itemCount gt 0>(#local.strData.strParentCategory.itemCount#)</cfif></strong>
			</div>
		<cfelseif listFindNoCase("ViewCategory", local.storeAction) and not local.strData.isRootCategory>
			<div onclick="document.location.href='/?pg=store';" class="well well-small" style="margin-bottom: 10px;">
				<strong><i class="icon-chevron-left"></i>RETURN TO STORE HOME</strong>
			</div>
		<cfelseif listFindNoCase("ViewDetails", local.storeAction) and local.strData.strCategory.itemCount gt 1>
			<div onclick="document.location.href='/?pg=store&sa=ViewCategory&Cat=#local.strData.strCategory.categoryID#';" class="well well-small" style="margin-bottom: 10px;">
				<strong><i class="icon-chevron-left"></i>#ucase(local.strData.strCategory.categoryName)# <cfif local.strData.strCategory.itemCount gt 0>(#local.strData.strCategory.itemCount#)</cfif></strong>
			</div>
		<cfelseif listFindNoCase("ViewDetails", local.storeAction) and local.strData.strCategory.itemCount eq 1 and structCount(local.strData.strParentCategory)>
			<div onclick="document.location.href='/?pg=store&sa=ViewCategory&Cat=#local.strData.strParentCategory.categoryID#';" class="well well-small" style="margin-bottom: 10px;">
				<strong><i class="icon-chevron-left"></i>#ucase(local.strData.strParentCategory.categoryName)# <cfif local.strData.strParentCategory.itemCount gt 0>(#local.strData.strParentCategory.itemCount#)</cfif></strong>
			</div>
		<cfelseif listFindNoCase("ViewDetails", local.storeAction) and local.strData.strCategory.itemCount eq 1>
			<div onclick="document.location.href='/?pg=store';" class="well well-small" style="margin-bottom: 10px;">
				<strong><i class="icon-chevron-left"></i>RETURN TO STORE HOME</strong>
			</div>
		</cfif>




		<cfif not len(local.storeAction) or listFindNoCase("ViewCategory", local.storeAction)>
			<div class="alert alert-info" style="margin-bottom: 10px;">
				<strong>
					<cfif not local.strData.isRootCategory>
						#ucase(local.strData.strCategory.categoryName)#
					<cfelse>
						STORE HOME
					</cfif>
				</strong>
				<cfif arrayLen(local.strData.arrSubCategories)>
					<br>
					<em>#local.categoryLabel# listed below</em>
				</cfif>
			</div>
		</cfif>


		<cfif not len(local.storeAction) or listFindNoCase("ViewCategory", local.storeAction) and arrayLen(local.strData.arrSubCategories)>
			<table class="table table-hover table-striped">
				<tbody id="subCategoriesListContainerFirstFew">
					<cfloop index="local.x" from="1" to="#min(4,arrayLen(local.strData.arrSubCategories))#">
						<tr id="category-#local.strData.arrSubCategories[local.x].categoryID#" data-listname="category-#local.strData.arrSubCategories[local.x].categoryID#" onclick="document.location.href='/?pg=store&sa=ViewCategory&Cat=#local.strData.arrSubCategories[local.x].categoryID#';" style="cursor:pointer;">
							<td class="span12">
								<i class="pull-right icon-chevron-right"></i>
								<span><strong>#ucase(local.strData.arrSubCategories[local.x].categoryName)# <cfif local.strData.arrSubCategories[local.x].itemCount gt 0>(#local.strData.arrSubCategories[local.x].itemCount#)</cfif></strong></span><br />
							</td>
						</tr>
					</cfloop>
				</tbody>

				<cfif not len(local.storeAction) or ((arrayLen(local.strData.arrSubCategories) gt 4) and structKeyExists(local.strData,"qryProductsInCategory") and local.strData.qryProductsInCategory.recordcount gt 0)>
					<cfset local.subCategoriesListContainerTheRestClass = "hidden-phone"/>
					<tbody id="subCategoriesListContainerShowmore" class="visible-phone">
						<tr class="info" id="showMoreLink" onclick="$('##subCategoriesListContainerShowmore').removeClass('visible-phone');$('##subCategoriesListContainerShowmore').hide();$('##subCategoriesListContainerTheRest').removeClass('#local.subCategoriesListContainerTheRestClass#');">
							<td class="span12">
								<div class="text-center">
									<i class="pull-left icon-chevron-down"></i>
									<i class="pull-right icon-chevron-down"></i>
									<strong>SHOW ALL #arrayLen(local.strData.arrSubCategories)# #ucase(local.categoryLabel)#</strong>
								</div>
							</td>
						</tr>
					</tbody>
				<cfelse>
					<cfset local.subCategoriesListContainerTheRestClass = ""/>
				</cfif> 

				<tbody id="subCategoriesListContainerTheRest" class="#local.subCategoriesListContainerTheRestClass#">
					<cfif arrayLen(local.strData.arrSubCategories) gte 5>
						<cfloop index="local.x" from="5" to="#arrayLen(local.strData.arrSubCategories)#">
							<tr id="category-#local.strData.arrSubCategories[local.x].categoryID#" data-listname="category-#local.strData.arrSubCategories[local.x].categoryID#" onclick="document.location.href='/?pg=store&sa=ViewCategory&Cat=#local.strData.arrSubCategories[local.x].categoryID#';">
								<td class="span12">
									<i class="pull-right icon-chevron-right"></i>
									<span><strong>#ucase(local.strData.arrSubCategories[local.x].categoryName)# <cfif local.strData.arrSubCategories[local.x].itemCount gt 0>(#local.strData.arrSubCategories[local.x].itemCount#)</cfif></strong></span><br />
								</td>
							</tr>
						</cfloop>
					</cfif>
				</tbody>
			</table>
		</cfif>
	</cfif>

</cfoutput> 