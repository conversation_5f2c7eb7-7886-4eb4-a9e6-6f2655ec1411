USE membercentral
Go

ALTER PROC dbo.sub_copyRate
@rateID int,
@newRateName varchar(100),
@newRateScheduleID int,
@recordedByMemberID int,
@copyPerms bit,
@newRateID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;
	CREATE TABLE #tblMCQSubCond (conditionID int);

	declare @status char(1), @rateStartDate datetime, @rateEndDate datetime, @rateStartAFID INT, @rateEndAFID INT,
		@termStartDate datetime, @termEndDate datetime, @termStartAFID INT, @termEndAFID INT, @graceEndDate datetime,
		@graceEndAFID INT, @recogStartDate datetime, @recogEndDate datetime, @recogStartAFID INT, @recogEndAFID INT,
		@rateAdvanceOnTermEnd INT, @isRenewalRate INT, @forceUpfront INT, @accountID INT, @frontEndAllowChangePrice INT,
		@linkedNonRenewalRateID INT, @scheduleID int, @fallbackRenewalRateID INT, @keepChangedPriceOnRenewal INT,
		@frontEndChangePriceMin decimal(18,2), @frontEndChangePriceMax decimal(18,2), @newSiteResourceID int,
		@rateAFStartDate datetime, @rateAFEndDate datetime, @termAFStartDate datetime, @termAFEndDate datetime,
		@recogAFStartDate datetime, @recogAFEndDate datetime, @siteResourceID int, @rfid int, @newrfid int,
		@resourceRightID int, @siteID int, @orgID int, @conditionKeyID int;
	declare @tblRights TABLE (resourceRightsID int);

	select @status=[status], @rateStartDate=rateStartDate, @rateEndDate=rateEndDate, @rateStartAFID=rateStartDateAFID,
		@rateEndAFID=rateEndDateAFID, @termStartDate=termStartDate, @termEndDate=termEndDate, @termStartAFID=termStartDateAFID,
		@termEndAFID=termEndDateAFID, @graceEndDate=graceEndDate, @graceEndAFID=graceAFID, @recogStartDate=recogStartDate,
		@recogEndDate=recogEndDate, @recogStartAFID=recogStartDateAFID, @recogEndAFID=recogEndDateAFID,
		@rateAdvanceOnTermEnd=rateAdvanceOnTermEnd, @isRenewalRate=isRenewalRate, @forceUpfront=forceUpfront,
		@accountID=GLAccountID, @frontEndAllowChangePrice=frontEndAllowChangePrice, @linkedNonRenewalRateID=linkedNonRenewalRateID,
		@scheduleID=scheduleID, @fallbackRenewalRateID=fallbackRenewalRateID, @keepChangedPriceOnRenewal=keepChangedPriceOnRenewal,
		@frontEndChangePriceMin=frontEndChangePriceMin, @frontEndChangePriceMax=frontEndChangePriceMax, @rateAFStartDate=rateAFStartDate,
		@rateAFEndDate=rateAFEndDate, @termAFStartDate=termAFStartDate, @termAFEndDate=termAFEndDate, 
		@recogAFStartDate=recogAFStartDate, @recogAFEndDate=recogAFEndDate, @siteResourceID=siteResourceID
	from dbo.sub_rates
	where rateID = @rateID;

	select @siteID = siteID from dbo.cms_siteResources where siteResourceID=@siteResourceID;
	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	-- cant copy linked rates if changing schedules
	IF @scheduleID <> @newRateScheduleID BEGIN
		set @linkedNonRenewalRateID = null;
		set @fallbackRenewalRateID = null;
	END

	SELECT @conditionKeyID = conditionKeyID 
	FROM dbo.ams_virtualGroupConditionKeys 
	WHERE conditionKey = 'subRate';

	-- get conditions that target all rates for subs using this rate schedule
	INSERT INTO #tblMCQSubCond (conditionID)
	SELECT DISTINCT c.conditionID
	FROM dbo.ams_virtualGroupConditions AS c
	INNER JOIN dbo.cache_members_conditions_subSubscriptions AS cs 
		ON cs.orgID = @orgID 
		AND cs.conditionID = c.conditionID
	INNER JOIN dbo.sub_subscriptions AS s ON s.orgID = @orgID
		AND s.subscriptionID = cs.subscriptionID
		AND s.scheduleID = @newRateScheduleID
	INNER JOIN dbo.ams_virtualGroupConditionValues AS cv ON cv.conditionID = c.conditionID
		AND cv.conditionKeyID = @conditionKeyID
		AND cv.conditionValue = ''
	WHERE c.orgID = @orgID
	AND c.fieldCode ='sub_entry';

	BEGIN TRAN;
		-- create rate
		EXEC dbo.sub_createRate @scheduleID=@newRateScheduleID, @rateName=@newRateName, @reportCode='', @status=@status, 
			@rateStartDate=@rateStartDate, @rateEndDate=@rateEndDate, @rateStartAFID=@rateStartAFID, 
			@rateEndAFID=@rateEndAFID, @termStartDate=@termStartDate, @termEndDate=@termEndDate,
			@termStartAFID=@termStartAFID, @termEndAFID=@termEndAFID, @graceEndDate=@graceEndDate,
			@graceEndAFID=@graceEndAFID, @recogStartDate=@recogStartDate, @recogEndDate=@recogEndDate,
			@recogStartAFID=@recogStartAFID, @recogEndAFID=@recogEndAFID, 
			@rateAdvanceOnTermEnd=@rateAdvanceOnTermEnd, @isRenewalRate=@isRenewalRate,
			@forceUpfront=@forceUpfront, @accountID=@accountID, @frontEndAllowChangePrice=@frontEndAllowChangePrice,
			@linkedNonRenewalRateID=@linkedNonRenewalRateID, @fallbackRenewalRateID=@fallbackRenewalRateID,
			@keepChangedPriceOnRenewal=@keepChangedPriceOnRenewal, @frontEndChangePriceMin=@frontEndChangePriceMin,
			@frontEndChangePriceMax=@frontEndChangePriceMax, @recordedByMemberID=@recordedByMemberID, @rateID=@newRateID OUTPUT, 
			@siteResourceID=@newSiteResourceID OUTPUT;

		UPDATE dbo.sub_rates
		SET rateAFStartDate = @rateAFStartDate,
			rateAFEndDate = @rateAFEndDate,
			termAFStartDate = @termAFStartDate,
			termAFEndDate = @termAFEndDate,
			recogAFStartDate = @recogAFStartDate,
			recogAFEndDate = @recogAFEndDate
		WHERE rateID = @newRateID;

		-- create rate frequencies
		select @rfid = min(RFID) from dbo.sub_rateFrequencies where rateID = @rateID;
		while @rfid is not null begin
			insert into dbo.sub_rateFrequencies (rateID, frequencyID, rateAmt, [status], numInstallments, allowFrontEnd)
			select @newRateID, frequencyID, rateAmt, [status], numInstallments, allowFrontEnd
			from dbo.sub_rateFrequencies
			where rfid = @rfid;
		
			select @newrfid = SCOPE_IDENTITY();

			insert into dbo.sub_rateFrequenciesMerchantProfiles (rfid, profileID, [status])
			select @newrfid, profileID, [status]
			from dbo.sub_rateFrequenciesMerchantProfiles
			where rfid = @rfid;
	
			select @rfid = min(RFID) from dbo.sub_rateFrequencies where rateID = @rateID and RFID > @rfid;
		end

		-- copy perms
		IF @copyPerms = 1 BEGIN
			INSERT INTO dbo.cms_siteResourceRights (siteID, resourceID, [include], functionID, roleID, groupID, inheritedRightsResourceID, inheritedRightsFunctionID)
				OUTPUT INSERTED.resourceRightsID
				INTO @tblRights
			select @siteID, @newSiteResourceID, [include], functionID, roleID, groupID, inheritedRightsResourceID, inheritedRightsFunctionID
			from dbo.cms_siteResourceRights
			where resourceID = @siteResourceID and siteID = @siteID;

			select @resourceRightID = min(resourceRightsID) from @tblRights;
			while @resourceRightID is not null begin
				EXEC dbo.cache_perms_refreshSiteResourceRight @siteID=@siteID, @siteResourceID=@newSiteResourceID, @resourceRightID=@resourceRightID;
				select @resourceRightID = min(resourceRightsID) from @tblRights where resourceRightsID > @resourceRightID;		
			end
		END
	COMMIT TRAN;
	IF EXISTS (SELECT 1 FROM #tblMCQSubCond)
		EXEC dbo.cache_members_populateSubConditionsSplitData @orgID=@orgID, @limitToConditionID=NULL;

	IF OBJECT_ID('tempdb..#tblMCQSubCond') IS NOT NULL 
		DROP TABLE #tblMCQSubCond;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
