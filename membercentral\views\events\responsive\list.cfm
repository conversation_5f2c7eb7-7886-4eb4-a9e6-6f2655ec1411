<cfparam name="attributes.data" default="#structNew()#">

<cfif thisTag.executionMode neq "start">
	<cfexit>
</cfif>

<cfset dataStruct = attributes.data.actionStruct>
<cfset calendarSettings = attributes.data.calendarSettings>
<cfset baseQueryString = attributes.data.baseQueryString>
<cfset arrEvents = attributes.data.actionStruct.arrEvents>

<!--- common js --->
<cfinclude template="../commonBaseView.cfm">

<!--- show/hide on current month list view only --->
<cfset local.ShowHidepastEventsFlag = false>
<cfif month(dataStruct.firstOfTheMonth) is month(now()) and year(dataStruct.firstOfTheMonth) is year(now())>
	<cfset local.ShowHidepastEventsFlag = true />
</cfif>
<cfif structKeyExists(url,"showAll")>
	<cfif url.showAll eq '1'>
		<cfset local.urlToShow = attributes.data.linkForPaging />
		<cfset local.urlToHide = replaceNoCase(attributes.data.linkForPaging,"showAll=1","showAll=0") />
	<cfelse>
		<cfset local.urlToShow = replaceNoCase(attributes.data.linkForPaging,"showAll=0","showAll=1") />
		<cfset local.urlToHide = attributes.data.linkForPaging />
	</cfif>
<cfelse>
	<cfset local.urlToShow = toString(attributes.data.linkForPaging) & '&showAll=1' />
	<cfset local.urlToHide = toString(attributes.data.linkForPaging) & '&showAll=0' />
</cfif>

<div class="container-fluid">
	<!--- Nav Bar --->
	<cfinclude template="calendarNavbar.cfm">
	
	<!--- main content --->
	<cfif arrayLen(arrEvents) or local.ShowHidepastEventsFlag>
		<cfoutput>
			<cfif isDefined("attributes.data.prevPage")>
				<cfif (attributes.data.prevPage NEQ 0) OR (((attributes.data.nextPage-1)*attributes.data.rowsize + 1) LTE arrayLen(arrEvents))>
					<div id="HidepastEventMessage" class="row-fluid" style="padding: 5px;">
						<div class="span12" style="text-align: right;">
							<cfif attributes.data.prevPage NEQ 0>
								<a href="#attributes.data.linkForPaging#&PageNum=#attributes.data.prevPage#"><< Previous Page</a>
							</cfif>
							<cfif (attributes.data.prevPage NEQ 0) AND (((attributes.data.nextPage-1)*attributes.data.rowsize + 1) LTE arrayLen(arrEvents))> | </cfif>
							<cfif ((attributes.data.nextPage-1)*attributes.data.rowsize + 1) lte arrayLen(arrEvents)>
								<a href="#attributes.data.linkForPaging#&PageNum=#attributes.data.nextPage#">Next Page &gt;&gt;</a>
							</cfif>
						</div>
					</div>
				</cfif>	
			</cfif>
	
			<!--- show/hide on current month list view only --->
			<cfif local.ShowHidepastEventsFlag>
				<div id="HidepastEventMessage" class="row-fluid" <cfif structKeyExists(url,"showAll") and url.showAll eq 1>style="padding: 5px; display:none;"</cfif>>
					<div class="span12 text-center">
						Note: This month's past events are hidden. <a href="#local.urlToShow#">Click to view all events</a>.
					</div>
				</div>
				<div id="ShowpastEventMessage" class="row-fluid" <cfif not structKeyExists(url,"showAll") or (structKeyExists(url,"showAll") and url.showAll neq 1)>style="padding: 5px; display:none;"</cfif>>
					<div class="span12 text-center">
						<a href="#local.urlToHide#">Click to hide past events</a>.
					</div>
				</div>
			</cfif>
			<!--- set variables for loop based on pagination --->
			<cfif isDefined("attributes.data.currPage")>
				<cfset local.fromNum=((attributes.data.currPage-1)*attributes.data.rowsize)+1>
				<cfif ((attributes.data.currPage-1)*attributes.data.rowsize)+attributes.data.rowsize gt arrayLen(arrEvents)>
					<cfset local.toNum=arrayLen(arrEvents)>
				<cfelse>
					<cfset local.toNum=((attributes.data.currPage-1)*attributes.data.rowsize)+attributes.data.rowsize>
				</cfif>
			<cfelse>
				<cfset local.fromNum=1>
				<cfset local.toNum=arrayLen(arrEvents)>
			</cfif>
			
			<cfset local.eventList = []>
			<cfloop index="x" from="#local.fromNum#" to="#local.toNum#">
				<cfset thisEvent = arrEvents[x]>
				
				<!-- Prepare categories array -->
				<cfset local.categories = []>
				<cfloop list="#thisEvent.categoryIDList#" index="categoryID">
					<cfset arrayAppend(local.categories, {
						"categoryID": categoryID,
						"categoryName": ListGetAt(thisEvent.categoryNameList, ListFind(thisEvent.categoryIDList, categoryID), '|')
					})>
				</cfloop>
				
				<!-- Add event details to local.eventList -->
				<cfset arrayAppend(local.eventList, {
					"eventID": thisEvent.eventID,
					"title": encodeForHTML(thisEvent.eventTitle),
					"categories": local.categories
				})>
				<cfif local.ShowHidepastEventsFlag and thisEvent.endTime lt now()>
					<cfset local.extraClass=" pastEvent">
					<cfset local.extraStyle="">
				<cfelse>
					<cfset local.extraClass="">
					<cfset local.extraStyle="">
				</cfif>
				<div class="row-fluid">
					<div id="ev#thisEvent.eventID#" class="span12 clearfix well well-small catev category#Replace(thisEvent.categoryIDList,'|',' category','ALL')# #local.extraClass#" style="padding: 5px;#local.extraStyle#">
						<div class="well well-small pull-left text-center event-date" style="margin-right: 10px;margin-bottom:10px;"><a href="javascript:gotoEvent(#thisEvent.eventID#,#thisEvent.isSWL#);"><span>#dateformat(thisEvent.startTime,"mmm")#</span><br><span class="lead"><strong>#dateformat(thisEvent.startTime,"dd")#</strong></span></a></div>
						<a href="javascript:gotoEvent(#thisEvent.eventID#,#thisEvent.isSWL#);"><strong>#encodeForHTML(thisEvent.eventTitle)#</strong></a> (<em>#Replace(thisEvent.categoryNameList,"|",", ","ALL")#</em>)<br/>
						<cfif len(thisEvent.eventSubTitle)><b>#encodeForHTML(thisEvent.eventSubTitle)#</b></cfif>
						<div>#thisEvent.formattedDateResponsive#</div>
					</div>
				</div>
			</cfloop>

			<cfif isDefined("attributes.data.prevPage")>
				<cfif (attributes.data.prevPage NEQ 0) OR (((attributes.data.nextPage-1)*attributes.data.rowsize + 1) LTE arrayLen(arrEvents))>
					<div id="HidepastEventMessage" class="row-fluid" style="padding: 5px;">
						<div class="span12" style="text-align: right;">
							<cfif attributes.data.prevPage NEQ 0>
								<a href="#attributes.data.linkForPaging#&PageNum=#attributes.data.prevPage#"><< Previous Page</a>
							</cfif>
							<cfif (attributes.data.prevPage NEQ 0) AND (((attributes.data.nextPage-1)*attributes.data.rowsize + 1) LTE arrayLen(arrEvents))> | </cfif>
							<cfif ((attributes.data.nextPage-1)*attributes.data.rowsize + 1) lte arrayLen(arrEvents)>
								<a href="#attributes.data.linkForPaging#&PageNum=#attributes.data.nextPage#">Next Page &gt;&gt;</a>
							</cfif>
						</div>
					</div>
				</cfif>	
			</cfif>
			<script language="javascript">
				$(function() {					
					<cfset local.eventsJSON = SerializeJSON(local.eventList)>
					<cfif IsJSON(local.eventsJSON)>
						MCLoader.loadJS('/assets/common/javascript/mcapps/events/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
						.then( () => {
							try {
								triggerEventsViewItemList('#attributes.data.pageName#',#local.eventsJSON#, 'List');
							} catch (error) {
								console.error("Error parsing JSON:", error.message);
							}
						});
					</cfif>
				});
			</script>
		</cfoutput>
	<cfelse>
		<div class="row-fluid">
			<div class="span12">There are no events scheduled for this month.</div>
		</div>
	</cfif>
</div>

<!--- footer code --->
<cfsavecontent variable="footerCode">
	<cfinclude template="commonBaseViewFooter.cfm">
</cfsavecontent>
<cfoutput>#application.objCommon.minText(footerCode)#</cfoutput>
