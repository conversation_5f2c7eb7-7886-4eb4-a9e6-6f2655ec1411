$(document).ready(function() {
    $('.fa').each(function() {
        if ($.trim($(this).html()) == '&nbsp;') $(this).html('');
    });
    $('.fas').each(function() {
        if ($.trim($(this).html()) == '&nbsp;') $(this).html('');
    });
    //Fixed Header Scroll Function Start
    $(window).scroll(function() {
        if ($(window).scrollTop() > 300) {
            $("#header").addClass("fixed-header", 220);
            $(".banner-content-bottom").addClass("fixed", 220);
        } else {
            $("#header").removeClass("fixed-header", 220);
            $(".banner-content-bottom").removeClass("fixed", 220);
        }
        if ($(window).scrollTop() > 300) {
            $("#header").addClass("shrink", 220);
        } else {
            $("#header").removeClass("shrink", 220);
        }
    });
    //Fixed Header Scroll Function End

    // menu css
    $('.navMenu > ul > li:has(ul)').addClass('dropdown');
    $('.navMenu > ul > li:has(ul)').append("<span class='dropdownArrow'></span>");
    $('.navMenu > ul > li > ul').addClass('dropdown-menu');
    $('.navMenu > ul > li > ul > li:has(ul)').addClass('dropdown-submenu');
    $('.navMenu > ul > li > ul > li:has(ul)').append("<span class='dropdownArrow'></span>");
    $('.navMenu > ul > li > ul > li.dropdown-submenu > ul').addClass('subdropdown-menu');
    $('.navMenu > ul > li > ul > li > ul > li:has(ul)').addClass('dropdown-submenu');
    $('.navMenu > ul > li > ul > li > ul > li:has(ul)').append("<span class='dropdownArrow'></span>");
    $('.navMenu > ul > li > ul > li.dropdown-submenu > ul > li.dropdown-submenu > ul').addClass('subdropdown-menu');
    $('.navMenu > ul > li.dropdown > a').append('<img alt="" src="/images/nav-arrow.png">');

    //  add remove class in header
    $('#navBtn').click(function() {
        $('.navbar-inner .menuDiv').toggleClass("show-menu");
        $('#navBtn').toggleClass("active");
    });

    $('.dropdown-submenu .dropdownArrow').on('click', function() {
        $(this).parent().children('.subdropdown-menu').slideToggle();
    });

    $('.dropdown .dropdownArrow').on('click', function() {
        $(this).parent().toggleClass('menu-open');
        $(this).parent().find('.dropdown-menu').slideToggle();
        $(this).parent().find('ul').find('ul').slideUp();
        $(this).parent().find('ul').find('.dropdown-submenu.menu-open').removeClass('menu-open');
    });

    //Accordian
    $('.accordion').each(function() {
        var $accordian = $(this);
        $accordian.find('.accordion-head').on('click', function() {
            $(this).parent().find(".accordion-head").removeClass('open closee');
            $(this).removeClass('open').addClass('closee');
            $accordian.find('.accordion-body').slideUp();
            $accordian.find('.accordion-body').toggleClass('openBody');
            if (!$(this).next().is(':visible')) {
                $(this).removeClass('closee').addClass('open');
                $(this).next().slideDown();
            }
        });
    });

    /*****************/
    $(".quicklink-mobile .DiamondBullets").hide();
    $(".quicklink-mobile h3").click(function() {
        $(".quicklink-mobile .DiamondBullets").slideToggle();
        $(this).toggleClass("quicklink-open");
    });

    $(".search-icon").click(function() {
        $(".searchbar-top").slideToggle();
    });

    $('.homeSlider img').unwrap();
    $('.NATLE-FYI-info .infoBox img').unwrap();
    $('.subfooter img').unwrap();

    $('.sponsorHeaderHolder').html('<h2 class="HeaderText">' + $(".sponsorWrapper h2").html() + '</h2>');
    var sponsorContent = '';
    $(".sponsorWrapper > ul > li").each(function(key) {
        sponsorContent = sponsorContent + '<li class="item">' + $(this).html() + '</li>';
    });

    if ($(".sponsorWrapper > ul > li").length) {
        $('.sponsorHolder').replaceWith(sponsorContent);
        $('.sponsorHolder').show();
        $('.sponsorOuter').show();
    }

    var eventWrapper = $('.eventHomeWrapper ul');
    var fullEventString = '';
    $('.upcomingEvents').hide();
    eventWrapper.each(function() {
        var heading = '';
        var eventLinkHref = '';
        var date = '';
        var category = '';
        var eventString = '';
        var EventImage = '';
        if ($('a', $($('li', $(this))[0])).length) {
            heading = $('a', $($('li', $(this))[0])).html();
            eventLinkHref = $('a', $($('li', $(this))[0])).attr('href');
        }
        if ($($('li', $(this))[1]).length) {
            date = $($('li', $(this))[1]).html();
        }
        if ($($('li', $(this))[2]).length && $($('li', $(this))[2]).html().length) {
            category = '<h6>' + $($('li', $(this))[2]).html() + '</h6>';
        }
        if ($($('li', $(this))[3]).length) {
            EventImage = $($('li', $(this))[3]).html();
        }

        if (EventImage == 'default-image.jpg') {
            EventImage = '<img src="/userimages/default-image.jpg" alt="Event Image"/>';
        } else {
            EventImage = '<img src="' + $.trim(EventImage).toLowerCase() + '" alt="Event Image" />';
        }

        eventString = '<div class="span4"><a href="' + eventLinkHref + '"><div class="event-image"><span>' + EventImage + '</span><label>' + date + '</label></div><div class="event-content">' + category + '<h5>' + heading + '</h5></div></a></div>';
        fullEventString = fullEventString + eventString;
    });
    var eventHeaderString = "";
    $('.eventHomeWrapper > p a').each(function() {
        eventHeaderString = eventHeaderString + '<div class="btn-center"><a href="' + $(this).attr('href') + '" class="NATLEButton">' + $(this).html() + '</a></div>';
    });
    fullEventString = '<div class="row-fluid">' + fullEventString + '</div>' + eventHeaderString;

    var eventHeader = '<h4 class="SectionHeader">' + $('.eventHomeWrapper h4').html() + '</h4>';
    if (eventWrapper.length) {
        $('.eventHomeHeaderHolder').replaceWith(eventHeader);
        $('.eventHomeHolder').replaceWith(fullEventString);
        $('.upcomingEvents').show();
    }

    $('.NATLE-FYI-info').hide();
    var newsWrapper = $('.newsHomeWrapper ul');
    var fullnewsString = '';
    newsWrapper.each(function() {
        var heading = '';
        var newsLinkHref = '';
        var date = '';
        var category = '';
        var newsString = '';
        if ($('a', $($('li', $(this))[0])).length) {
            heading = $('a', $($('li', $(this))[0])).html();
            newsLinkHref = $('a', $($('li', $(this))[0])).attr('href');
        }
        if ($($('li', $(this))[1]).length) {
            date = $($('li', $(this))[1]).html();
        }
        if ($($('li', $(this))[2]).length) {
            author = $($('li', $(this))[2]).html();
        }
        newsString = '<a href="' + newsLinkHref + '"><div class="blog-row"><div class="blog-date">' + date + '</div><div class="blog-col"><span>' + author + '</span><h5>' + heading + '</h5></div></div></a>';
        fullnewsString = fullnewsString + newsString;
    });

    var newsHeaderString = "";
    $('.newsHomeWrapper > p a').each(function() {
        newsHeaderString = newsHeaderString + '<div class="btn-center"><a href="' + $(this).attr('href') + '" class="NATLEButton">' + $(this).html() + '</a></div>';
    })
    fullnewsString = fullnewsString + '<div class="eventbox-item eventbox-item-link">' + newsHeaderString + '</div>';
    if (newsWrapper.length) {
        $('.blogHomeHolder').replaceWith(fullnewsString);
        $('.NATLE-FYI-info').show();
    }

    $(".homeSlider ul").addClass("carousel-caption");
    homeSliderObj = $('.homeSlider').owlCarousel({
        loop: true,
        items: 1,
        margin: 0,
        nav: false,
        animateOut: 'fadeOut',
        autoHeight: true,
        dots: true,
        autoplay: true,
        autoplayTimeout: 7000,
        autoplayHoverPause: false,
        mouseDrag: false,
    })
    // Sponsor slider
    var sponsorCarouselCount = $('.sponsorWrapper li').length;
    sponsorSlider2Obj = $('.sponsorSlider2').owlCarousel({
        loop: (sponsorCarouselCount > 5)?true:false,
        items: sponsorCarouselCount,
        dots: false,
        drag: false,
        mouseDrag: false,
        autoplay: true,
        autoPlaySpeed: 3000,
        autoplayTimeout: 3000,
        margin: 5,
        nav: false,
        navText: ["<i class='fa fa-chevron-left' aria-hidden='true'></i>", "<i class='fa fa-chevron-right' aria-hidden='true'></i>"],
        responsive: {
            0: { items: 1,loop: (sponsorCarouselCount > 1)?true:false },
            480: { items: 2,loop: (sponsorCarouselCount > 2)?true:false },
            600: { items: 3,loop: (sponsorCarouselCount > 3)?true:false },
            992: { items: 4,loop: (sponsorCarouselCount > 4)?true:false },
            1200: { items: 5,loop: (sponsorCarouselCount > 5)?true:false }
        }
    });

    if ($('.quickLinkHeaderWrapper a > img').length == 0) {
        var quickLinkWrapper = $('.quickLinkWrapper > ul li');
        var fullQuickLinkString = '';
        quickLinkWrapper.each(function() {
            linkText = $('a', this).html();
            link = $('a', this).attr('href');
            fullQuickLinkString = fullQuickLinkString + '<a href="' + link + '" class="sidebar-iconbox"><div class="textBox"><h2>' + linkText + '</h2></div><span class="arrow"><img src="/images/arrow1.png" alt=""></span></a>';
        });
        $('.head-one').html($('.quickLinkHeaderWrapper h4').html());
        var quickLinkHeader = '<div class="eventbox-img"><span><img src="' + $($('.quickLinkHeaderWrapper img')[1]).attr('src') + '"></span><div class="event-head"><img src="' + $($('.quickLinkHeaderWrapper img')[0]).attr('src') + '"><h4>' + $('.quickLinkHeaderWrapper h4').html() + '</h4></div></div>';
        if (quickLinkWrapper.length) {
            $('.quickLinkHeaderHolder').replaceWith(quickLinkHeader);
            $('.quickLinkHolder').replaceWith(fullQuickLinkString);
            $('.eventthree').show();
            $('.zeroItem').show();
        }
    } else {
        $('.quicklink-desktop .eventbox-col.eventthree').html($('.quickLinkHeaderWrapper').html());
        $('.quickLinkImg').html($('.quickLinkHeaderWrapper').html());
        $('.eventthree').show();
        $('.quickLinkImg').show();
    }

    var eventWrapper = $('.eventWrapper ul');
    var fullEventString = '';
    eventWrapper.each(function() {
        var heading = '';
        var eventLinkHref = '';
        var date = '';
        var category = '';
        var eventString = '';
        if ($('a', $($('li', $(this))[0])).length) {
            heading = $('a', $($('li', $(this))[0])).html();
            eventLinkHref = $('a', $($('li', $(this))[0])).attr('href');
        }
        if ($($('li', $(this))[1]).length) {
            date = $($('li', $(this))[1]).html();
        }
        if ($($('li', $(this))[2]).length && $($('li', $(this))[2]).html().length) {
            category = '<li>' + $($('li', $(this))[2]).html() + '</li>';
        }
        eventString = '<a href="' + eventLinkHref + '"><div class="eventbox-item"><div class="eventbox-item-in"><ul><li>' + date + '</li>' + category + '</ul><p>' + heading + '</p></div></div></a>';
        fullEventString = fullEventString + eventString;
    });
    var eventHeaderString = "";
    $('.eventWrapper > p a').each(function() {
        eventHeaderString = eventHeaderString + '<a href="' + $(this).attr('href') + '" class="event-link">' + $(this).html() + ' <i class="fa fa-angle-double-right" aria-hidden="true"></i></a>';
    });
    fullEventString = fullEventString + '<div class="eventbox-item eventbox-item-link">' + eventHeaderString + '</div>';
    $('.head-two').html($('.eventHeaderWrapper h4').html());
    var eventHeader = '<div class="eventbox-img"><span><img src="' + $($('.eventHeaderWrapper img')[1]).attr('src') + '"></span><div class="event-head"><img src="' + $($('.eventHeaderWrapper img')[0]).attr('src') + '"><h4>' + $('.eventHeaderWrapper h4').html() + '</h4></div></div>';
    if (eventWrapper.length) {
        $('.eventHeaderHolder').replaceWith(eventHeader);
        $('.eventHolder').replaceWith(fullEventString);
        $('.eventone').show();
        $('.oneItem').show();
    }

    var newsWrapper = $('.newsWrapper ul');
    var fullnewsString = '';
    newsWrapper.each(function() {
        var heading = '';
        var newsLinkHref = '';
        var date = '';
        var category = '';
        var newsString = '';
        if ($('a', $($('li', $(this))[0])).length) {
            heading = $('a', $($('li', $(this))[0])).html();
            newsLinkHref = $('a', $($('li', $(this))[0])).attr('href');
        }
        if ($($('li', $(this))[1]).length) {
            date = $($('li', $(this))[1]).html();
        }
        newsString = '<a href="' + newsLinkHref + '"><div class="eventbox-item"><div class="eventbox-item-in"><ul><li>' + date + '</li></ul><p>' + heading + '</p></div></div></a>';
        fullnewsString = fullnewsString + newsString;
    });

    var newsHeaderString = "";
    $('.newsWrapper > p a').each(function() {
        newsHeaderString = newsHeaderString + '<a href="' + $(this).attr('href') + '" class="event-link">' + $(this).html() + ' <i class="fa fa-angle-double-right" aria-hidden="true"></i></a>';
    })
    fullnewsString = fullnewsString + '<div class="eventbox-item eventbox-item-link">' + newsHeaderString + '</div>';
    var newsHeader = '<div class="eventbox-img"><span><img src="' + $($('.newsHeaderWrapper img')[1]).attr('src') + '"></span><div class="event-head"><img src="' + $($('.newsHeaderWrapper img')[0]).attr('src') + '"><h4>' + $('.newsHeaderWrapper h4').html() + '</h4></div></div>';
    $('.head-three').html($('.newsHeaderWrapper h4').html());
    if (newsWrapper.length) {
        $('.newsHeaderHolder').replaceWith(newsHeader);
        $('.newsHolder').replaceWith(fullnewsString);
        $('.eventtwo').show();
        $('.twoItem').show();
    }
    $(".footCol2 > h4").html($(".footCol2 .list-inline > h4").html());
    $(".footCol1 p:last-child a").addClass("link");
    $(".printFooter").html('<p>' + $(".copyrightList ul li:first-child").html() + '</p>');
    $('.homeSlider .item>ul li:nth-child(3)').each(function(){
        if($('p',this).length ==0){
            $(this).html('<p>'+$(this).html()+'</p>');
        }
    })

    document.addEventListener("visibilitychange", function() {
    if (!document.hidden) {
        homeSliderObj.trigger("stop.owl.autoplay");   
        homeSliderObj.trigger("play.owl.autoplay",[7000]);
        sponsorSlider2Obj.trigger("stop.owl.autoplay");   
        sponsorSlider2Obj.trigger("play.owl.autoplay",[3000]);
    }
    });
});