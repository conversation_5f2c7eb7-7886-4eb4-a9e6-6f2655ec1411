<cfset local.strCredits = ''>
<cfset local.strIdx = 1>
<cfloop query="arguments.strCredit.qrywddxcredits">
	<cfif arguments.strCredit.qrywddxcredits.fieldname neq 'ContinuingProfessionalDevelopment'>
		<cfset local.strCredits &= '#arguments.strCredit.qrywddxcredits.numcredits# #arguments.strCredit.qrywddxcredits.displayname#'>
		<cfif local.strIdx neq arguments.strCredit.qrywddxcredits.recordcount>
			<cfset local.strCredits &= ', '>
		</cfif>
	</cfif>
	<cfset local.strIdx = local.strIdx  + 1>
</cfloop>

<cfset local.titleLength = len(arguments.enrollmentInfo.contentName)>
<cfset local.lengthyTitleClass = "">
<cfif local.titleLength gt 90>
	<cfset local.lengthyTitleClass = "longTitle">
</cfif>

<cfoutput>
	<html>
		<head>
			<title>Statement of Credit</title>
			<link href="#local.assetsSiteURL#../fonts/ford-antenna-webfont/style.css/fonts/ford-antenna-webfont/style.css" rel="stylesheet" type="text/css">
			<style>	
			@font-face {
			font-family: 'Ford Antenna Medium';
			font-style: normal;
			font-weight: normal;
			}
				@page land {size: landscape;}
				.landscape {page: land;}
				body {  
					width: 297mm;
					height: auto;
					margin: 0;
					padding: 0;
					font-family: 'Ford Antenna Medium';
				}
				
				.mainContainer{ 
					width: 100%;
					height: 100%;
					border-collapse: collapse;
					table-layout: fixed;
					padding:0px;
					margin:0px;
				}

				.mainContainer td{
					width: 50%;
					height: 100%;
					vertical-align: top;
					padding: 0px;
				}
				.mainContainer td.left img{
					width: 80%;
					height: 229mm;				
				}
				.right {					
					background:##fff;
					width:60%
				}
				.left {					
					width:40%;
				}
				.rightLogo{
					width: 524px !important;
					height: 65px !important;
					margin-top:75px;
					margin-left:-48px;
				}
				.cOfCompletion.longTitle div{
					font-size:40px;
					margin-left:-105px !important;		
					padding-top:5px !important;					
				}
				.cOfCompletion div{
					color: ##730460;
					margin-top:22px;
					font-size:43px;
					margin-left:-150px;
					font-family: 'Ford Antenna Medium';
					padding-top:25px;
				}
				.presentedTo.longTitle div{
					padding-top:5px;
				}
				.presentedTo div{
					color: ##730460;
					margin-top:22px;
					font-size:24px;
					padding-right:90px;
					text-align:right;
					font-family: 'Ford Antenna Medium';
					padding-top:25px;
				}
				.creditDiv.longTitle div{
					padding-top:5px;
				}
				.creditDiv div{
					color: ##730460;
					margin-top:22px;
					font-size:22px;
					font-family: 'Ford Antenna Medium';
					text-align:right;
					padding-right:90px;
					margin-left:-130px;
					padding-top:25px;
				}
				.dateDiv div{
					color: ##730460;
					margin-top:22px;
					font-size:22px;
					padding-right:90px;
					font-family: 'Ford Antenna Medium';
					text-align:right;
				}
				.programTitle.longTitle div{
					font-size:40px !important;
				}
				.programTitle div{
					color: ##000;
					margin-top:22px;
					font-size:44px;
					margin-left:-185px;
					margin-right:82px;
					font-family: 'Ford Antenna Medium';
					text-align:right;
				}
				.presentedToName.longTitle div{
					font-size:40px !important;
				}
				.presentedToName div{
					margin-top:22px;
					font-size:43px;
					color: ##000;
					font-family: 'Ford Antenna Medium';
					text-align:right;
					padding-right:90px;
				}
				.footerLogoWrap img{
					position:absolute;
					right:-400px;
					bottom: 75px;
					height:70px;
					width:100px;
				}
			</style>
		</head>
		
		<body>
			<cfoutput>
				<table class="mainContainer">
					<tr>
						<td class="left">
							<img src="#local.assetsSiteURL#leftBg.png">
						</td>
						<td class="right">
							<table class="rightContainer">
								<tr>
									<td>
										<img class="rightLogo" src="#local.assetsSiteURL#swodCertLogo.png">
									</td>
								</tr>
								<tr>
									<td class="cOfCompletion #local.lengthyTitleClass#">
										<div>CERTIFICATE OF COMPLETION</div>
									</td>
								</tr>
								<tr>
									<td class="programTitle #local.lengthyTitleClass#">
										<div>#arguments.enrollmentInfo.contentName#</div>
									</td>
								</tr>
								<tr>
									<td class="presentedTo #local.lengthyTitleClass#"">
										<div>This certificate is proudly presented to</div>
									</td>
								</tr>
								<tr>
									<td class="presentedToName #local.lengthyTitleClass#">
										<div>#arguments.enrollmentInfo.fullname#</div>
									</td>
								</tr>
								<tr>
									<td class="creditDiv #local.lengthyTitleClass#">
										<div>#local.strCredits#</div>
									</td>
								</tr>
								<tr>
									<td class="dateDiv">
										<div>										
										#DateFormat(arguments.enrollmentInfo.dateCompleted, "m/d/yyyy")# at #TimeFormat(arguments.enrollmentInfo.dateCompleted, "h:mm tt")# CT
										</div>
									</td>
								</tr>
							</table>
							<div class="footerLogoWrap">
								<img width="130" height="105" class="footerLogo" src="#local.assetsSiteURL#SWODCertFooter.jpg">
							</div>
						</td>
					</tr>
				</table>
			</cfoutput>
		</body>
	</html>
</cfoutput>