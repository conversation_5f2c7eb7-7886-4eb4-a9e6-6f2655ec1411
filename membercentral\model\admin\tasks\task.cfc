<cfcomponent output="false">

	<cffunction name="getTaskData" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="taskID" type="numeric" required="true">

		<cfset var qryTaskDetails = ''>

		<cfquery name="qryTaskDetails" datasource="#application.dsn.membercentral.dsn#">
			select t.taskID, t.siteResourceID as taskSiteResourceID, t.dateEntered, t.dateDue, t.taskStatusID, 
				t.nextReminderDate, t.payProfileID, s.statusName, p.projectID, p.siteResourceID as projectSiteResourceID, 
				t.taskContentID, taskContent.contentTitle, taskContent.contentDesc, taskContent.rawContent, taskContent.isHTML, 
				mActive.firstname as prospectFirstName, mActive.lastname as prospectLastName, mActive.memberID as prospectMemberID
			from dbo.tasks_tasks as t
			inner join dbo.tasks_projects as p on p.projectID = t.projectID
			inner join dbo.tasks_statuses as s on s.taskStatusID = t.taskStatusID
			inner join dbo.cms_siteResources sr on sr.siteID = <cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER"> 
				and sr.siteResourceStatusID = 1 
				and t.siteResourceID = sr.siteResourceID 
			left outer join dbo.ams_members as m 
				inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				on m.memberID = t.prospectMemberID
			cross apply dbo.fn_getContent(t.taskContentID,<cfqueryparam value="#arguments.languageID#" cfsqltype="CF_SQL_INTEGER">) as taskContent
			where t.taskID = <cfqueryparam value="#arguments.taskID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn qryTaskDetails>
	</cffunction>

	<cffunction name="getSaveTaskDataStructure" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		
		<cfset local.columnNameRegex = "[^A-Za-z0-9_\-\&\(\)\:\/\s]">

		<!--- Project Settings --->
		<cfquery name="local.qryProject" datasource="#application.dsn.membercentral.dsn#">
			select p.siteResourceID, p.requestPayProfile, mp.profileID, mp.profileCode, rpt_sr.ruleID as projectRuleID,
				w.taskFieldLabel, w.taskFieldLabelPlural
			from dbo.tasks_projects as p
			inner join dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'workspace'
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = p.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.rpt_SavedReports as rpt_sr on rpt_sr.controllingSiteResourceID = p.siteResourceID
			left outer join dbo.mp_profiles as mp on mp.profileID = p.profileID 
				and mp.status = 'A'
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			and p.projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('projectID')#">
		</cfquery>

		<cfset arguments.event.setValue('mcproject_ruleID',local.qryProject.projectRuleID)>

		<cfif arguments.event.getValue('taskID',0) gt 0>
			<cfif local.qryProject.requestPayProfile is 1 and arguments.event.getValue('p_#local.qryProject.profileID#_mppid',0) gt 0>
				<cfset arguments.event.setValue('newPayProfileID',arguments.event.getValue('p_#local.qryProject.profileID#_mppid'))>
			<cfelse>
				<cfset arguments.event.setValue('newPayProfileID',0)>
			</cfif>
		</cfif>

		<cfquery name="local.qryTaskTags" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ct.siteResourceID, ct.categoryTreeID, ct.categoryTreeName, c.categoryID, c.categoryName, 
				dbo.fn_regexReplace(ct.categoryTreeName + '_' + c.categoryName,'#local.columnNameRegex#','') as taskTag, 
				ROW_NUMBER() OVER (ORDER BY ct.sortOrder, c.sortOrder) as row
			from dbo.cms_categoryTrees as ct
			inner join dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.cms_categories as c on c.categoryTreeID = ct.categoryTreeID and c.isActive = 1
			inner join dbo.cms_applicationInstances as ai on ai.siteResourceID = ct.controllingSiteResourceID
			inner join dbo.tasks_workspaces as w on w.applicationInstanceID = ai.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'workspace'
			inner join dbo.cms_siteResources as sr2 on sr2.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs2 on srs2.siteResourceStatusID = sr2.siteResourceStatusID
				and srs2.siteResourceStatusDesc = 'Active'
			inner join dbo.tasks_projects as p on p.workspaceID = w.workspaceID
			where ct.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			and p.projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('projectID')#">
			order by row;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset var taskTags = arguments.event.getTrimValue('taskTags','')>
		<cfset var arrTaskTags = []>
		<cfset local.qryTaskTags.each(
				function(thisRow) {
					var str = {
							'categoryID':arguments.thisRow.categoryID,
							'siteResourceID':arguments.thisRow.siteResourceID,
							'columnName':arguments.thisRow.taskTag,
							'columnvalue':''
						};
					if (listfind(taskTags,arguments.thisRow.categoryID))
						str.columnvalue = arguments.thisRow.taskTag;

					arrTaskTags.append(str);
				})>

		<cfset local.arrTaskTagCustomFields = []>
		<cfif listLen(taskTags)>
			<cfset local.qrySelectedTaskTags = QueryFilter(local.qryTaskTags,
					function(thisRow) {
						return listfind(taskTags,arguments.thisRow.categoryID);
					})>

			<!--- put task tag custom fields into array --->
			<cfloop query="local.qrySelectedTaskTags">
				<cfset local.thisTaskTagFieldsXML = local.objCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='ProjectWorkspace', areaName='TaskTag', csrid=local.qrySelectedTaskTags.siteResourceID, detailID=local.qrySelectedTaskTags.categoryID, hideAdminOnly=0)>
				<cfset local.thisTaskTagFieldsArr = xmlParse(local.thisTaskTagFieldsXML.returnXML).xmlRoot.xmlChildren>

				<cfif arrayLen(local.thisTaskTagFieldsArr)>
					<cfloop array="#local.thisTaskTagFieldsArr#" index="local.thisfield">
						<cfset local.tmpAtt = local.thisfield.xmlattributes>
						<cfif local.tmpAtt.displayTypeCode NEQ 'LABEL'>
							<cfset local.tmpStr = { categoryID=local.qrySelectedTaskTags.categoryID,
													fieldID=local.tmpAtt.fieldID,
													fieldText='#local.qrySelectedTaskTags.categoryName#_#local.tmpAtt.fieldText#',
													displayTypeCode=local.tmpAtt.displayTypeCode, 
													dataTypeCode=local.tmpAtt.dataTypeCode, 
													columnReference="cf_#local.tmpAtt.fieldID#",
													columnName="#local.qrySelectedTaskTags.taskTag#_#local.tmpAtt.fieldReference#",
													columnValue="",
													columnValueIDList="",
													value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#','') }>

							<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpAtt.displayTypeCode) and len(local.tmpStr.value)>
								<cfset var valueIDList = local.tmpStr.value>
								<cfset local.arrColValues = local.thisfield.xmlChildren.filter(
										function(thisRow) {
											return listFind(valueIDList,arguments.thisRow.XmlAttributes.valueID);
										})>
								<cfif arrayLen(local.arrColValues)>
									<cfset local.tmpStr.columnValue = local.arrColValues.map(
										function(thisRow) {
											return arguments.thisRow.XmlAttributes.fieldValue;
										})>
									<cfset local.tmpStr.columnValueIDList = valueIDList>
									<cfset local.tmpStr.columnValue = arrayToList(local.tmpStr.columnValue,'|')>
								</cfif>
							<cfelse>
								<cfset local.tmpStr.columnValue = local.tmpStr.value>
							</cfif>

							<cfset arrayAppend(local.arrTaskTagCustomFields,local.tmpStr)>
						</cfif>
					</cfloop>
				</cfif>
			</cfloop>
		</cfif>

		<!--- Project Fields --->
		<cfset local.projectCustomFieldsXML = local.objCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='Project', areaName='Project', csrid=local.qryProject.siteResourceID, detailID=arguments.event.getValue('projectID'), hideAdminOnly=0)>
		<cfset local.projectCustomFieldArr = xmlParse(local.projectCustomFieldsXML.returnXML).xmlRoot.xmlChildren>

		<!--- put project custom fields and field types into array --->
		<cfset local.arrProjectFields = []>
		<cfif arrayLen(local.projectCustomFieldArr)>
			<cfloop array="#local.projectCustomFieldArr#" index="local.thisfield">
				<cfset local.tmpAtt = local.thisfield.xmlattributes>
				<cfif local.tmpAtt.displayTypeCode NEQ 'LABEL'>
					<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID,
											fieldText=local.tmpAtt.fieldText,
											displayTypeCode=local.tmpAtt.displayTypeCode, 
											dataTypeCode=local.tmpAtt.dataTypeCode, 
											columnReference="cf_#local.tmpAtt.fieldID#",
											columnName=local.tmpAtt.fieldReference,
											columnValue="",
											columnValueIDList="",
											value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#','') }>

					<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.tmpAtt.displayTypeCode) and len(local.tmpStr.value)>
						<cfset var valueIDList = local.tmpStr.value>
						<cfset local.arrColValues = local.thisfield.xmlChildren.filter(
								function(thisRow) {
									return listFind(valueIDList,arguments.thisRow.XmlAttributes.valueID);
								})>
						<cfif arrayLen(local.arrColValues)>
							<cfset local.tmpStr.columnValue = local.arrColValues.map(
								function(thisRow) {
									return arguments.thisRow.XmlAttributes.fieldValue;
								})>
							<cfset local.tmpStr.columnValueIDList = valueIDList>
							<cfset local.tmpStr.columnValue = arrayToList(local.tmpStr.columnValue,'|')>
						</cfif>
					<cfelse>
						<cfset local.tmpStr.columnValue = local.tmpStr.value>
					</cfif>

					<cfset arrayAppend(local.arrProjectFields,local.tmpStr)>
				</cfif>
			</cfloop>
		</cfif>

		<cfset local.strActionFields = {}>
		<cfset local.arrExtraFields = []>
		<cfset local.solicitorsList = "">

		<cfif arguments.event.getValue('taskID',0) eq 0>
			<!--- solicitors --->
			<cfloop collection="#arguments.event.getCollection()#" item="local.thisField">
				<cfif (left(local.thisField,10) eq "tassignee_" and arguments.event.getTrimValue(local.thisField) gt 0)>
					<cfset local.solicitorsList = listAppend(local.solicitorsList,arguments.event.getTrimValue(local.thisField))>
				</cfif>
			</cfloop>

			<!--- unselected task tag fields to be appended in arrExtraFields to not break import --->
			<cfif arrayLen(arrTaskTags)>
				<cfset local.arrUnselectedTaskTags = arrTaskTags.filter(function(thisRow) { return arguments.thisRow.columnValue eq ''; })>
				<cfif arrayLen(local.arrUnselectedTaskTags)>
					<cfloop array="#local.arrUnselectedTaskTags#" index="local.thisTaskTag">
						<cfset local.thisTaskTagFieldsXML = local.objCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'), resourceType='ProjectWorkspace', areaName='TaskTag', csrid=local.thisTaskTag.siteResourceID, detailID=local.thisTaskTag.categoryID, hideAdminOnly=0)>
						<cfset local.thisTaskTagFieldsArr = xmlParse(local.thisTaskTagFieldsXML.returnXML).xmlRoot.xmlChildren>

						<cfif arrayLen(local.thisTaskTagFieldsArr)>
							<cfloop array="#local.thisTaskTagFieldsArr#" index="local.thisfield">
								<cfset local.arrExtraFields.append(
										{ 'columnName':'#local.thisTaskTag.columnName#_#local.thisfield.xmlattributes.fieldReference#', 'columnValue':'' }
									)>
							</cfloop>
						</cfif>
					</cfloop>
				</cfif>
			</cfif>

			<cfset local.prospectMemberNumberList = arguments.event.getTrimValue('prospectMemberNumberList','')>
			<cfif local.prospectMemberNumberList neq ''>
				<cfset local.crlf = "#Chr(13)##Chr(10)#">
				<cfset local.prospectMemberNumberList = listChangeDelims(arguments.event.getTrimValue('prospectMemberNumberList'),',',local.crlf)>
				<cfset local.prospectMemberNumberList = local.prospectMemberNumberList.listremoveduplicates()>
			</cfif>

			<cfset structInsert(local.strActionFields, "prospectAddMode", {"value":arguments.event.getValue('prospectAddMode',''), "valueid":""})>
			<cfset structInsert(local.strActionFields, "prospectMemberID", {"value":arguments.event.getValue('prospectMemberID',0), "valueid":""})>
			<cfset structInsert(local.strActionFields, "prospectMemberNumberList", {"value":local.prospectMemberNumberList, "valueid":""})>
			<cfset structInsert(local.strActionFields, "dueDate", {"value":arguments.event.getValue('dueDate',''), "valueid":""})>
			<cfset structInsert(local.strActionFields, "nextReminderDate", {"value":arguments.event.getValue('nextReminderDate',''), "valueid":""})>
			<cfset structInsert(local.strActionFields, "taskStatus", {"value":getTaskStatusName(statusID=arguments.event.getValue('taskStatus',0)), 
																		"valueid":arguments.event.getValue('taskStatus',0)})>
			<cfset structInsert(local.strActionFields, "solicitorsList", {"value":local.solicitorsList, "valueid":""})>
			<cfset structInsert(local.strActionFields, "taskDetails", {"value":arguments.event.getValue('taskDetails',''), "valueid":""})>

			<cfloop array="#local.arrProjectFields#" index="local.thisField">
				<cfset structInsert(local.strActionFields, local.thisField.columnReference, {"value":local.thisField.columnValue, "valueid":local.thisField.columnValueIDList})>
			</cfloop>
			<cfloop array="#local.arrTaskTagCustomFields#" index="local.thisField">
				<cfset structInsert(local.strActionFields, local.thisField.columnReference, {"value":local.thisField.columnValue, "valueid":local.thisField.columnValueIDList})>
			</cfloop>
		</cfif>

		<cfset local.returnStruct = { "arrTaskTags":arrTaskTags, "arrTaskTagCustomFields":local.arrTaskTagCustomFields, 
										"arrProjectFields":local.arrProjectFields, "arrExtraFields":local.arrExtraFields, 
										"solicitorsList":local.solicitorsList, "strActionFields"=local.strActionFields,
										"taskFieldLabel":local.qryProject.taskFieldLabel, 
										"taskFieldLabelPlural":local.qryProject.taskFieldLabelPlural }>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveTask" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="strTaskData" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<!--- activate rule version --->
		<cfset local.projectRuleID = val(arguments.event.getValue('mcproject_ruleID',0))>
		<cfif val(arguments.event.getValue('useRuleVersionID_#local.projectRuleID#',0))>
			<cfset CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin").acceptRuleVersion(ruleID=local.projectRuleID, ruleVersionID=arguments.event.getValue('useRuleVersionID_#local.projectRuleID#'))>
		</cfif>
		
		<!--- update --->
		<cfif arguments.event.getValue('taskID',0) gt 0>
			<cfset local.arrTaskChanges = getTaskUpdateChanges(event=arguments.event, arrTaskTagCustomFields=arguments.strTaskData.arrTaskTagCustomFields, 
												arrProjectFields=arguments.strTaskData.arrProjectFields)>
			<cfset local.addTaskSQL = saveTask_update(event=arguments.event, arrTaskTagCustomFields=arguments.strTaskData.arrTaskTagCustomFields, 
												arrProjectFields=arguments.strTaskData.arrProjectFields)>
	
		<!--- add --->
		<cfelse>
			<cfset local.strInsert = saveTask_insert(event=arguments.event, solicitorsList=arguments.strTaskData.solicitorsList,
												arrTaskTags=arguments.strTaskData.arrTaskTags, 
												arrTaskTagCustomFields=arguments.strTaskData.arrTaskTagCustomFields, 
												arrProjectFields=arguments.strTaskData.arrProjectFields, 
												arrExtraFields=arguments.strTaskData.arrExtraFields)>
			
			<cfif local.strInsert.success>
				<cfset local.addTaskSQL = local.strInsert.addTaskSQL>
			<cfelse>
				<cfset local.returnStruct.errMsg = local.strInsert.errMsg>
				<cfset local.returnStruct.success = false>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>

		<!--- save tasks --->
		<cftry>
			<cfquery name="local.qrySaveTask" datasource="#application.dsn.membercentral.dsn#" result="local.qrySaveTaskResults">
				#preserveSingleQuotes(local.addTaskSQL)#
			</cfquery>

			<cfif arguments.event.getValue('taskID',0) eq 0>
				<cfset local.returnStruct.importResultXML = xmlparse(local.qrySaveTask.importResult)>
				<cfset local.returnStruct.errCount = local.qrySaveTask.errCount>
				<cfset local.returnStruct.success = local.returnStruct.errCount eq 0>
			<cfelse>
				<cfset local.returnStruct.success = true>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errMsg = "We were unable to save #arguments.strTaskData.taskFieldLabelPlural#. Try again.">
			<cfset local.tmpErrStr = { sql=replace(local.addTaskSQL,chr(10),"<br/>","ALL") }>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.tmpErrStr)>
		</cfcatch>
		</cftry>

		<cfif arguments.event.getValue('taskID',0) gt 0 and local.returnStruct.success and arrayLen(local.arrTaskChanges)>
			<cfset createObject('component', 'model.system.platform.history').addTaskUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgID'), 
						taskID=arguments.event.getValue('taskID'), actorMemberID=session.cfcuser.memberData.memberID, mainMessage="Task Updated", 
						changes=local.arrTaskChanges)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveTask_insert" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="solicitorsList" type="string" required="true">
		<cfargument name="arrTaskTags" type="array" required="true">
		<cfargument name="arrTaskTagCustomFields" type="array" required="true">
		<cfargument name="arrProjectFields" type="array" required="true">
		<cfargument name="arrExtraFields" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "addTaskSQL":"", "errmsg":"" }>

		<cfset local.prospectAddMode = arguments.event.getValue('prospectAddMode','')>
		<cfswitch expression="#local.prospectAddMode#">
			<cfcase value="single">
				<cfset local.prospectMemberID = val(arguments.event.getValue('prospectMemberID',0))>
			</cfcase>
			<cfcase value="multiple">
				<cfset local.prospectMemberNumberList = arguments.event.getTrimValue('prospectMemberNumberList','')>
				<cfif local.prospectMemberNumberList neq ''>
					<cfset local.crlf = "#Chr(13)##Chr(10)#">
					<cfset local.prospectMemberNumberList = listChangeDelims(arguments.event.getTrimValue('prospectMemberNumberList'),',',local.crlf)>
					<cfset local.prospectMemberNumberList = local.prospectMemberNumberList.listremoveduplicates()>
				</cfif>
			</cfcase>
			<cfcase value="query">
				<!--- if rule has no conditions, dont run rule. --->
				<cfset local.objVGRAdmin = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin")>
				<cfset local.numConditions = local.objVGRAdmin.countRuleConditions(ruleID=arguments.event.getValue('mcproject_ruleID',0))>
				<cfif local.numConditions is 0>
					<cfset local.returnStruct = { "success":false, "errmsg":"No Filter Rule Conditions Found." }>
					<cfreturn local.returnStruct>
				<cfelse>
					<!--- ensure rule is active and cache refreshed. --->
					<cfset local.msg = local.objVGRAdmin.activateRule(ruleID=arguments.event.getValue('mcproject_ruleID'), forceCache=true)>
					<cfif local.msg is not 0>
						<cfset local.returnStruct = { "success":false, "errmsg":"We were unable to activate this Filter Rule." }>
						<cfreturn local.returnStruct>
					</cfif>
				</cfif>
			</cfcase>
		</cfswitch>

		<cfsavecontent variable="local.returnStruct.addTaskSQL">
			<cfoutput>
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				IF OBJECT_ID('tempdb..##tasks_tasksImport') IS NOT NULL 
					DROP TABLE ##tasks_tasksImport;
				IF OBJECT_ID('tempdb..##tasks_tblProspectMembers') IS NOT NULL 
					DROP TABLE ##tasks_tblProspectMembers;
				CREATE TABLE ##tasks_tasksImport (
					[MCProjectID] int NOT NULL,
					[TaskDueDate] varchar(max) NULL,
					[TaskObjective] varchar(max) NULL,
					[TaskStatus] varchar(max) NULL,
					[TaskReminderDate] varchar(max) NULL,
					[Prospect MemberNumber] varchar(max) NULL,
					[Solicitor MemberNumber] varchar(max) NULL,
					<cfloop array="#arguments.arrProjectFields#" index="local.cf">
						[#local.cf.columnName#] varchar(max) NULL,
					</cfloop>
					<cfloop array="#arguments.arrTaskTags#" index="local.taskTag">
						[#local.taskTag.columnName#] varchar(max) NULL,
					</cfloop>
					<cfloop array="#arguments.arrTaskTagCustomFields#" index="local.cf">
						[#local.cf.columnName#] varchar(max) NULL,
					</cfloop>
					<cfloop array="#arguments.arrExtraFields#" index="local.cf">
						[#local.cf.columnName#] varchar(max) NULL,
					</cfloop>
					rowID int NOT NULL IDENTITY(1,1)
				);
				CREATE TABLE ##tasks_tblProspectMembers (memberNumber varchar(max));

				DECLARE @orgID int, @siteID int, @projectID int, @runByMemberID int, @dueDate date, @nextReminderDate date, 
					@prospectMemberID int, @solicitorMemberNumbers varchar(max), @taskStatus varchar(50), @runImmediately bit, 
					@importResult xml, @errCount int;
				DECLARE @tblSolicitorMembers TABLE (memberNumber varchar(50));

				set @orgID = #arguments.event.getValue('mc_siteinfo.orgid')#;
				set @siteID = #arguments.event.getValue('mc_siteinfo.siteid')#;
				set @projectID = #int(val(arguments.event.getValue('projectID')))#;
				set @runByMemberID = #val(session.cfcuser.memberdata.memberid)#;

				<cfif len(arguments.event.getTrimValue('dueDate',''))>
					set @dueDate = '#arguments.event.getValue('dueDate')#';
				</cfif>
				<cfif len(arguments.event.getTrimValue('nextReminderDate',''))>
					set @nextReminderDate = '#arguments.event.getValue('nextReminderDate')#';
				</cfif>

				select @taskStatus = statusName
				from dbo.tasks_statuses 
				where taskStatusID = #int(val(arguments.event.getValue('taskStatus')))#;

				<cfswitch expression="#local.prospectAddMode#">
					<cfcase value="single">
						SET @prospectMemberID = #int(val(local.prospectMemberID))#;

						INSERT INTO ##tasks_tblProspectMembers (memberNumber)
						select memberNumber
						from dbo.ams_members 
						where orgID = @orgID 
						and memberID = @prospectMemberID;

						SET @runImmediately = 1;
					</cfcase>
					<cfcase value="multiple">
						INSERT INTO ##tasks_tblProspectMembers (memberNumber)
						select distinct listItem
						from dbo.fn_varcharListToTableInline('#local.prospectMemberNumberList#',',');

						IF @@ROWCOUNT = 1
							SET @runImmediately = 1;
						ELSE
							SET @runImmediately = 0;
					</cfcase>
					<cfcase value="query">
						DECLARE @ruleID int;

						select @ruleID = sr.ruleID
						from dbo.tasks_projects as p
						inner join dbo.rpt_SavedReports as sr on sr.controllingSiteResourceID = p.siteResourceID
						where p.projectID = @projectID;

						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;
						CREATE TABLE ##tmpVGRMembers (memberID int PRIMARY KEY);

						EXEC dbo.ams_RunVirtualGroupRuleV2 @orgID=@orgID, @ruleID=@ruleID;

						INSERT INTO ##tasks_tblProspectMembers (memberNumber)
						select m.memberNumber
						from ##tmpVGRMembers as tmp
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tmp.memberID;

						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;

						IF @@ROWCOUNT = 1
							SET @runImmediately = 1;
						ELSE
							SET @runImmediately = 0;
					</cfcase>
				</cfswitch>

				-- if no prospects
				IF NOT EXISTS (select 1 from ##tasks_tblProspectMembers) BEGIN
					select @importResult = '<import><errors><error msg="No Prospects found." /></errors></import>';
					GOTO on_done;
				END

				<!--- task assignees --->
				<cfif len(arguments.solicitorsList)>
					INSERT INTO @tblSolicitorMembers (memberNumber)
					select m.memberNumber
					from dbo.fn_intListToTableInline('#arguments.solicitorsList#',',') as tmp
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tmp.listitem
					where m.[status] in ('A','I');

					select @solicitorMemberNumbers = COALESCE(@solicitorMemberNumbers + '|', '') + memberNumber from @tblSolicitorMembers;
				</cfif>

				INSERT INTO ##tasks_tasksImport ([MCProjectID], [TaskDueDate], [TaskObjective], [TaskStatus], [TaskReminderDate], [Prospect MemberNumber],
					<cfloop array="#arguments.arrProjectFields#" index="local.cf">
						[#local.cf.columnName#],
					</cfloop>
					<cfloop array="#arguments.arrTaskTags#" index="local.taskTag">
						[#local.taskTag.columnName#],
					</cfloop>
					<cfloop array="#arguments.arrTaskTagCustomFields#" index="local.cf">
						[#local.cf.columnName#],
					</cfloop>
					<cfloop array="#arguments.arrExtraFields#" index="local.cf">
						[#local.cf.columnName#],
					</cfloop>
					[Solicitor MemberNumber]
				)
				select @projectID, @dueDate, '#replace(arguments.event.getValue('taskDetails',''),"'","''","ALL")#', @taskStatus, @nextReminderDate, memberNumber,
					<cfloop array="#arguments.arrProjectFields#" index="local.cf">
						'#replace(local.cf.columnValue,"'","''","ALL")#',
					</cfloop>
					<cfloop array="#arguments.arrTaskTags#" index="local.taskTag">
						'#replace(local.taskTag.columnValue,"'","''","ALL")#',
					</cfloop>
					<cfloop array="#arguments.arrTaskTagCustomFields#" index="local.cf">
						'#replace(local.cf.columnValue,"'","''","ALL")#',
					</cfloop>
					<cfloop array="#arguments.arrExtraFields#" index="local.cf">
						'#replace(local.cf.columnValue,"'","''","ALL")#',
					</cfloop>
					@solicitorMemberNumbers
				from ##tasks_tblProspectMembers;
				
				-- import tasks
				BEGIN TRY
					set @importResult = null;
					EXEC dbo.tasks_importTasks @siteID=@siteID, @runByMemberID=@runByMemberID, @runImmediately=@runImmediately, @importResult=@importResult OUTPUT;
				END TRY
				BEGIN CATCH
					select @importResult = '<import><errors><error msg="Unable to run import." /><error msg="' + error_message() + '" /></errors></import>';
					GOTO on_done;
				END CATCH
			
				on_done:
				SELECT @errCount = @importResult.value('count(/import/errors/error)','int');
				SELECT @importResult as importResult, @errCount as errCount; 

				IF OBJECT_ID('tempdb..##tasks_tasksImport') IS NOT NULL 
					DROP TABLE ##tasks_tasksImport;
				IF OBJECT_ID('tempdb..##tasks_tblProspectMembers') IS NOT NULL 
					DROP TABLE ##tasks_tblProspectMembers;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
			</cfoutput>
		</cfsavecontent>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveTask_update" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="arrTaskTagCustomFields" type="array" required="true">
		<cfargument name="arrProjectFields" type="array" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.updateTaskSQL">
			<cfoutput>
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				-- need this here so we can go in and out of snapshot as needed inside the transaction
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int, @siteID int, @prospectMemberID int, @enteredByMemberID int, @languageID int, @valueID int, 
					@fieldID int, @detail varchar(max), @taskTags varchar(max), @dataID int, @projectID int, @taskID int, 
					@taskDetails varchar(max), @taskStatusID int, @dueDate datetime, @nextReminderDate date, @payProfileID int;
				
				set @orgID = #arguments.event.getValue('mc_siteinfo.orgid')#;
				set @siteID = #arguments.event.getValue('mc_siteinfo.siteid')#;
				set @languageID = #arguments.event.getValue('mc_pageDefinition.pageLanguageID')#;
				set @taskID = #int(val(arguments.event.getValue('taskID')))#;
				set @projectID = #int(val(arguments.event.getValue('projectID')))#;
				set @enteredByMemberID = #val(session.cfcuser.memberdata.memberid)#;
				<cfif val(arguments.event.getValue('prospectMemberID',0)) gt 0>
					set @prospectMemberID = #int(val(arguments.event.getValue('prospectMemberID')))#;
				</cfif>
				set @taskStatusID = #int(val(arguments.event.getValue('taskStatus')))#;
				<cfif len(arguments.event.getTrimValue('dueDate',''))>
					set @dueDate = '#arguments.event.getValue('dueDate')#';
				</cfif>
				<cfif len(arguments.event.getTrimValue('nextReminderDate',''))>
					set @nextReminderDate = '#arguments.event.getValue('nextReminderDate')#';
				</cfif>
				set @payProfileID = nullif(#int(val(arguments.event.getValue('newPayProfileID',0)))#,0);
				set @taskDetails = '#replace(arguments.event.getValue('taskDetails',''),"'","''","ALL")#';
				set @taskTags = '#arguments.event.getTrimValue('taskTags','')#';
				
				
				BEGIN TRAN;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

					-- update task
					EXEC dbo.tasks_updateTask @taskID=@taskID, @prospectMemberID=@prospectMemberID, @taskStatusID=@taskStatusID, @taskDetails=@taskDetails, 
						@dateDue=@dueDate, @nextReminderDate=@nextReminderDate, @payProfileID=@payProfileID, @enteredByMemberID=@enteredByMemberID;
					
					-- clear any existing task tags
					delete from dbo.tasks_taskCategories where taskID = @taskID;

					-- clear any existing task tag field data
					delete fd
					from dbo.cf_fieldData as fd 
					inner join dbo.cf_fieldValues as fv on fv.valueID = fd.valueID
					where fd.itemID=@taskID
					and fd.itemType='TaskTagCustom';

					<cfif listLen(arguments.event.getTrimValue('taskTags',''))>
						-- add task tags
						insert into dbo.tasks_taskCategories(taskID, categoryID)
						select @taskID, listitem
						from dbo.fn_intListToTable(@taskTags, ',');

						-- add task tag details
						<cfloop array="#arguments.arrTaskTagCustomFields#" index="local.cf">
							<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
								<cfset local.tempSQL = addTask_cf_option(itemType='TaskTagCustom', fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
								#local.tempSQL#
							<cfelseif len(local.cf.value)>
								<cfset local.tempSQL = addTask_cf_nonOption(itemType='TaskTagCustom', fieldID=local.cf.fieldID, customText=local.cf.value)>
								#local.tempSQL#
							</cfif>
						</cfloop>
					</cfif>

					-- update project field details
					<cfloop array="#arguments.arrProjectFields#" index="local.cf">
						<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
							<cfset local.tempSQL = editTask_cf_option(itemType='TaskProjectCustom', taskID=arguments.event.getValue('taskID'), fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
							#local.tempSQL#
						<cfelseif len(local.cf.value)>
							<cfset local.tempSQL = editTask_cf_nonOption(itemType='TaskProjectCustom', taskID=arguments.event.getValue('taskID'), fieldID=local.cf.fieldID, customText=local.cf.value)>
								#local.tempSQL#
						</cfif>
					</cfloop>
				COMMIT TRAN

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.updateTaskSQL>
	</cffunction>

	<cffunction name="addTask_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif len(arguments.customText)>
			<cfsavecontent variable="local.addTaskSQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @dataID = null;

				EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@taskID, @itemType='#arguments.itemType#', 
						@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.addTaskSQL = "">
		</cfif>

		<cfreturn local.addTaskSQL>
	</cffunction>

	<cffunction name="editTask_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="taskID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
			select dataID 
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			and itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">
			and itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
		</cfquery>

		<cfif NOT local.qryGetDataID.recordcount>
			<cfset local.editTaskSQL = addTask_cf_nonOption(itemType=arguments.itemType, fieldID=arguments.fieldID, customText=arguments.customText)>
		<cfelse>
			<cfsavecontent variable="local.editTaskSQL">
				<cfoutput>
				set @dataID = #local.qryGetDataID.dataID#;
					
				<cfif len(arguments.customText)>
					set @detail = '#replace(arguments.customText,"'","''","ALL")#'
					set @fieldID = #arguments.fieldID#;
					set @valueID = null;

					EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null,
						@enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;

					UPDATE dbo.cf_fieldData
					SET valueID = @valueID
					WHERE dataID = @dataID;
				<cfelse>
					DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
				</cfif>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.editTaskSQL>
	</cffunction>

	<cffunction name="addTask_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.addTaskSQL">
			<cfoutput>
			<cfloop list="#arguments.valueIDList#" index="local.valueitem">
				<cfif val(local.valueitem) gt 0>
					set @fieldID = #arguments.fieldID#;
					set @valueID = #val(local.valueitem)#;
					set @dataID = null;
					
					EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@taskID, @itemType='#arguments.itemType#', 
							@valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
				</cfif>
			</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.addTaskSQL>
	</cffunction>
	
	<cffunction name="editTask_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="taskID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">

		<cfset var local = structNew()>

		<!--- existing options --->
		<cfquery name="local.qryExistingOptions" datasource="#application.dsn.membercentral.dsn#">
			select dataID, valueID
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			and itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">
			and itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
		</cfquery>

		<!--- get any options we need to remove --->
		<cfquery name="local.qryOptionsToRemove" dbtype="query">
			select dataID, valueID
			from [local].qryExistingOptions
			<cfif listLen(arguments.valueIDList)>
				where valueID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.valueIDList#" list="true">)
			</cfif>
		</cfquery>
		
		<!--- get any options we need to add --->
		<cfif listLen(arguments.valueIDList)>
			<cfquery name="local.qryOptionsToAdd" datasource="#application.dsn.membercentral.dsn#">
				select valueID
				from dbo.cf_fieldValues
				where fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
				and valueID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.valueIDList#" list="true">)
				<cfif local.qryExistingOptions.recordcount>
					and valueID NOT IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#valueList(local.qryExistingOptions.valueID)#" list="true">)
				</cfif>
			</cfquery>
			<cfset local.optionsToAdd = valueList(local.qryOptionsToAdd.valueID)>
		<cfelse>
			<cfset local.optionsToAdd = "">
		</cfif>

		<cfsavecontent variable="local.editTaskSQL">
			<cfoutput>
			<!--- remove options we dont want --->
			<cfloop query="local.qryOptionsToRemove">
				set @dataID = #val(local.qryOptionsToRemove.dataID)#;
				DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
			</cfloop>

			<!--- add new options. pass in the new options only --->
			<cfif len(local.optionsToAdd)>
				<cfset local.tempSQL = addTask_cf_option(itemType=arguments.itemType, fieldID=arguments.fieldID, valueIDList=local.optionsToAdd)>
				#local.tempSQL#
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.editTaskSQL>
	</cffunction>

	<cffunction name="getTaskUpdateChanges" access="private" output="false" returntype="array">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="arrTaskTagCustomFields" type="array" required="true">
		<cfargument name="arrProjectFields" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.objResourceCustomFields = CreateObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.arrTaskChanges = arrayNew(1)>

		<cfif arguments.event.getValue('dueDate','') NEQ arguments.event.getValue('oldDueDate','')>
			<cfset local.thisChange = { ITEM="Due Date",OLDVALUE=dateformat(arguments.event.getValue('oldDueDate',''),"m/d/yyyy"),NEWVALUE=dateformat(arguments.event.getValue('dueDate',''),"m/d/yyyy") }>
			<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
		</cfif>
		<cfif Compare(arguments.event.getValue('taskDetails',''),arguments.event.getValue('oldTaskDetails',''))>
			<cfset local.thisChange = { ITEM="Objective",OLDVALUE=arguments.event.getValue('oldTaskDetails',''),NEWVALUE=arguments.event.getValue('taskDetails','') }>
			<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
		</cfif>
		<cfif arguments.event.getValue('prospectMemberID','') NEQ arguments.event.getValue('oldProspectMemberID','')>
			<cfset local.oldMemberName = "">
			<cfset local.newMemberName = "">
			<cfset local.qryOldMember = application.objMember.getMemberInfo(memberID=val(arguments.event.getValue('oldProspectMemberID',0)), orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfset local.qryNewMember = application.objMember.getMemberInfo(memberID=val(arguments.event.getValue('prospectMemberID',0)), orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfif local.qryOldMember.recordcount>
				<cfset local.oldMemberName = "#local.qryOldMember.firstname# #local.qryOldMember.lastname# (#local.qryOldMember.membernumber#)">
			</cfif> 
			<cfif local.qryNewMember.recordcount>
				<cfset local.newMemberName = "#local.qryNewMember.firstname# #local.qryNewMember.lastname# (#local.qryNewMember.membernumber#)">
			</cfif> 
			<cfset local.thisChange = { ITEM="Prospect Member",OLDVALUE=local.oldMemberName,NEWVALUE=local.newMemberName }>
			<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
		</cfif>
		<cfif arguments.event.getValue('taskStatus','') NEQ arguments.event.getValue('oldTaskStatus','')>
			<cfset local.oldStatusName = getTaskStatusName(statusID=arguments.event.getValue('oldTaskStatus'))>
			<cfset local.newStatusName = getTaskStatusName(statusID=arguments.event.getValue('taskStatus'))>
			<cfset local.thisChange = { ITEM="Task Status",OLDVALUE=local.oldStatusName,NEWVALUE=local.newStatusName }>
			<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
		</cfif>
		<cfif arguments.event.getValue('nextReminderDate','') NEQ arguments.event.getValue('oldReminderDate','')>
			<cfset local.thisChange = { ITEM="Reminder Date",OLDVALUE=dateformat(arguments.event.getValue('oldReminderDate',''),"m/d/yyyy"),NEWVALUE=dateformat(arguments.event.getValue('nextReminderDate',''),"m/d/yyyy") }>
			<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
		</cfif>

		<cfif arguments.event.getValue('oldPayProfileID',0) neq arguments.event.getValue('newPayProfileID',0)>
			<cfif arguments.event.getValue('oldPayProfileID',0) gt 0>
				<cfset local.oldCardDetail = getProspectCCDetail(memberID=val(arguments.event.getValue('prospectMemberID',0)), payProfileID=arguments.event.getValue('oldPayProfileID'))>
			<cfelse>
				<cfset local.oldCardDetail = "">
			</cfif>
			<cfif arguments.event.getValue('newPayProfileID',0) gt 0>
				<cfset local.newCardDetail = getProspectCCDetail(memberID=val(arguments.event.getValue('prospectMemberID',0)), payProfileID=arguments.event.getValue('newPayProfileID'))>
			<cfelse>
				<cfset local.newCardDetail = "">
			</cfif>
			
			<cfset local.thisChange = { ITEM="Payment Profile",OLDVALUE=local.oldCardDetail,NEWVALUE=local.newCardDetail }>
			<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
		</cfif>

		<cfif listSort(arguments.event.getTrimValue('oldTaskTags',''),'Numeric') NEQ listSort(arguments.event.getTrimValue('taskTags',''),'Numeric')>
			<cfset local.oldTaskTags = getTaskTagNames(siteID=arguments.event.getValue('mc_siteinfo.siteID'), categoryIDList=arguments.event.getTrimValue('oldTaskTags',''))>
			<cfset local.newTaskTags = getTaskTagNames(siteID=arguments.event.getValue('mc_siteinfo.siteID'), categoryIDList=arguments.event.getValue('taskTags',''))>
			<cfset local.thisChange = { ITEM="Task Tags",OLDVALUE=local.oldTaskTags,NEWVALUE=local.newTaskTags }>
			<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
		</cfif>
		
		<cfif listLen(arguments.event.getTrimValue('taskTags',''))>
			<cfloop array="#arguments.arrTaskTagCustomFields#" index="local.cf">
				<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode) and listSort(arguments.event.getValue('old_cf_#local.cf.fieldID#',''),'Numeric') NEQ listSort(local.cf.value,'Numeric')>
					<cfset local.qryOldFieldValues = local.objResourceCustomFields.getFieldOptions(fieldID=local.cf.fieldID, restrictToValueIDList=arguments.event.getValue('old_cf_#local.cf.fieldID#',''))>
					<cfset local.oldFieldValues = local.qryOldFieldValues.recordCount GT 0 ? valueList(local.qryOldFieldValues.fieldValue) : "">
					<cfset local.qryNewFieldValues = local.objResourceCustomFields.getFieldOptions(fieldID=local.cf.fieldID, restrictToValueIDList=local.cf.value)>
					<cfset local.newFieldValues = local.qryNewFieldValues.recordCount GT 0 ? valueList(local.qryNewFieldValues.fieldValue) : "">
					<cfset local.thisChange = { ITEM=local.cf.fieldText,OLDVALUE=local.oldFieldValues,NEWVALUE=local.newFieldValues }>
					<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
				<cfelseif Compare(arguments.event.getTrimValue('old_cf_#local.cf.fieldID#',''),local.cf.value)>
					<cfset local.thisChange = { ITEM=local.cf.fieldText,OLDVALUE=arguments.event.getTrimValue('old_cf_#local.cf.fieldID#',''),NEWVALUE=local.cf.value }>
					<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
				</cfif>
			</cfloop>
		</cfif>

		<cfloop array="#arguments.arrProjectFields#" index="local.cf">
			<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode) and listSort(arguments.event.getValue('old_cf_#local.cf.fieldID#',''),'Numeric') NEQ listSort(local.cf.value,'Numeric')>
				<cfset local.qryOldFieldValues = local.objResourceCustomFields.getFieldOptions(fieldID=local.cf.fieldID, restrictToValueIDList=arguments.event.getValue('old_cf_#local.cf.fieldID#',''))>
				<cfset local.oldFieldValues = local.qryOldFieldValues.recordCount GT 0 ? valueList(local.qryOldFieldValues.fieldValue) : "">
				<cfset local.qryNewFieldValues = local.objResourceCustomFields.getFieldOptions(fieldID=local.cf.fieldID, restrictToValueIDList=local.cf.value)>
				<cfset local.newFieldValues = local.qryNewFieldValues.recordCount GT 0 ? valueList(local.qryNewFieldValues.fieldValue) : "">
				<cfset local.thisChange = { ITEM=local.cf.fieldText,OLDVALUE=local.oldFieldValues,NEWVALUE=local.newFieldValues }>
				<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
			<cfelseif Compare(arguments.event.getTrimValue('old_cf_#local.cf.fieldID#',''),local.cf.value)>
				<cfset local.thisChange = { ITEM=local.cf.fieldText,OLDVALUE=arguments.event.getTrimValue('old_cf_#local.cf.fieldID#',''),NEWVALUE=local.cf.value }>
				<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
			</cfif>
		</cfloop>

		<cfreturn local.arrTaskChanges>
	</cffunction>
	
	<cffunction name="updateViewTask" access="public" output="false" returntype="void" hint="Update task data">
		<cfargument name="taskID" type="numeric" required="true">
		<cfargument name="taskStatusID" type="numeric" required="true">
		
		<cfset var qryUpdate = ''>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdate">
			UPDATE dbo.tasks_tasks
			SET taskStatusID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.taskStatusID#">,
				dateLastModified = getdate()
			WHERE taskID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.taskID#">
		</cfquery>
	</cffunction>

	<cffunction name="removeTask" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="taskID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryTaskProject" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT p.projectID, p.siteResourceID, m.activeMemberID AS prospectMemberID
				FROM dbo.tasks_tasks AS t
				INNER JOIN dbo.tasks_projects AS p ON p.projectID = t.projectID
				LEFT OUTER JOIN dbo.ams_members AS m ON m.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
					AND m.memberID = t.prospectMemberID
				WHERE t.taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfset local.projectRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.qryTaskProject.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

			<cfif NOT (local.projectRights.deleteAllTasks EQ 1 OR (local.projectRights.deleteOwnTasks EQ 1 AND local.qryTaskProject.prospectMemberID EQ session.cfcuser.memberdata.memberID))>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tasks_deleteTask">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.taskID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMemberTaskAdminRights" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TaskAdmin',siteID=arguments.siteID)>
		<cfset local.tasksRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfreturn local.tasksRights>
	</cffunction>

	<cffunction name="addTaskAssignee" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="taskID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="projectSRID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.projectSRID, memberID=arguments.memberID, siteID=arguments.mcproxy_siteID)>

		<cftry>
			<cfif not (local.tmpRights.assignTasksToAnyone or (local.tmpRights.assignTasksToSelf and arguments.memberID eq application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.mcproxy_orgID)))>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tasks_addTaskAssignee">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeTaskAssignee" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="taskID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="projectSRID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.identifiedMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.mcproxy_orgID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.projectSRID, memberID=local.identifiedMemberID, siteID=arguments.mcproxy_siteID)>

		<cftry>
			<cfif not (local.tmpRights.unassignAllTasks or (local.tmpRights.unassignOwnTasks and arguments.memberID eq local.identifiedMemberID))>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="tasks_removeTaskAssignee">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="verifyLoggedInUserIsTaskAssignee" access="public" output="false" returntype="struct">
		<cfargument name="taskID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryTaskAssignee" datasource="#application.dsn.membercentral.dsn#">
			select assigneeID 
			from dbo.tasks_taskAssignees 
			where taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">
			and memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
		</cfquery>

		<cfif local.qryTaskAssignee.recordcount>
			<cfset local.data.success = true>
		<cfelse>
			<cfset local.data.msg = "You do not have rights to add task notes.">
			<cfset local.data.success = false>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="manageFilteredTasks" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = {}>
		<cfset local.fMemberID = arguments.event.getValue('memberID',0)>

		<cfif ListFindNoCase('allTasks,myTasks,memberTasks,projectTasks',arguments.mode)>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"dateLastModified #arguments.event.getValue('orderDir')#, taskStatus #arguments.event.getValue('orderDir')#, prospectMemberName #arguments.event.getValue('orderDir') EQ 'DESC' ? 'ASC' : 'DESC'#")>
			<cfset arrayAppend(local.arrCols,"prospectMemberName #arguments.event.getValue('orderDir')#, dateEntered #arguments.event.getValue('orderDir')#, dateDue #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"taskStatus #arguments.event.getValue('orderDir')#")>
			<cfif arguments.mode NEQ 'projectTasks'>
				<cfset arrayAppend(local.arrCols,"projectName #arguments.event.getValue('orderDir')#")>
			</cfif>
			
			<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>
		</cfif>

		<cfset local.strItemIDSQLVariable = { TaskProjectCustom='t.taskID', TaskTagCustom="t.taskID" }>
		<cfset local.strFields = createObject("component","model.admin.common.modules.customFields.customFields").generateFieldFilterSQL(rc=arguments.event.getCollection(), fieldIDPrefix='mctaskFieldID', 
				fieldExpPrefix='TF', strItemIDSQLVariable=local.strItemIDSQLVariable)>

		<cfset local.fHasCardOnFile = arguments.event.getValue('fHasCard','')>

		<cfif ListFindNoCase('allTasks,myTasks,memberTasks,projectTasks,massDeleteTasksCount,massDeleteTasks',arguments.mode)>
			<cfset local.deleteOwnTasksRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Project", functionName="deleteOwnTasks")>
			<cfset local.deleteAllTasksRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Project", functionName="deleteAllTasks")>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.qryTasks" result="local.qryTaskResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tblTaskSearch') IS NOT NULL 
					DROP TABLE ##tblTaskSearch;
				CREATE TABLE ##tblTaskSearch (taskID int PRIMARY KEY);

				DECLARE @siteID int, @orgID int, @projectID int, @workspaceID int, @enteredDateFrom date, @enteredDateTo datetime, @dateDueFrom date, @dateDueTo datetime, 
						@lastModifiedFrom date, @lastModifiedTo datetime, @fMemberID int, @taskStatusID int, @fProspectMemberID int, @fProspectGroupID int, 
						@fSolicitorMemberID int, @fSolicitorGroupID int;
				#PreserveSingleQuotes(local.strFields.filterSetupSQL)#

				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				<cfif arguments.event.getValue('fWorkspaceID',0) GT 0>
					SET @workspaceID = <cfqueryparam value="#arguments.event.getValue('fWorkspaceID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fProjectID',0) GT 0>
					SET @projectID = <cfqueryparam value="#arguments.event.getValue('fProjectID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getTrimValue('fDateEnteredFrom','') NEQ ''>
					SET @enteredDateFrom = <cfqueryparam value="#arguments.event.getTrimValue('fDateEnteredFrom')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				<cfif arguments.event.getTrimValue('fDateEnteredTo','') NEQ ''>
					SET @enteredDateTo = <cfqueryparam value="#arguments.event.getTrimValue('fDateEnteredTo')# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">;
				</cfif>
				<cfif arguments.event.getTrimValue('fDueDateFrom','') NEQ ''>
					SET @dateDueFrom = <cfqueryparam value="#arguments.event.getTrimValue('fDueDateFrom')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				<cfif arguments.event.getTrimValue('fDueDateTo','') NEQ ''>
					SET @dateDueTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('fDueDateTo')# 23:59:59.997">;
				</cfif>
				<cfif arguments.event.getTrimValue('fLastModifiedFrom','') NEQ ''>
					SET @lastModifiedFrom = <cfqueryparam value="#arguments.event.getTrimValue('fLastModifiedFrom')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				<cfif arguments.event.getTrimValue('fLastModifiedTo','') NEQ ''>
					SET @lastModifiedTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getTrimValue('fLastModifiedTo')# 23:59:59.997">;
				</cfif>
				<cfif local.fMemberID gt 0>
					SET @fMemberID = <cfqueryparam value="#local.fMemberID#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fTaskStatus',0) gt 0>
					SET @taskStatusID = <cfqueryparam value="#arguments.event.getValue('fTaskStatus')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fProspectMemberID',0) gt 0>
					SET @fProspectMemberID = <cfqueryparam value="#arguments.event.getValue('fProspectMemberID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fProspectGroupID',0) gt 0>
					SET @fProspectGroupID = <cfqueryparam value="#arguments.event.getValue('fProspectGroupID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fSolicitorMemberID',0) gt 0>
					SET @fSolicitorMemberID = <cfqueryparam value="#arguments.event.getValue('fSolicitorMemberID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fSolicitorGroupID',0) gt 0>
					SET @fSolicitorGroupID = <cfqueryparam value="#arguments.event.getValue('fSolicitorGroupID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>

				INSERT INTO ##tblTaskSearch 
				select distinct t.taskID
				from dbo.tasks_tasks as t
				inner join dbo.tasks_projects as p on p.projectID = t.projectID
				inner join dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
				inner join dbo.cms_siteResources as sr on t.siteResourceID = sr.siteResourceID and sr.siteID = @siteID
				inner join dbo.cms_siteResourceStatuses as srs on sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
				<cfif arguments.event.getValue('fProspectMemberID',0) gt 0>
					inner join dbo.ams_members as mt on mt.orgID = @orgID and mt.memberID = t.prospectMemberID
					inner join dbo.ams_members as mt2 on mt2.orgID = @orgID and mt2.memberID = mt.activeMemberID and mt2.memberID = @fProspectMemberID
				<cfelse>
					left outer join dbo.ams_members as mt on mt.orgID = @orgID and mt.memberID = t.prospectMemberID
					left outer join dbo.ams_members as mt2 on mt2.orgID = @orgID and mt2.memberID = mt.activeMemberID
				</cfif>
				<cfif arguments.event.getValue('fProspectGroupID',0) gt 0>
					inner join dbo.cache_members_groups as mg on mg.orgID = @orgID 
						and mg.memberID = mt2.memberID 
						AND mg.groupid = @fProspectGroupID
				</cfif>
				<cfif arguments.event.getValue('fSolicitorMemberID',0) gt 0>
					inner join dbo.tasks_taskAssignees as ta on ta.taskID = t.taskID
					inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID and tar.roleCode = 'assignee'
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = ta.memberID
					inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID and mActive.memberID = @fSolicitorMemberID
				<cfelseif arguments.event.getValue('fSolicitorGroupID',0) gt 0>
					inner join dbo.tasks_taskAssignees as ta on ta.taskID = t.taskID
					inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID and tar.roleCode = 'assignee'
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = ta.memberID
					inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
					inner join dbo.cache_members_groups as mgAssigned on mgAssigned.orgID = @orgID 
						and mgAssigned.memberID = mActive.memberID 
						AND mgAssigned.groupid = @fSolicitorGroupID
				</cfif>
				<cfif local.fMemberID is 0>
					where 1=1
				<cfelse>
					inner join dbo.ams_members as expMembers on expMembers.orgID = @orgID and expMembers.memberID = @fMemberID
					inner join dbo.ams_members as expMembers2 on expMembers2.orgID = @orgID and expMembers2.activeMemberID = expMembers.activeMemberID
					where (
						t.prospectMemberID = expMembers2.memberID
						or exists (select 1 
							from dbo.tasks_tasks
							inner join dbo.tasks_taskAssignees as ta on ta.taskID = t.taskID
							inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID and tar.roleCode = 'assignee'
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = ta.memberID
							inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID and mActive.memberID = expMembers2.memberID
						)
					)
				</cfif>
				<cfif arguments.event.getValue('fWorkspaceID',0) GT 0>
					and w.workspaceID = @workspaceID
				</cfif>
				<cfif arguments.event.getValue('fProjectID',0) GT 0>
					and t.projectID = @projectID
				</cfif>
				<cfif arguments.event.getTrimValue('fDateEnteredFrom','') NEQ ''>
					and t.dateEntered >= @enteredDateFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fDateEnteredTo','') NEQ ''>
					and t.dateEntered <= @enteredDateTo
				</cfif>
				<cfif arguments.event.getTrimValue('fDueDateFrom','') NEQ ''>
					and t.dateDue >= @dateDueFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fDueDateTo','') NEQ ''>
					and t.dateDue <= @dateDueTo
				</cfif>
				<cfif arguments.event.getTrimValue('fLastModifiedFrom','') NEQ ''>
					and t.dateLastModified >= @lastModifiedFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fLastModifiedTo','') NEQ ''>
					and t.dateLastModified <= @lastModifiedTo
				</cfif>
				<cfif arguments.event.getValue('fTaskStatus',0) gt 0>
					and t.taskStatusID = @taskStatusID
				</cfif>
				<cfif ListLen(arguments.event.getValue('fWorkspaceTag',''))>
					and exists (select 1 
								from dbo.tasks_taskCategories 
								where taskID = t.taskID
								and categoryID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fWorkspaceTag')#" list="true">))
				</cfif>
				<cfif len(local.fHasCardOnFile)>
					<cfif local.fHasCardOnFile eq 'Y'>
						and t.payProfileID IS NOT NULL
					<cfelseif local.fHasCardOnFile eq 'N'>
						and t.payProfileID IS NULL
					</cfif>
				</cfif>
				#PreserveSingleQuotes(local.strFields.filterSQL)#;

				<cfif ListFindNoCase('allTasks,myTasks,memberTasks,projectTasks,massDeleteTasksCount,massDeleteTasks',arguments.mode)>
					IF OBJECT_ID('tempdb..##tblTaskProjects') IS NOT NULL 
						DROP TABLE ##tblTaskProjects;
					CREATE TABLE ##tblTaskProjects (projectID int PRIMARY KEY, siteResourceID int, deleteOwnTaskRights bit, deleteAllTaskRights bit);

					DECLARE @memberID int, @groupPrintID int, @deleteOwnTasksRFID int, @deleteAllTasksRFID int,
						@totalFilteredTasks int, @deleteTasksCount int;

					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					SELECT @groupPrintID = groupPrintID
					FROM dbo.ams_members
					WHERE memberID = @memberID
					AND orgID IN (@orgID,1);

					SET @deleteOwnTasksRFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.deleteOwnTasksRFID#">;
					SET @deleteAllTasksRFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.deleteAllTasksRFID#">;

					INSERT INTO ##tblTaskProjects (projectID, siteResourceID, deleteOwnTaskRights, deleteAllTaskRights)
					SELECT DISTINCT p.projectID, p.siteResourceID, 0, 0
					FROM ##tblTaskSearch AS tmp
					INNER JOIN dbo.tasks_tasks AS t ON t.taskID = tmp.taskID
					INNER JOIN dbo.tasks_projects AS p ON p.projectID = t.projectID;

					UPDATE tmp
					SET tmp.deleteOwnTaskRights = 1
					FROM ##tblTaskProjects AS tmp
					INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON srfrp.siteID = @siteID 
						AND srfrp.siteResourceID = tmp.siteResourceID
						AND srfrp.functionID = @deleteOwnTasksRFID
					INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON gprp.siteID = @siteID
						AND gprp.rightPrintID = srfrp.rightPrintID
						AND gprp.groupPrintID = @groupPrintID;

					UPDATE tmp
					SET tmp.deleteAllTaskRights = 1
					FROM ##tblTaskProjects AS tmp
					INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON srfrp.siteID = @siteID 
						AND srfrp.siteResourceID = tmp.siteResourceID
						AND srfrp.functionID = @deleteAllTasksRFID
					INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON gprp.siteID = @siteID
						AND gprp.rightPrintID = srfrp.rightPrintID
						AND gprp.groupPrintID = @groupPrintID;
				</cfif>

				<cfif arguments.mode is 'taskTabEmailGrid'>
					DECLARE @posStart int, @posStartAndCount int, @emailTagTypeID int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					<cfif arguments.event.getValue('recipientMode','') is 'prospect'>
						SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
					</cfif>
					SET @emailTagTypeID = <cfqueryparam value="#arguments.event.getValue('emailTagType',0)#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tblTasks') IS NOT NULL 
						DROP TABLE ##tblTasks;
					CREATE TABLE ##tblTasks (taskID int, prospectMemberID int, prospectMemberFirstname varchar(75), prospectMemberLastname varchar(75), prospectMembernumber varchar(50),
						prospectMemberCompany varchar(200), prospectMemberEmail varchar(400), solicitorMemberID int, solicitorMemberFirstname varchar(75), solicitorMemberLastName varchar(75), 
						solicitorMemberNumber varchar(50), solicitorMemberCompany varchar(200), solicitorMemberEmail varchar(400), row int);

					INSERT INTO ##tblTasks
					select distinct t.taskID, mActive.memberID, mActive.firstname, mActive.lastname, mActive.membernumber, mActive.company, me.email, 
						mAssignedActive.memberID, mAssignedActive.firstname, mAssignedActive.lastname, mAssignedActive.membernumber, mAssignedActive.company, mAssignedMemEmail.email, 
						<cfif arguments.event.getValue('recipientMode','') is 'solicitor'> DENSE_RANK()<cfelse> ROW_NUMBER()</cfif> OVER 
							(ORDER BY mActive.lastname #arguments.event.getValue('orderDir')#, mActive.firstname #arguments.event.getValue('orderDir')#, mActive.memberNumber #arguments.event.getValue('orderDir')#, t.taskID #arguments.event.getValue('orderDir')#) as row
					from ##tblTaskSearch as tmp
					inner join dbo.tasks_tasks as t on t.taskID = tmp.taskID
					inner join dbo.ams_members as m ON m.orgID = @orgID and m.memberID = t.prospectMemberID
					inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberID = mActive.memberID
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = me.memberID and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagType = 'Primary'
					left outer join dbo.tasks_taskAssignees as ta 
						inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID and tar.roleCode = 'assignee'
						on ta.taskID = t.taskID
					left outer join dbo.ams_members as mAssigned 
						inner join dbo.ams_members as mAssignedActive on mAssignedActive.orgID = @orgID and mAssignedActive.memberID = mAssigned.activeMemberID
						on mAssigned.orgID = @orgID and mAssigned.memberID = ta.memberID
					left outer join dbo.ams_memberEmails as mAssignedMemEmail
						inner join dbo.ams_memberEmailTags as mAssigned_metag on mAssigned_metag.orgID = @orgID 
							and mAssigned_metag.memberID = mAssignedMemEmail.memberID 
							and mAssigned_metag.emailTypeID = mAssignedMemEmail.emailTypeID
						inner join dbo.ams_memberEmailTagTypes as mAssigned_metagt on mAssigned_metagt.orgID = @orgID
							and mAssigned_metagt.emailTagTypeID = mAssigned_metag.emailTagTypeID
							and mAssigned_metagt.emailTagTypeID = @emailTagTypeID
						on mAssignedMemEmail.orgID = @orgID
						and mAssignedMemEmail.memberID = mAssignedActive.memberID
					where 1=1
					<cfif arguments.event.getValue('recipientMode','') eq 'solicitor'>
						and mAssignedActive.memberID is not null
					</cfif>;

					select @totalCount = max(row) from ##tblTasks;

					select taskID, prospectMemberID, prospectMemberFirstname, prospectMemberLastname, prospectMembernumber, prospectMemberCompany, prospectMemberEmail, 
						solicitorMemberID, solicitorMemberFirstname, solicitorMemberLastName, solicitorMemberNumber, solicitorMemberCompany, solicitorMemberEmail, 
						row, @totalCount as totalCount
					from ##tblTasks
					where row > @posStart
					and row <= isnull(@posStartAndCount,@totalCount)
					order by row;

					IF OBJECT_ID('tempdb..##tblTasks') IS NOT NULL 
						DROP TABLE ##tblTasks;
				<cfelseif ListFindNoCase('allTasks,myTasks,memberTasks,projectTasks',arguments.mode)>
					DECLARE @posStart int, @posStartAndCount int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tblTasks') IS NOT NULL 
						DROP TABLE ##tblTasks;

					SELECT taskID, dateEntered, dateDue, dateLastModified, projectName, taskStatus, contentToolTip, prospectMemberNumber, prospectMemberCompany, 
						prospectFirstName, prospectLastName, prospectMemberName, prospectMemberID, requestPayProfile, taskPayProfileID, assigneeCount, 
						deleteTaskQueueItemID, hasDeleteTaskRights, ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
					INTO ##tblTasks
					FROM (
						select t.taskID, t.dateEntered, t.dateDue, t.dateLastModified, cl.contentTitle as projectName, s.statusName as taskStatus,
							REPLACE(REPLACE(REPLACE(taskContent.rawContent, CHAR(13) + CHAR(10), '<br>'), CHAR(13), '<br>'), CHAR(10), '<br>') as contentToolTip,
							mt2.membernumber as prospectMemberNumber, mt2.company as prospectMemberCompany,
							mt2.firstname as prospectFirstName, mt2.lastname as prospectLastName, mt2.firstName + ' ' + mt2.lastname as prospectMemberName, mt2.memberID as prospectMemberID, 
							p.requestPayProfile, isnull(t.payProfileID,0) as taskPayProfileID,
							isnull((select count(ta.assigneeID) 
									from dbo.tasks_taskAssignees as ta 
									inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID
									where ta.taskID = t.taskID
									and tar.roleCode = 'assignee'),0) as assigneeCount,
							qid.itemID as deleteTaskQueueItemID, case when tp.projectID is not null then 1 else 0 end as hasDeleteTaskRights
						from ##tblTaskSearch as tmp
						inner join dbo.tasks_tasks as t on t.taskID = tmp.taskID
						inner join dbo.tasks_projects as p on p.projectID = t.projectID
						inner join dbo.cms_contentLanguages as cl on cl.contentID = p.projectContentID and cl.languageID = 1
						inner join dbo.tasks_statuses as s on s.taskStatusID = t.taskStatusID
						inner join dbo.cms_siteResources sr on t.siteResourceID = sr.siteResourceID and sr.siteID = @siteID
						inner join dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
						left outer join dbo.ams_members as mt on mt.orgID = @orgID and mt.memberID = t.prospectMemberID
						left outer join dbo.ams_members as mt2 on mt2.orgID = @orgID and mt2.memberID = mt.activeMemberID
						left outer join platformQueue.dbo.queue_taskDelete as qid on qid.siteID = @siteID and qid.taskID = t.taskID
						left outer join ##tblTaskProjects as tp on tp.projectID = p.projectID
							and (tp.deleteAllTaskRights = 1 or (tp.deleteOwnTaskRights = 1 AND mt2.memberID = @memberID))
						cross apply dbo.fn_getContent(t.taskContentID,1) as taskContent
					) tmpOuter;

					select @totalCount = @@ROWCOUNT;

					select taskID, dateEntered, dateDue, dateLastModified, projectName, taskStatus, contentToolTip, prospectMemberNumber, prospectMemberCompany,prospectFirstName, prospectLastName, 
						prospectMemberName, prospectMemberID, requestPayProfile, taskPayProfileID, assigneeCount, deleteTaskQueueItemID, hasDeleteTaskRights, @totalCount as totalCount
					from ##tblTasks
					where row > @posStart
					and row <= @posStartAndCount
					order by row;

					IF OBJECT_ID('tempdb..##tblTasks') IS NOT NULL 
						DROP TABLE ##tblTasks;
				<cfelseif arguments.mode is 'taskTabEmail'>
					DECLARE @prospectsWithEmail int, @solicitorsWithEmail int, @emailTagTypeID int;
					SET @emailTagTypeID = <cfqueryparam value="#arguments.event.getValue('emailTagType',0)#" cfsqltype="CF_SQL_INTEGER">;

					select @prospectsWithEmail = count(*)
					from ##tblTaskSearch as tmp
					inner join dbo.tasks_tasks as t on t.taskID = tmp.taskID
					inner join dbo.ams_members as m ON m.orgID = @orgID and m.memberID = t.prospectMemberID
					inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberID = mActive.memberID and me.email <> ''
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID and metag.memberID = me.memberID and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagTypeID = @emailTagTypeID;

					select @solicitorsWithEmail = count(*)
					from ##tblTaskSearch as tmp
					inner join dbo.tasks_tasks as t on t.taskID = tmp.taskID
					inner join dbo.tasks_taskAssignees as ta on ta.taskID = t.taskID
					inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID and tar.roleCode = 'assignee'
					inner join dbo.ams_members as m ON m.orgID = @orgID and m.memberID = ta.memberID
					inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberID = mActive.memberID and me.email <> ''
					inner join dbo.ams_memberEmailTags as metag on metag.orgId = @orgID and metag.memberID = me.memberID and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID and metagt.emailTagTypeID = metag.emailTagTypeID and metagt.emailTagTypeID = @emailTagTypeID;

					select taskID, @prospectsWithEmail as prospectsWithEmail, @solicitorsWithEmail as solicitorsWithEmail
					from ##tblTaskSearch;
				<cfelseif ListFindNoCase('taskTabDownload,massEditTask',arguments.mode)>
					select taskID 
					from ##tblTaskSearch;
				<cfelseif arguments.mode EQ 'massDeleteTasksCount'>
					SELECT @totalFilteredTasks = COUNT(taskID) FROM ##tblTaskSearch;

					SELECT @deleteTasksCount = COUNT(t.taskID)
					FROM ##tblTaskSearch AS tmp
					INNER JOIN dbo.tasks_tasks AS t ON t.taskID = tmp.taskID
					INNER JOIN ##tblTaskProjects AS p ON p.projectID = t.projectID
					INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID 
						AND m.memberID = t.prospectMemberID
					WHERE p.deleteAllTaskRights = 1
					OR (p.deleteOwnTaskRights = 1 AND m.activeMemberID = @memberID);

					SELECT @totalFilteredTasks AS totalFilteredTasks, @deleteTasksCount AS deleteTasksCount;
				<cfelseif arguments.mode EQ 'massDeleteTasks'>
					DECLARE @taskIDList varchar(max);

					SELECT @taskIDList = STRING_AGG(t.taskID,',')
					FROM ##tblTaskSearch AS tmp
					INNER JOIN dbo.tasks_tasks AS t ON t.taskID = tmp.taskID
					INNER JOIN ##tblTaskProjects AS p ON p.projectID = t.projectID
					INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID 
						AND m.memberID = t.prospectMemberID
					WHERE p.deleteAllTaskRights = 1
					OR (p.deleteOwnTaskRights = 1 AND m.activeMemberID = @memberID);

					IF len(@taskIDList) > 0
						EXEC dbo.tasks_deleteTask @siteID=@siteID, @taskIDList=@taskIDList;

					SELECT 1 AS success;
				</cfif>

				IF OBJECT_ID('tempdb..##tblTaskSearch') IS NOT NULL 
					DROP TABLE ##tblTaskSearch;
				<cfif ListFindNoCase('allTasks,myTasks,memberTasks,projectTasks,massDeleteTasksCount,massDeleteTasks',arguments.mode)>
					IF OBJECT_ID('tempdb..##tblTaskProjects') IS NOT NULL 
						DROP TABLE ##tblTaskProjects;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfif NOT ListFindNoCase('massDeleteTasksCount,massDeleteTasks',arguments.mode)>
			<cfset local.returnedTaskIDs = listRemoveDuplicates(valuelist(local.returnStruct.qryTasks.taskID))>
			<cfset local.returnStruct.taskAssignees = {}>

			<cfif listlen(local.returnedTaskIDs)>
				<cfquery name="local.taskAssignees" datasource="#application.dsn.memberCentral.dsn#" returntype="array">
					set nocount on
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					declare @taskIDList varchar(max) = <cfqueryparam value="#local.returnedTaskIDs#" cfsqltype="CF_SQL_VARCHAR">;
					declare @tempTasks TABLE (taskID int PRIMARY KEY);

					insert into @tempTasks (taskID)
					select listitem as TaskID
					from dbo.fn_intListToTable(@taskIDList,',');

					select t.taskID, mActive.membernumber, mActive.memberID, mActive.firstname, mActive.lastname, isnull(mActive.company,'') as company
					from @tempTasks t
					inner join dbo.tasks_taskAssignees as ta on ta.taskID = t.taskID
					inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID and tar.roleCode = 'assignee'
					inner join dbo.ams_members as m ON m.memberID = ta.memberID
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
					order by mActive.lastname, mActive.firstname, mActive.company;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfscript>
					var tempTaskAssignees = {};
					if (arrayLen(local.taskAssignees)) {
						arrayEach(local.taskAssignees, function(element,index) {
							if (not structKeyExists(tempTaskAssignees,element.taskID))
								tempTaskAssignees[element.taskID] = [];
							arrayAppend(tempTaskAssignees[element.taskID],element);
						});
					}
					local.returnStruct.taskAssignees = tempTaskAssignees;
				</cfscript>
			</cfif>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getTaskDataForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="taskIDList" type="string" required="true">
		<cfargument name="recipientMode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.retStruct.extendedLinkedMergeCode = "">
		<cfset local.retStruct.arrResTypeMergeCodes = arrayNew(1)>

		<cfset local.taskDataArray = listToArray(arguments.taskIDList, "|")>
		<cfset local.taskIDList = local.taskDataArray[1]>
		<cfset local.retStruct.recipientID = val(local.taskDataArray[2])>

		<cfif arguments.recipientMode is 'solicitor'>
			<cfset local.retStruct.qryData = getTaskDetailsForSummary(taskIDList=local.taskIDList)>
			<cfset local.retStruct.taskList = getTaskListDetails(qryTasks=local.retStruct.qryData)>
		<cfelse>
			<cfset local.retStruct.qryData = getTaskDetails(taskID=val(local.taskIDList))>
			<cfset local.retStruct.taskSolicitorList = getTaskSolicitorsList(taskID=val(local.taskIDList), projectID=local.retStruct.qryData.projectID)>
			<cfset local.retStruct.taskSolicitor = getTaskSolicitorName(taskID=val(local.taskIDList))>

			<cfset local.retStruct.taskData = { projectName=local.retStruct.qryData.projectName, projectDescription=local.retStruct.qryData.projectDescription, 
												workspace=local.retStruct.qryData.workspace, assigneeInstructions=local.retStruct.qryData.assigneeInstructions, 
												dueDate=DateFormat(local.retStruct.qryData.dateDue,'m/d/yyyy'), taskObjective=local.retStruct.qryData.taskObjective, 
												taskStatus=local.retStruct.qryData.statusName, taskID=local.retStruct.qryData.taskID, 
												applicationInstanceID=local.retStruct.qryData.applicationInstanceID, projectID=local.retStruct.qryData.projectID,
												prospectMemberNumber=local.retStruct.qryData.prospectMemberNumber, taskSolicitors=local.retStruct.taskSolicitorList, taskSolicitor=local.retStruct.taskSolicitor }>
			<cfset local.retStruct.taskData["strTaskProjectFieldData"] = getTaskProjectFieldData(taskID=local.retStruct.qryData.taskID)>
			<cfset local.retStruct.taskData["strTaskTags"] = getTaskTagDetails(taskID=local.retStruct.qryData.taskID)>
			<cfset local.retStruct.taskData["strTaskTagFields"] = getTaskTagsFieldData(taskID=local.retStruct.qryData.taskID)>
			<cfset local.retStruct.taskData["strWorkspaceProjectFieldData"] = createObject("component","model.admin.projects.project").getWorkspaceProjectFieldData(projectID=local.retStruct.qryData.projectID)>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getFilteredTasksForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">
		<cfargument name="recipientMode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = { itemIDList='', toolType='TaskAdmin', catTreeCode='ETTASKS', extendedLinkedMergeCode='', extraMergeTagList='taskList|taskProspectURL|taskProjectURL|taskSolicitors', errorCode='' }>
		
		<cfset local.strTasks = manageFilteredTasks(event=arguments.event, mode='taskTabEmail')>
		<cfset local.qryTask = local.strTasks.qryTasks>

		<cfif local.qryTask.recordcount is 0>
			<cfset local.retStruct.errorCode = 'norecipient'>
			<cfreturn local.retStruct>
		<cfelse>
			<cfset local.retStruct.itemIDList = valueList(local.qryTask.taskID)>
		</cfif>

		<cfif arguments.event.getValue('ntLst',1) eq 4>
			<cfset local.retStruct.operationMode = "projectTasks">
		</cfif>

		<!--- no email ids defined --->
		<cfif (arguments.recipientMode eq 'prospect' and val(local.qryTask.prospectsWithEmail) is 0) OR (arguments.recipientMode eq 'solicitor' and val(local.qryTask.solicitorsWithEmail) is 0)>
			<cfset local.data = showMessage(errorCode='noemailrecipient')>
			<cfset local.retStruct.errorCode = 'noemailrecipient'>
			<cfreturn local.retStruct>
		</cfif>

		<cfif arguments.recipientMode eq 'solicitor'>
			<cfset local.retStruct.qryTaskDetails = getTaskDetailsForSummary(taskIDList=local.retStruct.itemIDList)>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getTaskDetails" access="public" output="no" returntype="query">
		<cfargument name="taskID" type="numeric" required="yes">

		<cfset var qryTask = "">

		<cfquery name="qryTask" datasource="#application.dsn.membercentral.dsn#">
			select t.taskID, t.prospectMemberID as memberID, t.dateDue, s.statusName, 
				ai.applicationInstanceName as workspace, taskContent.rawcontent as taskObjective, 
				projectContent.contentTitle as projectName, projectContent.rawcontent as projectDescription, 
				projectInstructionContent.rawcontent as assigneeInstructions, w.applicationInstanceID, p.projectID,
				mAssocActive.membernumber AS prospectMemberNumber, mAssocActive.firstName + ' ' + mAssocActive.lastname as prospectMemberName, t.payProfileID
			from dbo.tasks_tasks as t
			inner join dbo.tasks_projects as p on p.projectID = t.projectID
			inner join dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID 
				and at.applicationTypeName = 'workspace'
			inner join dbo.tasks_statuses as s on s.taskStatusID = t.taskStatusID
			left outer join dbo.ams_members as mAssoc on mAssoc.memberID = t.prospectMemberID
			left outer join dbo.ams_members as mAssocActive on mAssocActive.memberID = mAssoc.activeMemberID
			left outer join dbo.organizations as o on o.orgId = mAssocActive.orgID
			cross apply dbo.fn_getContent(t.taskContentID,1) as taskContent
			cross apply dbo.fn_getContent(p.projectContentID,1) as projectContent
			cross apply dbo.fn_getContent(p.instructionContentID,1) as projectInstructionContent
			where t.taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">
		</cfquery>

		<cfreturn qryTask>
	</cffunction>

	<cffunction name="getTaskDetailsForSummary" access="private" output="no" returntype="query">
		<cfargument name="taskIDList" type="string" required="yes">

		<cfset var qryTaskDetails = "">

		<cfquery name="qryTaskDetails" datasource="#application.dsn.membercentral.dsn#">
			select t.taskID, t.prospectMemberID as memberID, t.dateDue, t.datelastmodified, s.statusName, project.contentTitle as projectTitle, p.projectID,
				w.applicationInstanceID,
				mAssocActive.membernumber, mAssocActive.firstname as memberFirstname, mAssocActive.lastname as memberLastname, mAssocActive.company as memberCompany,
				mAssignedActive.memberID as solicitorMemberID, mAssignedActive.membernumber as solicitorMemberNumber, 
				mAssignedActive.firstname as solicitorMemberFirstname, mAssignedActive.lastname as solicitorMemberLastName, mAssignedActive.company as solicitormemberCompany
			from dbo.tasks_tasks as t
			inner join dbo.tasks_projects as p on p.projectID = t.projectID
			inner join dbo.tasks_workspaces w on w.workspaceID = p.workspaceID
			inner join dbo.tasks_statuses as s on s.taskStatusID = t.taskStatusID
			left outer join dbo.tasks_taskAssignees as ta 
				inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID and tar.roleCode = 'assignee'
				on ta.taskID = t.taskID
			left outer join dbo.ams_members as mAssigned on mAssigned.memberID = ta.memberID
			left outer join dbo.ams_members as mAssignedActive on mAssignedActive.memberID = mAssigned.activeMemberID
			left outer join dbo.ams_members as mAssoc on mAssoc.memberID = t.prospectMemberID
			left outer join dbo.ams_members as mAssocActive on mAssocActive.memberID = mAssoc.activeMemberID
			cross apply dbo.fn_getContent(p.projectContentID,1) as project
			where t.taskID in (<cfqueryparam value="0#arguments.taskIDList#" cfsqltype="CF_SQL_INTEGER" list="true">)
			order by project.contentTitle, t.datelastmodified
		</cfquery>

		<cfreturn qryTaskDetails>
	</cffunction>

	<cffunction name="getTaskListDetails" access="public" output="false" returntype="string">
		<cfargument name="qryTasks" type="query" required="true">

		<cfset var taskDetails = "">

		<cfsavecontent variable="taskDetails">
			<cfoutput>
				<cfif arguments.qryTasks.recordcount gt 0>
					<hr>
					<b>Your Assigments</b><br><br>
				</cfif>
			</cfoutput>
			<cfoutput query="arguments.qryTasks" group="taskID">
				<cfset local.baseWorkspaceLink = application.objApplications.getAppBaseLink(applicationInstanceID=arguments.qryTasks.applicationInstanceID,siteid=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteid)>
				<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgcode, membernumber=arguments.qryTasks.solicitorMemberNumber)>
				<a target="_blank" href="#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname#/?#local.baseWorkspaceLink#&wsAction=showTaskDetail&pid=#arguments.qryTasks.projectID#&tid=#arguments.qryTasks.taskID#&mk=#local.memberKey#">#arguments.qryTasks.memberFirstname# #arguments.qryTasks.memberLastname# (#arguments.qryTasks.membercompany#)</a><br>
				Status: #arguments.qryTasks.statusName#<br/>
				Project: #arguments.qryTasks.projectTitle#<br/>
				<cfif len(arguments.qryTasks.dateDue)>Due Date: #dateFormat(arguments.qryTasks.dateDue,'m/d/yyyy')#<br/></cfif>
				<cfoutput>
					<cfif len(arguments.qryTasks.solicitorMemberNumber)>Assigned: #arguments.qryTasks.solicitorMemberFirstname# #arguments.qryTasks.solicitorMemberLastName# (#arguments.qryTasks.solicitormemberCompany#)<br/></cfif>
				</cfoutput>
				<br/>
			</cfoutput>
		</cfsavecontent>

		<cfreturn trim(taskDetails)>
	</cffunction>

	<cffunction name="getTaskSolicitorsList" access="public" output="false" returntype="string">
		<cfargument name="taskID" type="numeric" required="true">
		<cfargument name="projectID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.returnStruct = getTaskSolicitorMembersDetails(mcproxy_siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteid, taskID=arguments.taskID, projectID=arguments.projectID)>

		<cfsavecontent variable="local.solicitorsList">
			<cfoutput>
			<table id="resultsTbl" cellpadding="4" cellspacing="0" border="0" width="100%">
				<cfloop from="1" to="#arrayLen(local.returnStruct.arrmembers)#" index="local.thisNum">
					<cfset local.currentMemberData = local.returnStruct.arrmembers[local.thisNum]>
					<tr valign="top">
						<td width="45%">
							<b>#local.currentMemberData.mc_combinedName#</b>
							<div style="margin-left:10px;">
								<cfif len(local.currentMemberData.company)>
									<div class="com">#local.currentMemberData.company#&nbsp;</div>
								</cfif><br/>
								#local.currentMemberData.mc_combinedAddresses#
								<cfif len(local.currentMemberData.mc_extraInfo)>
									<div>#local.currentMemberData.mc_extraInfo#</div>
								</cfif>
							</div>
							<div style="margin-top:5px;">
								<cfif len(local.currentMemberData.mc_recordType)>#local.currentMemberData.mc_recordType#<br/></cfif>
								<cfif len(trim(local.currentMemberData.mc_memberType))>#local.currentMemberData.mc_memberType#<br/></cfif>
								<cfif len(local.currentMemberData.mc_lastlogin)>#local.currentMemberData.mc_lastlogin#<br/></cfif>
								<cfif local.currentMemberData.mcaccountstatus eq "I">
									<span style="font-weight:bold;color:##F00;">ACCOUNT INACTIVE</span><br/>
								<cfelseif len(trim(local.currentMemberData.mc_memberStatus))>
									#local.currentMemberData.mc_memberStatus#<br/>
								</cfif>
								#local.currentMemberData.mc_memberclassifications#
							</div>
						</td>
					</tr>
				</cfloop>
			</table>
			</cfoutput>
		</cfsavecontent>

		<cfreturn trim(local.solicitorsList)>
	</cffunction>

	<cffunction name="getTaskStatuses" access="public" output="false" returntype="query">
		<cfset var qryTaskStatuses = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTaskStatuses">
			select taskStatusID, statusName
			from dbo.tasks_statuses
			order by taskStatusID
		</cfquery>

		<cfreturn qryTaskStatuses>
	</cffunction>

	<cffunction name="getTaskAssigneeRoles" access="public" output="false" returntype="query">
		<cfset var qryTaskAssigneeRoles = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTaskAssigneeRoles">
			select assigneeRoleID, roleName
			from dbo.tasks_taskAssigneeRoles
			order by roleName
		</cfquery>

		<cfreturn qryTaskAssigneeRoles>
	</cffunction>

	<cffunction name="getWorkspaces" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryWorkspaces = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryWorkspaces">
			select w.workspaceID, ai.applicationInstanceName
			from dbo.tasks_workspaces as w 
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'workspace'
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			order by ai.applicationInstanceName;
		</cfquery>

		<cfreturn qryWorkspaces>
	</cffunction>

	<cffunction name="getWorkspaceProjects" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="workspaceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct['arrprojects'] = arrayNew(1)>

		<cfset local.qryProjects = getSiteOrWorkspaceProjects(siteID=arguments.mcproxy_siteID, workspaceID=arguments.workspaceID)>
		
		<cfloop query="local.qryProjects">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['projectid'] = local.qryProjects.projectID>
			<cfset local.tmp['projectname'] = local.qryProjects.projectName>
			<cfset arrayAppend(local.returnStruct['arrprojects'], local.tmp)>
		</cfloop>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSiteProjects" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryProjects = getSiteOrWorkspaceProjects(siteID=arguments.siteID, workspaceID=0)>

		<cfreturn qryProjects>
	</cffunction>

	<cffunction name="getSiteOrWorkspaceProjects" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="workspaceID" type="numeric" required="true">

		<cfset var qryProjects = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryProjects">
			select p.projectID, projectContent.contentTitle as projectName
			from dbo.tasks_projects as p
			inner join dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'workspace'
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = p.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			cross apply dbo.fn_getContent(p.projectContentID,1) as projectContent
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfif arguments.workspaceID gt 0>
				and w.workspaceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.workspaceID#">
			</cfif>
			order by projectContent.contentTitle;
		</cfquery>

		<cfreturn qryProjects>
	</cffunction>

	<cffunction name="getProjectWorkpsaceSettings" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="projectID" type="numeric" required="true">

		<cfset var qryProjectWorkpsaceSettings = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryProjectWorkpsaceSettings">
			select p.siteResourceID, projectContent.contentTitle as projectName, p.profileID, 
				p.requestPayProfile, mp.profileCode, reqPayProfContent.rawcontent as paymentInstructions,
				w.solicitorFieldLabel, w.solicitorFieldLabelPlural, w.prospectFieldLabel, w.prospectFieldLabelPlural, 
				w.taskFieldLabel, w.taskFieldLabelPlural, w.noteCategoryID, ai.applicationInstanceName as workspaceName, 
				mfu_prospect.fieldSetID as prospectContactFSID, rpt_sr.reportID, rpt_sr.ruleID
			from dbo.tasks_projects as p
			inner join dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'workspace'
			inner join dbo.cms_siteResources as sr on sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#"> 
				and sr.siteResourceStatusID = 1 
				and sr.siteResourceID = p.siteResourceID
			inner join dbo.rpt_SavedReports as rpt_sr on rpt_sr.controllingSiteResourceID = p.siteResourceID
			left outer join dbo.mp_profiles as mp on mp.profileID = p.profileID 
				and mp.status = 'A'
			left outer join dbo.ams_memberFieldUsage as mfu_prospect on mfu_prospect.siteResourceID = p.siteResourceID 
				and mfu_prospect.area = 'prospectcontact'
			cross apply dbo.fn_getContent(p.projectContentID,1) as projectContent
			cross apply dbo.fn_getContent(p.requestPayProfileContentID,1) as reqPayProfContent
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and p.projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.projectID#">
		</cfquery>

		<cfreturn qryProjectWorkpsaceSettings>
	</cffunction>

	<cffunction name="getTaskProjectFieldsInfo" access="public" output="false" returntype="struct">
		<cfargument name="fd" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset arguments.fd = DeserializeJSON(arguments.fd)>

		<cfif not structKeyExists(arguments.fd,"siteID")>
			<cfset arguments.fd['siteID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"taskID")>
			<cfset arguments.fd['taskID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"projectID")>
			<cfset arguments.fd['projectID'] = 0>
		</cfif>

		<!--- Project Fields --->
		<cfquery name="local.qryProject" datasource="#application.dsn.membercentral.dsn#">
			select p.siteResourceID, projectContent.contentTitle as projectName
			from dbo.tasks_projects as p
			inner join dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'workspace'
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = p.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			cross apply dbo.fn_getContent(p.projectContentID,1) as projectContent
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fd.siteID#">
			and p.projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fd.projectID#">
		</cfquery>

		<cfset local.projectCustomFieldsXML = local.objCustomFields.getFieldsXML(siteID=arguments.fd.siteID, resourceType='Project', areaName='Project', csrid=local.qryProject.siteResourceID, detailID=arguments.fd.projectID, hideAdminOnly=0)>
		<cfset local.projectFieldsXML = xmlParse(local.projectCustomFieldsXML.returnXML).xmlRoot>
		
		<cfset local.data['hasProjectFields'] = arrayLen(local.projectFieldsXML.xmlChildren) gt 0>
		<cfif local.data['hasProjectFields']>
			<cfset local.data['arrProjectFields'] = local.objCustomFields.getResourceFieldsArrayFromFieldsXML(itemID=arguments.fd.taskID, itemType='TaskProjectCustom', usageRT='Project', 
					usageAN='Project', csrid=local.qryProject.siteResourceID, detailID=arguments.fd.projectID, fieldsXML=local.projectFieldsXML)>
		<cfelse>
			<cfset local.data['arrProjectFields'] = arrayNew(1)>
		</cfif>

		<!--- Task Tags --->
		<cfquery name="local.qryTaskTags" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @siteID int, @projectID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fd.siteID#">;
			set @projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fd.projectID#">;

			select ct.siteResourceID, ct.categoryTreeID, ct.categoryTreeName, c.categoryID, c.categoryName, 
				ROW_NUMBER() OVER (ORDER BY ct.sortOrder, c.sortOrder) as row
			from dbo.cms_categoryTrees as ct
			inner join dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.cms_categories as c on c.categoryTreeID = ct.categoryTreeID and c.isActive = 1
			inner join dbo.cms_applicationInstances as ai on ai.siteResourceID = ct.controllingSiteResourceID
			inner join dbo.tasks_workspaces as w on w.applicationInstanceID = ai.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'workspace'
			inner join dbo.cms_siteResources as sr2 on sr2.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs2 on srs2.siteResourceStatusID = sr2.siteResourceStatusID
				and srs2.siteResourceStatusDesc = 'Active'
			inner join dbo.tasks_projects as p on p.workspaceID = w.workspaceID
			where ct.siteID = @siteID
			and p.projectID = @projectID
			order by row;
		</cfquery>

		<cfset local.data['hasTaskTags'] = local.qryTaskTags.recordCount gt 0>
		<cfif local.data['hasTaskTags']>
			<cfif arguments.fd.taskID gt 0>
				<cfset local.qryTaskCategories = getTaskCategories(taskID=arguments.fd.taskID)>
				<cfset local.selectedCategoryList = valueList(local.qryTaskCategories.categoryID)>
				<cfset local.data['arrSelectedTaskTags'] = listToArray(local.selectedCategoryList)>
			</cfif>

			<cfset local.data['arrWorkspaceCatTrees'] = arrayNew(1)>
			<cfoutput query="local.qryTaskTags" group="categoryTreeID">
				<cfset local.strTmpCatTree = structNew()>
				<cfset local.strTmpCatTree['categoryTreeID'] = local.qryTaskTags.categoryTreeID>
				<cfset local.strTmpCatTree['categoryTreeName'] = local.qryTaskTags.categoryTreeName>
				<cfset local.strTmpCatTree['arrTaskTags'] = arrayNew(1)>

				<cfoutput>
					<cfset local.strTmp = structNew()>
					<cfset local.strTmp['categoryID'] = local.qryTaskTags.categoryID>
					<cfset local.strTmp['categorytreename'] = local.qryTaskTags.categoryTreeName>
					<cfset local.strTmp['categoryname'] = local.qryTaskTags.categoryName>
					<cfset local.strTmp['isSelected'] = arguments.fd.taskID gt 0 and listFindNoCase(local.selectedCategoryList,local.qryTaskTags.categoryID)>
					
					<cfset local.thisTaskTagFieldsXML = local.objCustomFields.getFieldsXML(siteID=arguments.fd.siteID, resourceType='ProjectWorkspace', areaName='TaskTag', csrid=local.qryTaskTags.siteResourceID, detailID=local.qryTaskTags.categoryID, hideAdminOnly=0)>
					<cfset local.taskTagFieldsXML = xmlParse(local.thisTaskTagFieldsXML.returnXML).xmlRoot>
					
					<cfset local.strTmp['hasTaskTagFields'] = arrayLen(local.taskTagFieldsXML.xmlChildren) gt 0>
					<cfif local.strTmp['hasTaskTagFields']>
						<cfset local.strTmp['arrTaskTagFields'] = local.objCustomFields.getResourceFieldsArrayFromFieldsXML(itemID=arguments.fd.taskID, itemType='TaskTagCustom', usageRT='ProjectWorkspace', 
							usageAN='TaskTag', csrid=local.qryTaskTags.siteResourceID, detailID=local.qryTaskTags.categoryID, fieldsXML=local.taskTagFieldsXML)>
					<cfelse>
						<cfset local.strTmp['arrTaskTagFields'] = arrayNew(1)>
					</cfif>
					
					<cfset arrayAppend(local.strTmpCatTree['arrTaskTags'], local.strTmp)>
				</cfoutput>

				<cfset arrayAppend(local.data['arrWorkspaceCatTrees'],local.strTmpCatTree)>
			</cfoutput>
		</cfif>

		<cfset local.data['success'] = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getTaskCategories" access="public" output="false" returntype="query">
		<cfargument name="taskID" type="numeric" required="true">

		<cfset var qryTaskCategories = "">

		<cfquery name="qryTaskCategories" datasource="#application.dsn.membercentral.dsn#">
			select tc.categoryID
			from dbo.tasks_taskCategories as tc 
			inner join dbo.tasks_tasks as t on t.taskID = tc.taskID 
			where t.taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">
		</cfquery>

		<cfreturn qryTaskCategories>
	</cffunction>

	<cffunction name="importTasks" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryImportColumns" type="query" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errMsg = "">
		
		<cftry>
			<cfset local.strSQLPrep = createObject("component","model.admin.common.modules.import.import").prepBCPToTableSQL(event=arguments.event, qryImportColumns=arguments.qryImportColumns, importTableName='##tasks_tasksImport')>
			<cfif not local.strSQLPrep.success>
				<cfthrow message="There was an error processing final import.">
			</cfif>

			<cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tasks_tasksImport') IS NOT NULL 
						DROP TABLE ##tasks_tasksImport;

					declare @siteID int, @projectID int, @runByMemberID int, @importResult xml;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
					set @projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('projectID',0)#">;
					set @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;

					-- bcp to table
					BEGIN TRY
						#PreserveSingleQuotes(local.strSQLPrep.sql)#
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- add MCProjectID column
					BEGIN TRY
						set @importResult = null;
						ALTER TABLE ##tasks_tasksImport ADD MCProjectID int null;

						<cfif arguments.event.getValue('projectID',0) gt 0>
							UPDATE ##tasks_tasksImport 
							SET MCProjectID = @projectID;
						</cfif>
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to prepare table for import." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
					
					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.tasks_importTasks @siteID=@siteID, @runByMemberID=@runByMemberID, @runImmediately=0, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					declare @errCount int;
					select @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount; 

					IF OBJECT_ID('tempdb..##tasks_tasksImport') IS NOT NULL 
						DROP TABLE ##tasks_tasksImport;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)> 
			<cfset local.returnStruct.errCount = local.qryImport.errCount>
			<cfif local.returnStruct.errCount gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.strImportDetails = getTaskImportDetails(event=arguments.event, qryImportColumns=arguments.qryImportColumns)>
			</cfif>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			 </cfcatch> 
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getTaskImportDetails" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryImportColumns" type="query" required="yes">

		<cfset var local = structNew()>
		
		<cfset local.strFormFields = structNew()>
		<cfset local.arrImportColumnDetails = arrayNew(1)>

		<cfset structInsert(local.strFormFields, 'resourceType', 'Tasks')>
		<cfset structInsert(local.strFormFields, 'ntLst', arguments.event.getValue('ntLst',0))>
		<cfset structInsert(local.strFormFields, 'projectID', arguments.event.getValue('projectID',0))>
		<cfset structInsert(local.strFormFields, 'bcpfilename', arguments.event.getValue('bcpfilename',''))>
		<cfset structInsert(local.strFormFields, 'uploadedFileFieldList', arguments.event.getValue('uploadedFileFieldList',''))>

		<cfloop query="arguments.qryImportColumns">
			<cfset local.tmpStr = { columnID=arguments.qryImportColumns.columnID, mappedColValue=arguments.event.getValue('mcimpcol_map_#arguments.qryImportColumns.columnID#',''),
									mappedColOverrideValue='' }>
			<cfif local.tmpStr.mappedColValue EQ '_override_value_'>
				<cfset local.tmpStr.mappedColOverrideValue = arguments.event.getValue('mcimpcol_override_#arguments.qryImportColumns.columnID#','')>
			</cfif>
			<cfset arrayAppend(local.arrImportColumnDetails, local.tmpStr)>
		</cfloop>

		<cfset local.strImportDetails = { strFormFields=local.strFormFields, arrImportColumnDetails=local.arrImportColumnDetails }>

		<cfreturn local.strImportDetails>
	</cffunction>
	
	<cffunction name="showImportResults" access="package" output="false" returntype="string">
		<cfargument name="strImportResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">
		<cfargument name="projectID" type="numeric" required="yes">

		<cfscript>
			var local = structNew();
			local.data = '';
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif NOT arguments.strImportResult.success>
				<div id="divTaskImportErrorScreen" class="alert alert-danger">
					<h4>Import Results</h4>
					<div class="font-weight-bold mb-2">The import was stopped and requires your attention.</div>
					<cfif structKeyExists(arguments.strImportResult,"importResultXML")>
						<cfset local.arrErrors = XMLSearch(arguments.strImportResult.importResultXML,"/import/errors/error")>
						<div class="mb-2">
							<cfif arrayLen(local.arrErrors) gt 200>
								<b>Only the first 200 errors are shown.</b><br/><br/>
							</cfif>
							<cfset local.thisErrNum = 0>
							<cfloop array="#local.arrErrors#" index="local.thisErr">
								<cfset local.thisErrNum = local.thisErrNum + 1>
								#local.thisErr.xmlAttributes.msg#<br/>
								<cfif local.thisErrNum is 200>
									<cfbreak>
								</cfif>
							</cfloop>
						</div>
					<cfelse>
						<div class="mb-2">#arguments.strImportResult.errMsg#</div>
					</cfif>
					<div>
						<button class="btn btn-sm btn-secondary" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#&tab=import';">Try upload again</button>
						<cfif structKeyExists(arguments.strImportResult,"previousMappingScreen")>
							<a href="##" class="btn btn-sm btn-secondary" onclick="$('##divTaskImportErrorScreen').hide();$('##divTaskImportMappingScreen').show(300);return false;">Return to Column Mapping</a>
						</cfif>
					</div>
				</div>
				<cfif structKeyExists(arguments.strImportResult,"previousMappingScreen")>
					<div id="divTaskImportMappingScreen" style="display:none;">
						#arguments.strImportResult.previousMappingScreen#
					</div>
				</cfif>
			<cfelse>
				<h4>Import Results</h4>
				<p class="font-weight-bold">Import Has Been Scheduled</p>
				<div class="mb-2">
					The import of the uploaded file has been scheduled and will begin shortly.<br/>
					You will be sent an e-mail with the results of the import.
				</div>
				<div>
					<button class="btn btn-sm btn-secondary" name="btnDoOver" type="button" onClick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
					<cfif arguments.projectID gt 0>
						<button class="btn btn-sm btn-secondary" name="btnCancel" type="button" onClick="top.reloadProjectTasks();">Close</button>
					</cfif>
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="massUpdateTasks" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryUpdateColumns" type="query" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errMsg = "">
		
		<cftry>
			<cfset local.strSQLPrep = createObject("component","model.admin.common.modules.massUpdate.massUpdate").prepBCPToTableSQL(event=arguments.event, qryUpdateColumns=arguments.qryUpdateColumns, updateTableName='##tasks_tasksUpdate')>
			<cfif not local.strSQLPrep.success>
				<cfthrow message="There was an error processing final update.">
			</cfif>

			<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#" result="local.qryUpdateResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tasks_tasksUpdate') IS NOT NULL 
						DROP TABLE ##tasks_tasksUpdate;

					declare @siteID int, @projectID int, @runByMemberID int, @updateResult xml;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
					set @projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('projectID',0)#">;
					set @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;

					-- bcp to table
					BEGIN TRY
						#PreserveSingleQuotes(local.strSQLPrep.sql)#
					END TRY
					BEGIN CATCH
						select @updateResult = '<update><errors><error msg="Unable to upload the file for processing." /><error msg="' + dbo.fn_RegExReplace(error_message(), '[^A-Za-z0-9_\- ]', '') + '" /></errors></update>';
						GOTO on_done;
					END CATCH

					-- add MCProjectID column
					BEGIN TRY
						set @updateResult = null;
						ALTER TABLE ##tasks_tasksUpdate ADD MCProjectID int null;

						<cfif arguments.event.getValue('projectID',0) gt 0>
							UPDATE ##tasks_tasksUpdate 
							SET MCProjectID = @projectID;
						</cfif>
					END TRY
					BEGIN CATCH
						select @updateResult = '<update><errors><error msg="Unable to prepare table for update." /><error msg="' + dbo.fn_RegExReplace(error_message(), '[^A-Za-z0-9_\- ]', '') + '" /></errors></update>';
						GOTO on_done;
					END CATCH
					
					-- update
					BEGIN TRY
						set @updateResult = null;
						EXEC dbo.tasks_massUpdateTasks @siteID=@siteID, @runByMemberID=@runByMemberID, @updateResult=@updateResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @updateResult = '<update><errors><error msg="Unable to process the update file." /><error msg="' + dbo.fn_RegExReplace(error_message(), '[^A-Za-z0-9_\- ]', '') + '" /></errors></update>';
						GOTO on_done;
					END CATCH
				
					on_done:
					declare @errCount int;
					select @errCount = @updateResult.value('count(/update/errors/error)','int');
					SELECT @updateResult as updateResult, @errCount as errCount; 

					IF OBJECT_ID('tempdb..##tasks_tasksUpdate') IS NOT NULL 
						DROP TABLE ##tasks_tasksUpdate;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.updateResultXML = xmlparse(local.qryUpdate.updateResult)> 
			<cfset local.returnStruct.errCount = local.qryUpdate.errCount>
			<cfif local.returnStruct.errCount gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.strUpdateDetails = getTaskMassUpdateDetails(event=arguments.event, qryUpdateColumns=arguments.qryUpdateColumns)>
			</cfif>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			 </cfcatch> 
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getTaskMassUpdateDetails" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryUpdateColumns" type="query" required="yes">

		<cfset var local = structNew()>
		
		<cfset local.strFormFields = structNew()>
		<cfset local.arrUpdateColumnDetails = arrayNew(1)>

		<cfset structInsert(local.strFormFields, 'resourceType', 'Tasks')>
		<cfset structInsert(local.strFormFields, 'ntLst', arguments.event.getValue('ntLst',0))>
		<cfset structInsert(local.strFormFields, 'projectID', arguments.event.getValue('projectID',0))>
		<cfset structInsert(local.strFormFields, 'bcpfilename', arguments.event.getValue('bcpfilename',''))>
		<cfset structInsert(local.strFormFields, 'uploadedFileFieldList', arguments.event.getValue('uploadedFileFieldList',''))>

		<cfloop query="arguments.qryUpdateColumns">
			<cfset local.tmpStr = { columnID=arguments.qryUpdateColumns.columnID, mappedColValue=arguments.event.getValue('mcupdcol_map_#arguments.qryUpdateColumns.columnID#',''),
									mappedColOverrideValue='' }>
			<cfif local.tmpStr.mappedColValue EQ '_override_value_'>
				<cfset local.tmpStr.mappedColOverrideValue = arguments.event.getValue('mcupdcol_override_#arguments.qryUpdateColumns.columnID#','')>
			</cfif>
			<cfset arrayAppend(local.arrUpdateColumnDetails, local.tmpStr)>
		</cfloop>

		<cfset local.strUpdateDetails = { strFormFields=local.strFormFields, arrUpdateColumnDetails=local.arrUpdateColumnDetails }>

		<cfreturn local.strUpdateDetails>
	</cffunction>

	<cffunction name="showTaskMassUpdateResults" access="package" output="false" returntype="string">
		<cfargument name="strUpdateResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">
		<cfargument name="projectID" type="numeric" required="yes">

		<cfscript>
			var local = structNew();
			local.data = '';
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif NOT arguments.strUpdateResult.success>
				<div id="divTaskUpdateErrorScreen" class="alert alert-danger">
					<h4>Mass Update Results</h4>
					<div class="mb-3"><b>The update was stopped and requires your attention.</b></div>

					<cfif structKeyExists(arguments.strUpdateResult,"updateResultXML")>
						<cfset local.arrErrors = XMLSearch(arguments.strUpdateResult.updateResultXML,"/update/errors/error")>
						<div class="mb-3">
							<cfif arrayLen(local.arrErrors) gt 200>
								<b>Only the first 200 errors are shown.</b><br/><br/>
							</cfif>
							<cfset local.thisErrNum = 0>
							<cfloop array="#local.arrErrors#" index="local.thisErr">
								<cfset local.thisErrNum = local.thisErrNum + 1>
								#local.thisErr.xmlAttributes.msg#<br/>
								<cfif local.thisErrNum is 200>
									<cfbreak>
								</cfif>
							</cfloop>
						</div>
					<cfelse>
						<div>#arguments.strUpdateResult.errMsg#</div>
					</cfif>

					<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
					<cfif structKeyExists(arguments.strUpdateResult,"previousMappingScreen")>
						&nbsp;<a href="##" onClick="$('##divTaskUpdateErrorScreen').hide();$('##divTaskUpdateMappingScreen').show(300);return false;">Return to Column Mapping</a>
					</cfif>
				</div>
				<cfif structKeyExists(arguments.strUpdateResult,"previousMappingScreen")>
					<div id="divTaskUpdateMappingScreen" style="display:none;">
						#arguments.strUpdateResult.previousMappingScreen#
					</div>
				</cfif>
			<cfelse>
				<h4>Mass Update Results</h4>
				<p><b>Update Has Been Completed</b></p>
				<div class="mb-3">
					Mass Update of tasks has been completed.<br/>
				</div>
				<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
			</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="massUpdateFilteredTasks" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="projectID" type="numeric" required="true">
		<cfargument name="taskIDList" type="string" required="true">
		<cfargument name="strUpdateColumns" type="struct" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errMsg = "">
		
		<cftry>
			<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#" result="local.qryUpdateResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tasks_tasksUpdate') IS NOT NULL 
						DROP TABLE ##tasks_tasksUpdate;

					declare @siteID int, @projectID int, @runByMemberID int, @updateResult xml;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
					set @projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.projectID#">;
					set @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;

					-- insert data to table
					BEGIN TRY
						select listitem as TaskID, ROW_NUMBER() OVER (order by listitem) as rowID
							<cfloop collection="#arguments.strUpdateColumns#" item="local.thisCol">
								, '#arguments.strUpdateColumns[local.thisCol]#' as [#local.thisCol#]
							</cfloop>
						into ##tasks_tasksUpdate
						from dbo.fn_intListToTable('0#arguments.taskIDList#',',');
					END TRY
					BEGIN CATCH
						select @updateResult = '<update><errors><error msg="Unable to prepare data for mass update." /><error msg="' + error_message() + '" /></errors></update>';
						GOTO on_done;
					END CATCH

					-- add MCProjectID column
					BEGIN TRY
						set @updateResult = null;
						ALTER TABLE ##tasks_tasksUpdate ADD MCProjectID int null;

						<cfif arguments.projectID gt 0>
							UPDATE ##tasks_tasksUpdate 
							SET MCProjectID = @projectID;
						</cfif>
					END TRY
					BEGIN CATCH
						select @updateResult = '<update><errors><error msg="Unable to add projectID column for mass update." /><error msg="' + error_message() + '" /></errors></update>';
						GOTO on_done;
					END CATCH
					
					-- update
					BEGIN TRY
						set @updateResult = null;
						EXEC dbo.tasks_massUpdateTasks @siteID=@siteID, @runByMemberID=@runByMemberID, @updateResult=@updateResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @updateResult = '<update><errors><error msg="Unable to process the mass update data." /><error msg="' + error_message() + '" /></errors></update>';
						GOTO on_done;
					END CATCH
				
					on_done:
					declare @errCount int;
					select @errCount = @updateResult.value('count(/update/errors/error)','int');
					SELECT @updateResult as updateResult, @errCount as errCount; 

					IF OBJECT_ID('tempdb..##tasks_tasksUpdate') IS NOT NULL 
						DROP TABLE ##tasks_tasksUpdate;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.updateResultXML = xmlparse(local.qryUpdate.updateResult)> 
			<cfset local.returnStruct.errCount = local.qryUpdate.errCount>
			<cfif local.returnStruct.errCount gt 0>
				<cfset local.returnStruct.success = false>
				
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errMsg = "There was a problem updating the data. Try again or contact us for assistance.">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		 </cfcatch> 
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="filteredTasksMassUpdateResults" access="package" output="false" returntype="string">
		<cfargument name="strUpdateResult" type="struct" required="yes">
		<cfargument name="taskFieldLabelPlural" type="string" required="yes">

		<cfset var resultString = "">
		
		<cfsavecontent variable="resultString">
			<cfoutput>
			<div class="row my-3">
				<div class="col-md-12">
					<div class="card card-box mb-1">
						<div class="card-header py-1 bg-light">
							<div class="card-header--title font-weight-bold font-size-md">
								Mass Update Results
							</div>
						</div>
						<div class="card-body">
							<cfif NOT arguments.strUpdateResult.success>
								<div class="alert alert-danger mb-3">
									<div class="mb-3">The update was stopped and requires your attention.</div>
									
									<cfif structKeyExists(arguments.strUpdateResult,"updateResultXML")>
										<cfset local.arrErrors = XMLSearch(arguments.strUpdateResult.updateResultXML,"/update/errors/error")>
										<div>
											<cfif arrayLen(local.arrErrors) gt 200>
												<b>Only the first 200 errors are shown.</b><br/><br/>
											</cfif>
											<cfset local.thisErrNum = 0>
											<cfloop array="#local.arrErrors#" index="local.thisErr">
												<cfset local.thisErrNum = local.thisErrNum + 1>
												#local.thisErr.xmlAttributes.msg#<br/>
												<cfif local.thisErrNum is 200>
													<cfbreak>
												</cfif>
											</cfloop>
										</div>
									<cfelse>
										<div>#arguments.strUpdateResult.errMsg#</div>
									</cfif>
								</div>
								<button type="button" class="btn btn-sm btn-secondary" name="btnDoOver" onclick="cancelTask(false);">Return To #arguments.taskFieldLabelPlural#</button>
							<cfelse>
								<div class="mb-3">Mass Update of tasks has been completed.</div>
								<button class="btn btn-sm btn-secondary" name="btnDoOver" type="button" onclick="cancelTask(true);">Return To #arguments.taskFieldLabelPlural#</button>
							</cfif>
						</div>
					</div>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn resultString>
	</cffunction>

	<cffunction name="getTaskStatusName" access="public" output="false" returntype="string">
		<cfargument name="statusID" type="numeric" required="yes">

		<cfset var qryTaskStatus ="">

		<cfquery name="qryTaskStatus" datasource="#application.dsn.membercentral.dsn#">
			select statusName
			from dbo.tasks_statuses
			where taskStatusID = <cfqueryparam value="#arguments.statusID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn qryTaskStatus.statusName>
	</cffunction>

	<cffunction name="getTaskTagNames" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="categoryIDList" type="string" required="yes">

		<cfset var qryTaskTags ="">

		<cfif len(arguments.categoryIDList)>
			<cfquery name="qryTaskTags" datasource="#application.dsn.membercentral.dsn#">
				select c.categoryName
				from dbo.cms_categories as c
				inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID 
				where ct.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and c.isActive = 1
				and c.categoryID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.categoryIDList#">)
				order by c.categoryName
			</cfquery>

			<cfreturn valueList(qryTaskTags.categoryName)>
		<cfelse>
			<cfreturn ''>
		</cfif>
	</cffunction>

	<cffunction name="getProjectFieldsetDetails" access="private" output="no" returntype="query">
		<cfargument name="projectID" type="numeric" required="yes">
		<cfargument name="area" type="string" required="yes">
		
		<cfset var qryProjectFieldsetDetails = "">
		
		<cfquery name="qryProjectFieldsetDetails" datasource="#application.dsn.membercentral.dsn#">
			select p.showMemberPhotos, p.siteResourceID, mfu.fieldsetID, mfs.fieldsetName, 
				mfs.nameFormat, mfs.showHelp
			from dbo.tasks_projects as p
			inner join dbo.ams_memberFieldUsage as mfu on mfu.siteResourceID = p.siteResourceID
			inner join dbo.ams_memberFieldSets mfs on mfs.fieldsetID = mfu.fieldsetID
			where p.projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.projectID#">
			and mfu.area = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.area#">
		</cfquery>
	
		<cfreturn qryProjectFieldsetDetails>
	</cffunction>

	<cffunction name="getTaskSolicitorMembersDetails" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="taskID" type="numeric" required="yes">
		<cfargument name="projectID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryTaskSolicitors" datasource="#application.dsn.memberCentral.dsn#">
			select mActive.orgID, mActive.memberID, tar.roleName
			from dbo.tasks_tasks as t
			inner join dbo.cms_siteResources as sr on t.siteResourceID = sr.siteResourceID 
				and sr.siteID = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">
			inner join dbo.cms_siteResourceStatuses as srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.sites as s on s.siteID = sr.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
			inner join dbo.tasks_taskAssignees as ta on ta.taskID = t.taskID
			inner join dbo.ams_members as m on m.memberID = ta.memberID and m.orgID = o.orgID
			inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID
			where t.taskID = <cfqueryparam value="#arguments.taskID#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>

		<cfif local.qryTaskSolicitors.recordCount>
			<cfset local.returnStruct = getTaskMembersDetails(mcproxy_orgID=local.qryTaskSolicitors.orgID, projectID=arguments.projectID, memberIDList=valueList(local.qryTaskSolicitors.memberID), mode='solicitor')>
			<cfloop from="1" to="#arrayLen(local.returnStruct.arrmembers)#" index="local.thisNum">
				<cfquery name="local.qryThisMemRole" dbtype="query">
					select roleName
					from [local].qryTaskSolicitors
					where memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.returnStruct.arrmembers[local.thisNum].memberID#">
				</cfquery>
				<cfset local.returnStruct['arrmembers'][local.thisNum]['roleName'] = local.qryThisMemRole.roleName>
			</cfloop>
		<cfelse>
			<cfset local.returnStruct = structNew()>
			<cfset local.returnStruct['success'] = true>
			<cfset local.returnStruct['arrmembers'] = arrayNew(1)>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	<cffunction name="getTaskSolicitorName" access="public" output="false" returntype="string">
		
		<cfargument name="taskID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryTaskSolicitor" datasource="#application.dsn.memberCentral.dsn#">
			select   mActive.firstname  + ' ' + mActive.lastname as taskSolicitor
						from  dbo.tasks_taskAssignees ta
						inner join dbo.tasks_taskAssigneeRoles as tar on tar.assigneeRoleID = ta.assigneeRoleID and tar.roleCode = 'assignee'
						inner join dbo.ams_members as m ON m.memberID = ta.memberID
						inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
						where ta.taskID = <cfqueryparam value="#arguments.taskID#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>

		<cfif local.qryTaskSolicitor.recordCount>
			<cfset local.taskSolicitor = local.qryTaskSolicitor.taskSolicitor>
		<cfelse>
			<cfset local.taskSolicitor = ''>			
		</cfif>

		<cfreturn local.taskSolicitor>
	</cffunction>

	<cffunction name="getTaskMembersDetails" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="projectID" type="numeric" required="yes">
		<cfargument name="memberIDList" type="string" required="yes">
		<cfargument name="mode" type="string" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct['arrmembers'] = arrayNew(1)>
		<cfset local.returnStruct['success'] = false>

		<cfset local.qryProjectDetails = getProjectFieldsetDetails(projectID=arguments.projectID, area='details')>

		<cfset local.returnStruct['showmemberphoto'] = local.qryProjectDetails.showMemberPhotos>
		<cfset local.fieldSetID = val(local.qryProjectDetails.fieldsetID)>
		
		<cfquery name="local.qryMembers" datasource="#application.dsn.memberCentral.dsn#" result="local.qryMembersResult">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">, @outputFieldsXML xml;

			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
				DROP TABLE ##tmpMembersFS;
			CREATE TABLE ##tmpMembers (memberID int PRIMARY KEY, lastname varchar(75), firstname varchar(75), membernumber varchar(50), 
				MCAccountStatus char(1), hasMemberPhotoThumb bit, company varchar(200), mc_row int IDENTITY(1,1));
			CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

			INSERT INTO ##tmpMembers (memberID, lastname, firstname, membernumber, MCAccountStatus, hasMemberPhotoThumb, company)
			select m.memberID, m.lastname, m.firstname, m.membernumber, m.status, m.hasMemberPhotoThumb, m.company		
			from dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.memberIDList#">,',') as tmp
			inner join dbo.ams_members as m on m.memberID = tmp.listitem
			inner join dbo.organizations as o on o.orgId = m.orgId
			where m.orgID = @orgID
			and m.memberid = m.activeMemberID
			and m.status <> 'D';

			-- get fieldset data and set back to snapshot because proc ends in read committed
			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
				@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.fieldsetID#">,
				@existingFields='m_lastname,m_firstname,m_membernumber,m_company,m_status', @ovNameFormat=NULL, @ovMaskEmails=0,
				@membersTableName='##tmpMembers', @membersResultTableName='##tmpMembersFS', @linkedMembers=0, 
				@mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT m.lastname, m.firstname, m.membernumber, m.MCAccountStatus, m.hasMemberPhotoThumb, m.company, tmp.*,
				CASE WHEN m.mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
			FROM ##tmpMembers AS m
			INNER JOIN ##tmpMembersFS AS tmp ON tmp.memberID = m.memberID
			ORDER BY m.mc_row;

			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
				DROP TABLE ##tmpMembersFS;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryMembers.recordCount>
			<cfset local.objMemberAdmin = createObject("component","model.admin.members.memberAdmin")>
			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.mcproxy_orgID, includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
				<cfelse>
					<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
				</cfif>
			</cfloop>
			
			<cfset local.xmlResultFields = local.qryMembers.mc_outputFieldsXML[1]>
			<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
			<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
			<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
			<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
			<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>
			<cfset local.mc_combinedAddresses = structNew()>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
			<cfloop array="#local.tmp#" index="local.thisField">
				<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
				<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
					<cfset local.strKey = "t#local.thisATID#">
				<cfelse>
					<cfset local.strKey = local.thisATID>
				</cfif>
				<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
					<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
				</cfif>
			</cfloop>

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				SELECT *
				FROM [local].qryOutputFields
				WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
			</cfquery>
			<cfloop query="local.qryMembers">
				<cfset local.tmpStr = structNew()>

				<cfset local.tmpStr['memberid'] = local.qryMembers.memberID>
				<cfset local.tmpStr['company'] = local.qryMembers.company>
				<cfset local.tmpStr['mcaccountstatus'] = local.qryMembers.MCAccountStatus>
				
				<cfset local.tmpStr['hasPhoto'] = local.qryMembers.hasMemberPhotoThumb>
				<cfif local.qryMembers.hasMemberPhotoThumb>
					<cfset local.tmpStr['memberphoto'] = local.qryMembers.membernumber & ".jpg">
				<cfelse>
					<cfset local.tmpStr['memberphoto'] = "">
				</cfif>
				
				<cfif val(local.qryProjectDetails.fieldsetID) gt 0>
					
					<cfset local.tmpStr['mc_combinedNameNoMemberNumber'] = local.qryMembers['Extended Name']>
					<cfset local.tmpStr['mc_combinedName'] = local.qryMembers['Extended MemberNumber']>

					<!--- combine address fields if there are any --->
					<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
					<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
						<cfsavecontent variable="local.thisATFull">
							<cfoutput>
							<cfif left(local.thisATID,1) eq "t">
								<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
								<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
							<cfelse>
								<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
								<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
							</cfif>

							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#</cfif>
							<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
							<cfif arrayLen(local.tmp2) is 1 and len(local.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>, #local.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]# </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])> #local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]# County<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])> #local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
							<cfloop array="#local.tmp#" index="local.thisPT">
								<cfif len(local.qryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
									<div>#local.thisPT.xmlAttributes.FieldLabel#: #local.qryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryMembers.currentrow]#</div>
								</cfif>
							</cfloop>
							</cfoutput>
						</cfsavecontent>
						<cfset local.thisATfull = trim(replace(replace(rereplace(local.thisATFull,'[\r\n\t]','','ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
						<cfif left(local.thisATfull,2) eq ", ">
							<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
						</cfif>
						<cfif len(local.thisATfull)>
							<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
						<cfelse>
							<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
						</cfif>
					</cfloop>

					<!--- get recordtype if available --->
					<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.qryMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
						<cfset local.tmpStr['mc_recordType'] = local.qryMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]>
					<cfelse>
						<cfset local.tmpStr['mc_recordType'] = "">
					</cfif>
					
					<!--- get membertypeid if available --->
					<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.qryMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
						<cfset local.tmpStr['mc_memberType'] = local.qryMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]>
					<cfelse>
						<cfset local.tmpStr['mc_memberType'] = "">
					</cfif>
					
					<!--- get status if available --->
					<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.qryMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
						<cfset local.tmpStr['mc_memberStatus'] = local.qryMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]>
					<cfelse>
						<cfset local.tmpStr['mc_memberStatus'] = "">
					</cfif>

					<!--- get last login date if available --->
					<cfif arrayLen(local.LastLoginDateInFS) is 1>
						<cfif len(local.qryMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
							<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: #DateFormat(local.qryMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow],"m/d/yy")#'>
						<cfelse>
							<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: <span class="dim"><i>none</i></span>'>
						</cfif>
					<cfelse>
						<cfset local.tmpStr['mc_lastlogin'] = "">
					</cfif>	

					<cfif StructCount(local.thisMem_mc_combinedAddresses)>
						<cfsavecontent variable="local.thisMemCombinedAddress">
							<cfoutput>
							<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
								<div><b style="font-size:95%; margin-right:15px;">#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#</b><br/>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>
							</cfloop>
							</cfoutput>
						</cfsavecontent>
						<cfset local.tmpStr['mc_combinedAddresses'] = rereplace(replace(replace(local.thisMemCombinedAddress,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
					<cfelse>
						<cfset local.tmpStr['mc_combinedAddresses'] = "">
					</cfif>

					<cfif local.qryOutputFieldsForLoop.recordCount>
						<cfsavecontent variable="local.thisMemExtraInfo">
							<cfoutput>
								<cfloop query="local.qryOutputFieldsForLoop">
								<cfset local.currValue = local.qryMembers[local.qryOutputFieldsForLoop.fieldLabel][local.qryMembers.currentrow]>
								<cfif len(local.currValue)>
									<div>
										#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
										<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
											#dollarFormat(local.currValue)#
										<cfelse>
											<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
												<cfcase value="DATE">
													#dateFormat(local.currValue,"m/d/yyyy")#
												</cfcase>
												<cfcase value="STRING,DECIMAL2,INTEGER">
													#local.currValue#
												</cfcase>
												<cfcase value="BIT">
													#YesNoFormat(local.currValue)#
												</cfcase>
											</cfswitch>
										</cfif>
									</div>
								</cfif>
							</cfloop>
							</cfoutput>
						</cfsavecontent>
						<cfset local.tmpStr['mc_extraInfo'] = rereplace(replace(replace(local.thisMemExtraInfo,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
					<cfelse>
						<cfset local.tmpStr['mc_extraInfo'] = "">
					</cfif>

					<cfset local.classifications = local.objMemberAdmin.getMemberClassifications(siteResourceID=local.qryProjectDetails.siteResourceID, memberID=local.qryMembers.memberID)>
					<cfif isArray(local.classifications) and not arrayIsEmpty(local.classifications)>
						<cfsavecontent variable="local.thisMemClassifications">
						<cfoutput>
							<br/>
							<cfloop from="1" to="#arrayLen(local.classifications)#" index="local.currentClass">
								<cfset local.qryClass = local.classifications[local.currentClass].qryClass>
								<cfset local.name = local.classifications[local.currentClass].name>
								<cfif local.qryClass.recordCount>
									<b style="font-size:95%;">#replace(local.name,'_',' ','ALL')#</b><br/>
									<cfloop query="local.qryClass">
										- #local.qryClass.groupName#<br/>
									</cfloop>
									<br/>
								</cfif>
							</cfloop>
						</cfoutput>
						</cfsavecontent>
						<cfset local.tmpStr['mc_memberclassifications'] = rereplace(replace(replace(local.thisMemClassifications,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
					<cfelse>
						<cfset local.tmpStr['mc_memberclassifications'] = ''>
					</cfif>

				<!--- no project fieldsets defined for prospect/solicitor --->
				<cfelse>
					<cfset local.tmpStr['mc_combinedNameNoMemberNumber'] = "#local.qryMembers.lastName#, #local.qryMembers.firstName#">
					<cfset local.tmpStr['mc_combinedName'] = "#local.tmpStr.mc_combinedNameNoMemberNumber# (#local.qryMembers.memberNumber#)">
					<cfset local.tmpStr['mc_recordType'] = "">
					<cfset local.tmpStr['mc_memberType'] = "">
					<cfset local.tmpStr['mc_memberStatus'] = "">
					<cfset local.tmpStr['mc_lastlogin'] = "">
					<cfset local.tmpStr['mc_combinedAddresses'] = "">
					<cfset local.tmpStr['mc_extraInfo'] = "">
					<cfset local.tmpStr['mc_memberclassifications'] = ''>
				</cfif>

				<cfset arrayAppend(local.returnStruct['arrmembers'],local.tmpStr)>
			</cfloop>
		</cfif>

		<cfif arguments.mode eq 'frontEndTaskDetail' and ListLen(arguments.memberIDList) is 1 and arrayLen(local.returnStruct['arrmembers'])>
			<cfset local.returnStruct['strMember'] = local.returnStruct['arrmembers'][1]>
		</cfif>

		<cfset local.returnStruct['success'] = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getTaskProjectFieldData" access="private" output="false" returntype="struct">
		<cfargument name="taskID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strTaskProjectFieldData = structNew()>

		<cfquery name="local.qryTaskProjectFieldData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpTaskProjectFieldData') is not null
				DROP TABLE ##tmpTaskProjectFieldData;
			CREATE TABLE ##tmpTaskProjectFieldData (fieldID int, fieldReference varchar(128), ANSWER varchar(max));

			DECLARE @taskID int, @usageID int;
			SET @taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">;
			select @usageID = dbo.fn_cf_getUsageID('Project','Project',null);

			INSERT INTO ##tmpTaskProjectFieldData (fieldID, fieldReference, ANSWER)
			SELECT fieldID, fieldReference, STRING_AGG(customValue,', ')
			FROM dbo.fn_cf_getResponses (@taskID, 'TaskProjectCustom', NULL)
			GROUP BY fieldID, fieldReference;

			SELECT f.fieldReference, '' as answer
			FROM dbo.cf_fields as f
			INNER JOIN dbo.tasks_projects as p on p.siteResourceID = f.controllingSiteResourceID
				and f.usageID = @usageID
				and f.detailID = p.projectID
			INNER JOIN dbo.tasks_tasks as t on t.projectID = p.projectID
				and t.taskID = @taskID
			LEFT OUTER JOIN ##tmpTaskProjectFieldData as tmp on tmp.fieldID = f.fieldID
			WHERE f.isActive = 1
			AND len(f.fieldReference) > 0
			AND tmp.fieldID is null
				UNION ALL
			SELECT fieldReference, ANSWER
			FROM ##tmpTaskProjectFieldData;

			IF OBJECT_ID('tempdb..##tmpTaskProjectFieldData') is not null
				DROP TABLE ##tmpTaskProjectFieldData;
		</cfquery>

		<cfloop query="local.qryTaskProjectFieldData">
			<cfif not structKeyExists(local.strTaskProjectFieldData,local.qryTaskProjectFieldData.fieldReference)>
				<cfset local.strTaskProjectFieldData[local.qryTaskProjectFieldData.fieldReference] = local.qryTaskProjectFieldData.answer>
			</cfif>
		</cfloop>

		<cfreturn local.strTaskProjectFieldData>
	</cffunction>

	<cffunction name="getTaskTagDetails" access="private" output="false" returntype="struct">
		<cfargument name="taskID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strTaskTags = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTaskTags">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpTaskTags') is not null
				DROP TABLE ##tmpTaskTags;

			DECLARE @taskID int;
			SET @taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">;

			select ct.categoryTreeID, ct.categoryTreeName, STRING_AGG(c.categoryName,', ') as categoryNameList
			into ##tmpTaskTags
			from dbo.tasks_taskCategories as tc 
			inner join dbo.tasks_tasks as t on t.taskID = tc.taskID
				and t.taskID = @taskID
			inner join dbo.cms_categories as c on c.categoryID = tc.categoryID
			inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
			where c.isActive = 1
			group by ct.categoryTreeID, ct.categoryTreeName;

			select ct.categoryTreeName, '' as categoryNameList
			from dbo.tasks_tasks as t
			inner join dbo.tasks_projects as p on p.projectID = t.projectID
				and t.taskID = @taskID
			inner join dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
			inner join dbo.cms_categoryTrees as ct on ct.controllingSiteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			left outer join ##tmpTaskTags as tmp on tmp.categoryTreeID = ct.categoryTreeID
			where tmp.categoryTreeID is null
				union all
			select categoryTreeName, categoryNameList
			FROM ##tmpTaskTags;

			IF OBJECT_ID('tempdb..##tmpTaskTags') is not null
				DROP TABLE ##tmpTaskTags;
		</cfquery>

		<cfloop query="local.qryTaskTags">
			<cfif not structKeyExists(local.strTaskTags,local.qryTaskTags.categoryNameList)>
				<cfset local.strTaskTags[local.qryTaskTags.categoryTreeName] = local.qryTaskTags.categoryNameList>
			</cfif>
		</cfloop>

		<cfreturn local.strTaskTags>
	</cffunction>

	<cffunction name="getTaskTagsFieldData" access="private" output="false" returntype="struct">
		<cfargument name="taskID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strTaskTagFields = structNew()>

		<cfquery name="local.qryTaskTagsFieldData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpFieldData') is not null
				DROP TABLE ##tmpFieldData;
			IF OBJECT_ID('tempdb..##tmpTaskTagsFieldData') is not null
				DROP TABLE ##tmpTaskTagsFieldData;
			CREATE TABLE ##tmpFieldData (fieldID int, ANSWER varchar(max));
			CREATE TABLE ##tmpTaskTagsFieldData (taskID int, categoryID int, fieldID int, titleOnInvoice varchar(max), ANSWER varchar(max));

			DECLARE @taskID int, @usageID int;
			SET @taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">;
			select @usageID = dbo.fn_cf_getUsageID('ProjectWorkspace','TaskTag',null);

			INSERT INTO ##tmpFieldData (fieldID, ANSWER)
			SELECT fieldID, STRING_AGG(customValue,', ')
			FROM dbo.fn_cf_getResponses (@taskID, 'TaskTagCustom', NULL)
			GROUP BY fieldID;
			
			INSERT INTO ##tmpTaskTagsFieldData (taskID, categoryID, fieldID, titleOnInvoice, ANSWER)
			SELECT DISTINCT @taskID, c.categoryID, f.fieldID, 
				ct.categoryTreeName + '_' + c.categoryName + '_' + f.fieldReference as titleOnInvoice,
				tmp.ANSWER
			FROM ##tmpFieldData as tmp
			INNER JOIN dbo.cf_fields as f on f.fieldID = tmp.fieldID
			INNER JOIN dbo.cms_categories as c on c.categoryID = f.detailID AND c.isActive = 1
			INNER JOIN dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
				AND ct.siteResourceID = f.controllingSiteResourceID;

			SELECT DISTINCT ct.categoryTreeName + '_' + c.categoryName + '_' + f.fieldReference as titleOnInvoice, '' as answer
			FROM dbo.tasks_tasks as t
			INNER JOIN dbo.tasks_projects as p on p.projectID = t.projectID
				AND t.taskID = @taskID
			INNER JOIN dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
			INNER JOIN dbo.cms_categoryTrees as ct on ct.controllingSiteResourceID = ai.siteResourceID
			INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_categories as c on c.categoryTreeID = ct.categoryTreeID AND c.isActive = 1
			INNER JOIN dbo.cf_fields as f on f.detailID = c.categoryID
				AND f.usageID = @usageID
				AND f.controllingSiteResourceID = ct.siteResourceID
			LEFT OUTER JOIN ##tmpTaskTagsFieldData as tmp on tmp.categoryID = c.categoryID
			WHERE tmp.categoryID is null
			AND f.isActive = 1
			AND len(f.fieldReference) > 0
				UNION ALL
			SELECT DISTINCT ct.categoryTreeName + '_' + c.categoryName + '_' + f.fieldReference as titleOnInvoice, '' as answer
			FROM dbo.cf_fields as f
			INNER JOIN dbo.cms_categories as c on c.categoryID = f.detailID
				AND f.usageID = @usageID
			INNER JOIN dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
				AND f.controllingSiteResourceID = ct.siteResourceID
			INNER JOIN dbo.tasks_taskCategories as tc on tc.categoryID = c.categoryID
				AND tc.taskID = @taskID
			LEFT OUTER JOIN ##tmpTaskTagsFieldData as tmp on tmp.fieldID = f.fieldID
			WHERE f.isActive = 1
			AND len(f.fieldReference) > 0
			AND tmp.fieldID is null
				UNION ALL
			SELECT titleOnInvoice, ANSWER
			FROM ##tmpTaskTagsFieldData;

			IF OBJECT_ID('tempdb..##tmpFieldData') is not null
				DROP TABLE ##tmpFieldData;
			IF OBJECT_ID('tempdb..##tmpTaskTagsFieldData') is not null
				DROP TABLE ##tmpTaskTagsFieldData;
		</cfquery>

		<cfloop query="local.qryTaskTagsFieldData">
			<cfif not structKeyExists(local.strTaskTagFields,local.qryTaskTagsFieldData.titleOnInvoice)>
				<cfset local.strTaskTagFields[local.qryTaskTagsFieldData.titleOnInvoice] = local.qryTaskTagsFieldData.answer>
			</cfif>
		</cfloop>

		<cfreturn local.strTaskTagFields>
	</cffunction>

	<cffunction name="showMessage" access="private" output="false" returntype="string">
		<cfargument name="errorCode" type="string" required="true">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.errorCode#">
			<cfcase value="norecipient">
				<cfset local.message = '<h4>No Recipients Found</h4><div>Please check the filtered recipients.</div>'>
			</cfcase>
			<cfcase value="noemailrecipient">
				<cfset local.message = '<h4>No Recipients with Defined Emails</h4><div>No filtered recipients had email addresses defined, so we were not able to send this message.</div>'>
			</cfcase>
			<cfdefaultcase>
				<cfset local.message = "<b>An error occurred. Try again or contact support for assistance.</b">
			</cfdefaultcase>
		</cfswitch>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
			<div style="padding:10px;">#JSStringFormat(local.message)#</div>
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getTaskStatusesForConditions" access="public" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct['arrtaskstatus'] = arrayNew(1)>

		<cfset local.qryTaskStatus = getTaskStatuses()>
		
		<cfloop query="local.qryTaskStatus">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['statusid'] = local.qryTaskStatus.taskStatusID>
			<cfset local.tmp['statusname'] = local.qryTaskStatus.statusName>
			<cfset arrayAppend(local.returnStruct['arrtaskstatus'], local.tmp)>
		</cfloop>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveTaskReminderAndDueDates" access="public" output="false" returntype="struct">
		<cfargument name="taskID" type="string" required="true">
		<cfargument name="reminderDate" type="string" required="true">
		<cfargument name="dateDue" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySaveTaskDates">
			UPDATE dbo.tasks_tasks
			SET nextReminderDate = NULLIF(<cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.reminderDate#">,''),
				dateDue = NULLIF(<cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.dateDue#">,''),
				dateLastModified = getDate()
			WHERE taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">
		</cfquery>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateTaskStatus" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="taskID" type="string" required="true">
		<cfargument name="statusName" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateTaskStatus">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @taskID int, @newTaskStatusID int, @oldTaskStatusID int, @enteredByMemberID int;
					SET @taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">;
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.mcproxy_orgID)#">;

					select @newTaskStatusID = taskStatusID 
					from dbo.tasks_statuses 
					where statusName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.statusName#">;

					select @oldTaskStatusID = taskStatusID 
					from dbo.tasks_tasks
					where taskID = @taskID;
					
					IF @oldTaskStatusID	<> @newTaskStatusID BEGIN
						BEGIN TRAN;
							UPDATE dbo.tasks_tasks
							SET taskStatusID = @newTaskStatusID,
								dateLastModified = getDate()
							WHERE taskID = @taskID;
							
							INSERT INTO dbo.tasks_statusHistory (taskID, updateDate, taskStatusID, oldTaskStatusID, enteredByMemberID)
							VALUES (@taskID, getDate(), @newTaskStatusID, @oldTaskStatusID, @enteredByMemberID);
						COMMIT TRAN;
					END

					SELECT @oldTaskStatusID AS oldTaskStatusID, @newTaskStatusID AS newTaskStatusID;
					
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfif local.qryUpdateTaskStatus.recordCount GT 0 AND local.qryUpdateTaskStatus.oldTaskStatusID NEQ local.qryUpdateTaskStatus.newTaskStatusID>
				<cfset local.arrTaskChanges = arrayNew(1)>
				<cfset local.oldStatusName = getTaskStatusName(statusID=local.qryUpdateTaskStatus.oldTaskStatusID)>
				<cfset local.thisChange = { ITEM="Task Status",OLDVALUE=local.oldStatusName,NEWVALUE=arguments.statusName }>
				<cfset arrayAppend(local.arrTaskChanges,local.thisChange)>
				<cfset createObject('component', 'model.system.platform.history').addTaskUpdateHistory(orgID=arguments.mcproxy_orgID, taskID=arguments.taskID,
							actorMemberID=session.cfcuser.memberData.memberID, mainMessage="Task Updated", changes=local.arrTaskChanges)>
			</cfif>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getTaskRecipientReferences" access="public" output="false" returntype="query">
		<cfargument name="recipientID" type="numeric" required="true">

		<cfset var qryRecipientReferences = "">

		<cfquery datasource="#application.dsn.platformMail.dsn#" name="qryRecipientReferences">
			SELECT rr.referenceID, m.memberID, p.projectID
			FROM platformMail.dbo.email_messageRecipientReferences rr
			INNER JOIN memberCentral.dbo.tasks_tasks AS t ON t.taskID = rr.referenceID
			INNER JOIN memberCentral.dbo.tasks_projects as p on p.projectID = t.projectID
			INNER JOIN memberCentral.dbo.ams_members AS m ON m.memberID = t.prospectMemberID
			WHERE rr.recipientID = <cfqueryparam value="#arguments.recipientID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY rr.recipientReferenceID;
		</cfquery>

		<cfreturn qryRecipientReferences>
	</cffunction>

	<cffunction name="getProspectCCDetail" access="public" output="false" returntype="string">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="payProfileID" type="numeric" required="true">

		<cfset var qryProspectCCDetail = ''>

		<cfquery name="qryProspectCCDetail" datasource="#application.dsn.membercentral.dsn#">
			select case when len(isnull(mpp.nickname,'')) > 0 then mpp.nickname + ' (' + mpp.detail + ')' else mpp.detail end as detail
			from dbo.ams_memberPaymentProfiles as mpp
			inner join dbo.mp_profiles as mp on mp.profileID = mpp.profileID
			where mpp.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			and mpp.payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">
			and mpp.status = 'A';
		</cfquery>
		
		<cfreturn qryProspectCCDetail.detail>
	</cffunction>

	<cffunction name="updateTaskPayProfile" access="public" output="false" returntype="void">
		<cfargument name="taskID" type="numeric" required="true">
		<cfargument name="payProfileID" type="numeric" required="true">

		<cfset var qryUpdatePayProfile = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdatePayProfile">
			update dbo.tasks_tasks
			set payProfileID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">, 0)
			where taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">;
		</cfquery>
	</cffunction>

	<cffunction name="getProspectContactRecords" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="taskID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="memPageNum" type="numeric" required="true">
		<cfargument name="perPageResults" type="numeric" required="true">
		<cfargument name="displayMode" type="string" required="true" hint="admin or frontend">

		<cfset var local = structNew()>
		<cfset local.returnStruct = {
				"success":true,
				"arrmembers":[],
				"strpage":{
					"rowsize":arguments.perPageResults,
					"currpage":arguments.memPageNum,
					"nextpage":arguments.memPageNum + 1,
					"prevpage":arguments.memPageNum - 1,
					"currcountstart":0,
					"currcountstop":0,
					"totalcount":0,
					"showmemberphoto":false
				}
			}>

		<cfset local.start = (local.returnStruct.strpage.prevPage * local.returnStruct.strpage.rowsize) + 1>
		<cfset local.end = (local.start + arguments.perPageResults) - 1>
		
		<cfquery name="local.qryProjectDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select mfu.fieldsetID, mfs.fieldsetName, mfs.nameFormat, mfs.showHelp, 
				p.siteResourceID as projectSiteResourceID, p.showMemberPhotos
			from dbo.tasks_tasks as t 
			inner join dbo.tasks_projects as p on p.projectID = t.projectID 
			inner join dbo.ams_memberFieldUsage as mfu on mfu.siteResourceID = p.siteResourceID
			inner join dbo.ams_memberFieldSets mfs on mfs.fieldsetID = mfu.fieldsetID
			where t.taskID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.taskID#">
			and mfu.area = 'prospectcontact';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif NOT local.qryProjectDetails.recordCount>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- get result fields --->
		<cfset local.returnStruct['strpage']['showmemberphoto'] = local.qryProjectDetails.showMemberPhotos>

		<cfquery name="local.qryContactRecords" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpContactRecordMemberIDs') IS NOT NULL 
					DROP TABLE ##tmpContactRecordMemberIDs;
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmpMembersWithFS') IS NOT NULL 
					DROP TABLE ##tmpMembersWithFS;
				CREATE TABLE ##tmpContactRecordMemberIDs (memberID int PRIMARY KEY);
				CREATE TABLE ##tmpMembers (memberID int INDEX IX_tmpMembers_memberID, lastname varchar(75), firstname varchar(75), 
					membernumber varchar(50), MCAccountStatus char(1), hasMemberPhotoThumb bit, company varchar(200), mc_row int);
				CREATE TABLE ##tmpMembersWithFS (MFSAutoID int IDENTITY(1,1) not null);

				DECLARE @orgID int, @memberID int, @posStart int, @posStartAndCount int, @resultsFSID int, @outputFieldsXML xml, @totalCount int;
				
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">; 
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.start#">;
				SET @posStartAndCount = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.end#">;
				SET @resultsFSID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryProjectDetails.fieldsetID#">;
			
				INSERT INTO ##tmpContactRecordMemberIDs (memberID)
				select childMember.memberID
				from dbo.ams_members as m
				inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID 
					and rr.masterMemberID = m.memberID
					and rr.isActive = 1
					and m.memberID = @memberID
					and m.status in ('A','I')
				inner join dbo.ams_members as childMember on childMember.orgID = @orgID 
					and childMember.memberID = rr.childMemberID
				where childMember.activeMemberID = childMember.memberID
				and childMember.status in ('A','I')
				group by childMember.memberID
					union
				select childMember.memberID
				from dbo.ams_members as m
				inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID 
					and rr.childMemberID = m.memberID
					and rr.isActive = 1
					and m.memberID = @memberID
					and m.status in ('A','I')
				inner join dbo.ams_members as masterMember on masterMember.orgID = @orgID 
					and masterMember.memberID = rr.masterMemberID
				inner join dbo.ams_recordRelationships as rrChildren on rrChildren.orgID = @orgID 
					and rrChildren.masterMemberID = masterMember.memberID
					and rrChildren.isActive = 1
				inner join dbo.ams_members as childMember on childMember.orgID = @orgID 
					and childMember.memberID = rrChildren.childMemberID
				where childMember.activeMemberID = childMember.memberID
				and childMember.status in ('A','I')
				and childMember.memberID <> @memberID
				group by childMember.memberID;
				
				SELECT @totalCount = count(*) from ##tmpContactRecordMemberIDs;

				INSERT INTO ##tmpMembers (memberID, lastname, firstname, membernumber, MCAccountStatus, hasMemberPhotoThumb, company, mc_row)
				select distinct m.memberid, m.lastname, m.firstname, m.membernumber, m.status, m.hasMemberPhotoThumb, m.company,
					ROW_NUMBER() OVER (order by m.lastname, m.firstname, m.membernumber) as mc_row
				from ##tmpContactRecordMemberIDs as tmp
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tmp.memberID;

				DELETE FROM ##tmpMembers
				WHERE mc_row NOT BETWEEN @posStart and @posStartAndCount;

				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@resultsFSID, @existingFields='m_lastname,m_firstname,m_membernumber,m_company,m_status',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmpMembers', @membersResultTableName='##tmpMembersWithFS',
					@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;

				-- return @outputFieldsXML in only the first row to reduce the query payload
				SELECT *, CASE WHEN mc_row = @posStart THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
				FROM (
					SELECT m.lastname, m.firstname, m.membernumber, m.MCAccountStatus, m.hasMemberPhotoThumb, m.company, 
						m.mc_row, tmpM.*, @totalCount as mc_totalCount
					FROM ##tmpMembers as m 
					INNER JOIN ##tmpMembersWithFS as tmpM on tmpM.memberID = m.memberID
				) tmp
				ORDER BY mc_row;

				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmpMembersWithFS') IS NOT NULL 
					DROP TABLE ##tmpMembersWithFS;
				IF OBJECT_ID('tempdb..##tmpContactRecordMemberIDs') IS NOT NULL 
					DROP TABLE ##tmpContactRecordMemberIDs;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif NOT local.qryContactRecords.recordCount>
			<cfreturn local.returnStruct>
		</cfif>

		<cfif local.qryContactRecords.recordCount>
			<cfset local.objMemberAdmin = createObject("component","model.admin.members.memberAdmin")>

			<cfset local.returnStruct['strpage']['totalcount'] = local.qryContactRecords.mc_totalCount>
			<cfif local.returnStruct.strpage.currpage eq 1>
				<cfset local.returnStruct['strpage']['currcountstart'] = 1>
				<cfset local.returnStruct['strpage']['currcountstop'] 	= local.returnStruct.strpage.rowsize>
			<cfelse>
				<cfset local.returnStruct['strpage']['currcountstart'] = local.returnStruct.strpage.rowSize * (local.returnStruct.strpage.currpage - 1) + 1>
				<cfset local.returnStruct['strpage']['currcountstop'] 	= local.returnStruct.strpage.rowsize * (local.returnStruct.strpage.currpage - 1) + local.qryContactRecords.recordCount>
			</cfif>
			<cfset local.returnStruct['strpage']['nextcountstart'] = local.returnStruct.strpage.currPage * local.returnStruct.strpage.rowsize + 1>

			<cfset local.xmlResultFields = local.qryContactRecords.mc_outputFieldsXML[1]>
			<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
			<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
			<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
			<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
			<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				SELECT *
				FROM [local].qryOutputFields
				WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
			</cfquery>

			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.mcproxy_orgID, includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
				<cfelse>
					<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
				</cfif>
			</cfloop>
			<cfset local.mc_combinedAddresses = structNew()>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
			<cfloop array="#local.tmp#" index="local.thisField">
				<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
				<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
					<cfset local.strKey = "t#local.thisATID#">
				<cfelse>
					<cfset local.strKey = local.thisATID>
				</cfif>
				<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
					<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
				</cfif>
			</cfloop>
			
			<cfloop query="local.qryContactRecords">
				<cfset local.tmpStr = structNew()>

				<cfset local.tmpStr['memberid'] = local.qryContactRecords.memberID>
				<cfset local.tmpStr['company'] = local.qryContactRecords.company>
				<cfset local.tmpStr['mcaccountstatus'] = local.qryContactRecords.MCAccountStatus>
				<cfset local.tmpStr['hasPhoto'] = local.qryContactRecords.hasMemberPhotoThumb>
				<cfif local.qryContactRecords.hasMemberPhotoThumb>
					<cfset local.tmpStr['memberphoto'] = local.qryContactRecords.membernumber & ".jpg">
				<cfelse>
					<cfset local.tmpStr['memberphoto'] = "">
				</cfif>

				<cfset local.tmpStr['mc_combinedName'] = local.qryContactRecords['Extended MemberNumber'][local.qryContactRecords.currentrow]>

				<!--- combine address fields if there are any --->
				<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
				<cfif StructCount(local.thisMem_mc_combinedAddresses)>
					<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
						<cfsavecontent variable="local.thisATFull">
							<cfoutput>
							<cfif left(local.thisATID,1) eq "t">
								<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
								<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
							<cfelse>
								<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
								<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
							</cfif>

							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>#local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>#local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>#local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>#local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]#</cfif>
							<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
							<cfif arrayLen(local.tmp2) is 1 and len(local.qryContactRecords[local.tmp2[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>, #local.qryContactRecords[local.tmp2[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]# </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])> #local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>#local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]# County<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])> #local.qryContactRecords[local.tmp[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
							<cfloop array="#local.tmp#" index="local.thisPT">
								<cfif len(local.qryContactRecords[local.thisPT.xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>
									<div>#local.thisPT.xmlAttributes.FieldLabel#: #local.qryContactRecords[local.thisPT.xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]#</div>
								</cfif>
							</cfloop>
							</cfoutput>
						</cfsavecontent>
						<cfset local.thisATfull = trim(replace(replace(Replace(Replace(local.thisATFull,'#chr(13)##chr(10)#','','ALL'),'#chr(9)#','', 'ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
						<cfif left(local.thisATfull,2) eq ", ">
							<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
						</cfif>
						<cfif len(local.thisATfull)>
							<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
						<cfelse>
							<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
						</cfif>
					</cfloop>
				</cfif>

				<!--- get recordtype if available --->
				<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.qryContactRecords[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>
					<cfset local.tmpStr['mc_recordType'] = local.qryContactRecords[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_recordType'] = "">
				</cfif>
				
				<!--- get membertypeid if available --->
				<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.qryContactRecords[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>
					<cfset local.tmpStr['mc_memberType'] = local.qryContactRecords[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_memberType'] = "">
				</cfif>
				
				<!--- get status if available --->
				<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.qryContactRecords[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>
					<cfset local.tmpStr['mc_memberStatus'] = local.qryContactRecords[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_memberStatus'] = "">
				</cfif>

				<!--- get last login date if available --->
				<cfif arrayLen(local.LastLoginDateInFS) is 1>
					<cfif len(local.qryContactRecords[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow])>
						<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: #DateFormat(local.qryContactRecords[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryContactRecords.currentrow],"m/d/yy")#'>
					<cfelse>
						<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: <span class="dim"><i>none</i></span>'>
					</cfif>
				<cfelse>
					<cfset local.tmpStr['mc_lastlogin'] = "">
				</cfif>

				<cfif StructCount(local.thisMem_mc_combinedAddresses)>
					<cfsavecontent variable="local.thisMemCombinedAddress">
						<cfoutput>
						<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
							<div><b style="font-size:95%; margin-right:15px;">#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#</b><br/>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['mc_combinedAddresses'] = rereplace(replace(replace(local.thisMemCombinedAddress,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['mc_combinedAddresses'] = "">
				</cfif>

				<cfif local.qryOutputFieldsForLoop.recordCount>
					<cfsavecontent variable="local.thisMemExtraInfo">
						<cfoutput>
						<cfloop query="local.qryOutputFieldsForLoop">
							<cfset local.currValue = local.qryContactRecords[local.qryOutputFieldsForLoop.fieldLabel][local.qryContactRecords.currentrow]>
							<cfif len(local.currValue)>
								<div>
									#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
									<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
										#dollarFormat(local.currValue)#
									<cfelse>
										<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
											<cfcase value="DATE">
												#dateFormat(local.currValue,"m/d/yyyy")#
											</cfcase>
											<cfcase value="STRING,DECIMAL2,INTEGER">
												#local.currValue#
											</cfcase>
											<cfcase value="BIT">
												#YesNoFormat(local.currValue)#
											</cfcase>
										</cfswitch>
									</cfif>
								</div>
							</cfif>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['mc_extraInfo'] = rereplace(replace(replace(local.thisMemExtraInfo,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['mc_extraInfo'] = "">
				</cfif>

				<cfset local.classifications = local.objMemberAdmin.getMemberClassifications(siteResourceID=local.qryProjectDetails.projectSiteResourceID, memberID=local.qryContactRecords.memberID, area='prospectcontact')>
				<cfif isArray(local.classifications) and not arrayIsEmpty(local.classifications)>
					<cfsavecontent variable="local.thisMemClassifications">
					<cfoutput>
						<br/>
						<cfloop from="1" to="#arrayLen(local.classifications)#" index="local.currentClass">
							<cfset local.qryClass = local.classifications[local.currentClass].qryClass>
							<cfset local.name = local.classifications[local.currentClass].name>
							<cfif local.qryClass.recordCount>
								<b>#replace(local.name,'_',' ','ALL')#</b><br/>
								<cfloop query="local.qryClass">
									- #local.qryClass.groupName#<br/>
								</cfloop>
								<br/>
							</cfif>
						</cfloop>
					</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['mc_memberclassifications'] = rereplace(replace(replace(local.thisMemClassifications,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['mc_memberclassifications'] = ''>
				</cfif>

				<cfset arrayAppend(local.returnStruct['arrmembers'],local.tmpStr)>
			</cfloop>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAdvanceFormula" access="public" returnType="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryAllAFs = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryAllAFs">
			SELECT AFID, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			ORDER BY afName
		</cfquery>

		<cfreturn qryAllAFs>
	</cffunction>

	<cffunction name="saveTaskAutomation" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="automationID" type="numeric" required="true">
		<cfargument name="automationType" type="string" required="true">
		<cfargument name="friendlyName" type="string" required="true">
		<cfargument name="projectID" type="numeric" required="true">
		<cfargument name="filterXML" type="string" required="true">
		<cfargument name="actionXML" type="string" required="true">
		<cfargument name="createdByMemberID" type="numeric" required="true">
		<cfargument name="runDate" type="string" required="true">
		<cfargument name="runAFID" type="numeric" required="true">
		<cfargument name="copyFromRuleID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfif arguments.automationID eq 0>
			
			<cfquery name="local.qryCreateTaskAutomation" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @orgID int, @siteID int, @projectID int, @automationType varchar(20), @friendlyName varchar(200),
						@filterXML xml, @actionXML xml, @createdByMemberID int, @runDate datetime, @runAFID int,
						@automationID int, @copyFromRuleID int, @ruleID int, @sourceProjectRuleXML xml, @sourceProjectOtherXML xml, 
						@sourceProjectRuleSQL varchar(max), @sourceProjectconditionCount int, @sourceProjectUsesOr bit, 
						@sourceProjectUsesAnd bit, @sourceProjectUsesExclude bit, @sourceProjectRuleIsSimple bit, @ruleVersionID int;

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
					SET @projectID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.projectID#">;
					SET @automationType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.automationType#">;
					SET @friendlyName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.friendlyName#">;
					SET @filterXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.filterXML#">;
					SET @actionXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.actionXML#">;
					SET @runDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.runDate#">;
					SET @runAFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.runAFID#">;
					SET @copyFromRuleID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.copyFromRuleID#">,0);
					SET @createdByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.createdByMemberID#">;

					SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

					<cfif arguments.copyFromRuleID gt 0>
						select @sourceProjectRuleXML = vgrv.ruleXML, @sourceProjectRuleSQL = vgrv.ruleSQL, @sourceProjectconditionCount = vgrv.conditionCount, 
							@sourceProjectUsesOr = vgrv.usesOr, @sourceProjectUsesAnd = vgrv.usesAnd, @sourceProjectUsesExclude = vgrv.usesExclude, 
							@sourceProjectRuleIsSimple = vgrv.isSimple
						from dbo.ams_virtualGroupRules as vgr
						inner join dbo.ams_virtualGroupRuleVersions as vgrv on vgrv.orgID = @orgID
							and vgrv.ruleVersionID = vgr.activeVersionID
						where vgr.ruleID = @copyFromRuleID
						and vgr.orgID = @orgID;
					</cfif>

					BEGIN TRAN;	
						EXEC dbo.tasks_createAutomations @siteID=@siteID, @automationType=@automationType, @friendlyName=@friendlyName,
							@projectID=@projectID, @filterXML=@filterXML, @actionXML=@actionXML, @createdByMemberID=@createdByMemberID, 
							@runDate=@runDate, @runAFID=@runAFID, @automationID=@automationID OUTPUT;

						IF @copyFromRuleID IS NOT NULL BEGIN
							SELECT @ruleID = ruleID
							FROM dbo.tasks_automations
							WHERE automationID = @automationID;

							SELECT @ruleVersionID = activeVersionID
							FROM dbo.ams_virtualGroupRules
							WHERE ruleID = @ruleID;

							UPDATE dbo.ams_virtualGroupRuleVersions
							SET ruleXML = @sourceProjectRuleXML,
								ruleSQL = @sourceProjectRuleSQL,
								conditionCount = @sourceProjectconditionCount,
								usesOR = @sourceProjectUsesOr,
								usesAND = @sourceProjectUsesAnd,
								usesExclude = @sourceProjectUsesExclude,
								isSimple = @sourceProjectRuleIsSimple,
								dateUpdated = GETDATE()
							WHERE ruleVersionID = @ruleVersionID;

							EXEC dbo.ams_populateVirtualGroupRuleConditionSets @ruleID=@ruleID, @ruleXML=@sourceProjectRuleXML, @ruleVersionID=@ruleVersionID;
						END
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
		<cfelse>
			<cfquery name="local.qryUpdateTaskAutomation" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.tasks_automations
				SET friendlyName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.friendlyName#">,
					filterXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.filterXML#">,
					actionXML = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.actionXML#">,
					AFrunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.runDate#">,
					runAFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.runAFID#">
				WHERE automationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.automationID#">
			</cfquery>
		</cfif>
	</cffunction>

	<cffunction name="getTasksAutomation" access="public" output="false" returntype="query">
		<cfargument name="automationID" type="numeric" required="yes">
		
		<cfset var qryTasksAutomation = "">

		<cfquery name="qryTasksAutomation" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select a.automationID, a.automationType, a.friendlyName, a.createdDate, a.runDate, 
				a.projectID, a.ruleID, a.AFrunDate, a.runAFID, a.filterXML, a.actionXML
			from dbo.tasks_automations as a
			inner join dbo.tasks_projects as p on p.projectID = a.projectID
			where a.automationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.automationID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryTasksAutomation>
	</cffunction>

	<cffunction name="getTaskMemberDetails" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		
		<cfset var qryTaskMember = "">

		<cfquery name="qryTaskMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select memberID, firstName, lastName, memberNumber
			from dbo.ams_members
			where memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryTaskMember>
	</cffunction>

	<cffunction name="getTaskMemberDetailsFromMemberNumber" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="memberNumber" type="string" required="yes">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryTaskMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select memberID, firstName, lastName, memberNumber
			from dbo.ams_members
			where memberNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.memberNumber#">
			and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryTaskMember.recordCount>
			<cfset local.returnStruct = {
					"memberid":local.qryTaskMember.memberID,
					"firstname":local.qryTaskMember.firstName,
					"lastname":local.qryTaskMember.lastName,
					"membernumber":local.qryTaskMember.memberNumber,
					"success":true
				}>
		<cfelse>
			<cfset local.returnStruct.success = true>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getTaskMemberGroupDetails" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="groupID" type="numeric" required="yes">
		
		<cfset var qryTaskMemberGroup = "">

		<cfquery name="qryTaskMemberGroup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select groupID, groupPathExpanded
			from dbo.ams_groups
			where groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">
			and orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryTaskMemberGroup>
	</cffunction>

	<cffunction name="getTasksCountInTaskImportQueue" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="projectID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryTasksCountInTaskImportQueue" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(distinct qi.itemUID) as importCount
			from dbo.tblQueueItems as qi
			inner join tblQueueItemData as qid on qid.itemUID = qi.itemUID
			inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.queueStatusID
			inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID 
				and qt.queueType = 'TaskImport'
			inner join dbo.tblQueueTypeDataColumns as qdc on qdc.queueTypeID = qt.queueTypeID 
				and qdc.columnID = qid.columnID
				and qdc.columnName = 'MCProjectID'
			where qid.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			and qs.queueStatus in ('ReadyToProcess','Processing','GrabbedForProcessing')
			<cfif arguments.projectID gt 0>
				and qid.columnValueInteger = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.projectID#">
			</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = { "taskscount":val(local.qryTasksCountInTaskImportQueue.importCount), "success":true}>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="showMassAddTaskErrors" access="package" output="false" returntype="string">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="taskFieldLabelPlural" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.data = "">

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif NOT arguments.strResult.success>
				<div class="alert alert-danger">
					<h5>Add #arguments.taskFieldLabelPlural# Errors</h5>
					
					<cfif structKeyExists(arguments.strResult,"importResultXML")>
						<cfset local.arrErrors = XMLSearch(arguments.strResult.importResultXML,"/import/errors/error")>
						<div class="mb-2">
							<cfif arrayLen(local.arrErrors) gt 200>
								<b>Only the first 200 errors are shown.</b><br/><br/>
							</cfif>
							<cfset local.thisErrNum = 0>
							<cfloop array="#local.arrErrors#" index="local.thisErr">
								<cfset local.thisErrNum = local.thisErrNum + 1>
								#local.thisErr.xmlAttributes.msg#<br/>
								<cfif local.thisErrNum is 200>
									<cfbreak>
								</cfif>
							</cfloop>
						</div>
					<cfelse>
						<div class="mb-2">#arguments.strResult.errMsg#</div>
					</cfif>
					<button type="button" class="btn btn-sm btn-secondary" onclick="cancelTask(false);">Return to #arguments.taskFieldLabelPlural#</button>
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>

</cfcomponent>