<cfset local.appBaseLink = CreateObject('component', 'model.apploader').getAppBaseLink(applicationInstanceID=local.mdSettings.applicationInstanceID,siteID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID) />
<cfset local.memberLocalLink = "/?" & local.appBaseLink />
<cfset local.memberExternalLink = "#arguments.event.getValue('mc_siteInfo.scheme','http')#://" & arguments.event.getValue('mc_siteInfo.mainhostname') & local.memberLocalLink />
<cfset local.permsGotoLink = CreateObject('component', 'model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct'>

<cfsavecontent variable="local.settingsJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/dhtmlxgrid/ext/dhtmlxgrid_drag.js"></script>
	<style>
		##justview1,##justview2 {
			    font-weight: 400;
				line-height: 1.5;
				color: ##3b3e66;
				background-color: ##fff;
				background-clip: padding-box;
				border: 1px solid ##d1d2db;
				border-radius: 0.29rem;
		} 
	</style>
	<script language="JavaScript">
		var #ToScript(local.permsGotoLink,"mca_perms_link")#
		function validateSettings() {
			var sfs = $('##SearchFieldSet').val();
			if (sfs != 0) {
				mca_hideAlert('err_settings');
				return true;
			} 
			mca_showAlert('err_settings', 'Please choose a Field Set for Search Form.', true);
			$("button##btnSave").prop('disabled',false);
			return false;
		}
		function showContactForm() {
			if ($('##includeContactFormOnDetails').is(':checked')) {
				$('##frmbcccontact').removeClass("d-none");
			} else {
				$('##frmbcccontact').addClass("d-none");
				$('##bccContactForm').val('');
			}
		}
		/* functions for field set selector widget */
		function getAvailableAndSelectedFieldSetsForResults(onCompleteFunc){
			let objParams = { siteResourceID:#local.siteResourceID#, area:'results' };
			TS_AJX('FIELDSETWIDGET','getAvailableAndSelectedFieldSetsJSON',objParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}
		function createMemberFieldUsageForResults(fsID, onCompleteFunc) {
			let objParams = { fieldSetID:fsID, siteResourceID:#local.siteResourceID#, area:'results' };
			TS_AJX('FIELDSETWIDGET','createMemberFieldUsage',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
		function getAvailableAndSelectedFieldSetsForDetails(onCompleteFunc){
			let objParams = { siteResourceID:#local.siteResourceID#, area:'details' };
			TS_AJX('FIELDSETWIDGET','getAvailableAndSelectedFieldSetsJSON',objParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}
		function createMemberFieldUsageForDetails(fsID, onCompleteFunc) {
			let objParams = { fieldSetID:fsID, siteResourceID:#local.siteResourceID#, area:'details' };
			TS_AJX('FIELDSETWIDGET','createMemberFieldUsage',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
		function getAvailableAndSelectedFieldSetsForCompare(onCompleteFunc){
			let objParams = { siteResourceID:#local.siteResourceID#, area:'compare' };
			TS_AJX('FIELDSETWIDGET','getAvailableAndSelectedFieldSetsJSON',objParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}
		function createMemberFieldUsageForCompare(fsID, onCompleteFunc) {
			let objParams = { fieldSetID:fsID, siteResourceID:#local.siteResourceID#, area:'compare' };
			TS_AJX('FIELDSETWIDGET','createMemberFieldUsage',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
		function moveMemberFieldFSRow(useID, dir, onCompleteFunc) {
			var objParams = { useID:useID, dir:dir };
			TS_AJX('FIELDSETWIDGET','fsMove',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
		function removeMemberFieldUsage(useID, onCompleteFunc) {
			var objParams = { useID:useID };
			TS_AJX('FIELDSETWIDGET','fsRemove',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
	</script>
	</cfoutput>
</cfsavecontent>	
<cfhtmlhead text="#application.objCommon.minText(local.settingsJS)#">	

<cfoutput>
	<div>
		<div id="err_settings" class="alert alert-danger mb-2 mt-2 d-none"></div>
		
		<form name="mdSettings" method="post" action="#this.link.saveSettings#&mdID=#arguments.event.getValue('mdID')#" onsubmit="return validateSettings();">
			<div class="row">
				<div class="col text-right">
				<span id="saveResponse"></span>
				<button type="button" name="btnShowPerms" class="btn btn-sm btn-secondary" onclick="mca_showPermissions(#local.siteResourceID#,'#encodeForJavaScript(local.resourceName)#');">Permissions</button>
				<button type="submit" class="btn btn-sm btn-primary" name="btnSave"  id="btnSave">Save Settings</button>
				</div>
			</div>

			<input type="hidden" name="applicationInstanceID" value="#local.mdSettings.applicationInstanceID#">
			<input type="hidden" name="useID" value="#local.data.searchUsageDetails.useID#">
			
			<div class="form-group mt-2">
				<div class="form-label-group">
					<input type="text" name="applicationInstanceName" id="applicationInstanceName" value="#local.mdSettings.applicationInstanceName#" class="form-control">
					<label for="applicationInstanceName">Directory Name</label>
				</div>
			</div>

			<div class="form-group">
				<div class="form-label-group">
					<select name="recordsPerPage" id="recordsPerPage"  class="form-control">
						<option value="5" <cfif local.mdSettings.recordsperpage eq 5>selected</cfif>>5</option>
						<option value="10" <cfif local.mdSettings.recordsperpage eq 10>selected</cfif>>10</option>
						<option value="15" <cfif local.mdSettings.recordsperpage eq 15>selected</cfif>>15</option>
						<option value="25" <cfif local.mdSettings.recordsperpage eq 25>selected</cfif>>25</option>
				  	</select>
					<label for="recordsPerPage">Records Shown Per Page</label>
				</div>
			</div>

			<div class="form-group">
				<div class="form-label-group">
					<select name="maxSearchResults" id="maxSearchResults"  class="form-control">
						<option value="0" <cfif local.mdSettings.maxsearchresults eq 0>selected</cfif>>Unlimited</option>
						<option value="10" <cfif local.mdSettings.maxsearchresults eq 10>selected</cfif>>10</option>
						<option value="25" <cfif local.mdSettings.maxsearchresults eq 25>selected</cfif>>25</option>
						<option value="50" <cfif local.mdSettings.maxsearchresults eq 50>selected</cfif>>50</option>
						<option value="100" <cfif local.mdSettings.maxsearchresults eq 100>selected</cfif>>100</option>
						<option value="250" <cfif local.mdSettings.maxsearchresults eq 250>selected</cfif>>250</option>
						<option value="500" <cfif local.mdSettings.maxsearchresults eq 500>selected</cfif>>500</option>
						<option value="1000" <cfif local.mdSettings.maxsearchresults eq 1000>selected</cfif>>1000</option>
						<option value="2500" <cfif local.mdSettings.maxsearchresults eq 2500>selected</cfif>>2500</option>
					</select>
					<label for="maxSearchResults">Maximum Results Shown</label>
				</div>
			</div>

			<div class="form-group">
				<div class="custom-control custom-switch">
					<input type="checkbox" name="inactiveMembersVisible" id="inactiveMembersVisible" value="1" class="custom-control-input" <cfif local.mdSettings.inactiveMembersVisible is 1> checked="checked"</cfif>>
					<label class="custom-control-label" for="inactiveMembersVisible">Show Inactive Members</label>
				</div>
			</div>

			<div class="form-group">
				<div class="custom-control custom-switch">
					<input type="checkbox" name="enableStats" id="enableStats" value="1" class="custom-control-input" <cfif local.mdSettings.enableStats is 1> checked="checked"</cfif>>
					<label class="custom-control-label" for="enableStats">Enable Statistics</label>
				</div>
			</div>

			<div class="form-group">
				<div class="custom-control custom-switch">
					<input type="checkbox" name="enableCompare" id="enableCompare" value="1" class="custom-control-input" <cfif local.mdSettings.enableCompare is 1> checked="checked"</cfif>>
					<label class="custom-control-label" for="enableCompare">Enable Comparison</label>
				</div>
			</div>

			<cfif local.hasSocialNetwork>
				<div class="form-group">
					<div class="form-label-group">
						<select name="showPhotoRule" id="showPhotoRule" class="form-control">
							<option value="1" <cfif (local.mdSettings.showPhotoRule eq '') OR (local.mdSettings.showPhotoRule eq 1)>selected</cfif>>Show Member Photo Only</option>
							<option value="2" <cfif local.mdSettings.showPhotoRule eq 2>selected</cfif>>Show Social Network Photo Only</option>
							<option value="3" <cfif local.mdSettings.showPhotoRule eq 3>selected</cfif>>Show Photo, prefer Member Photo</option>
							<option value="4" <cfif local.mdSettings.showPhotoRule eq 4>selected</cfif>>Show Photo, prefer Social Network Photo</option>
						</select>
						<label for="showPhotoRule">Show Photos</label>
					</div>
				</div>
			</cfif>

			<div class="form-group">
				<div class="form-label-group">
					<input type="text" name="distanceLabel" id="distanceLabel" value="#local.mdSettings.distanceLabel#" class="form-control">
					<label for="distanceLabel">Distance Label</label>
				</div>
			</div>

			<div class="card card-box mt-2">
				<div class="card-header bg-light">
				<div class="card-header--title font-weight-bold font-size-md">Contact Form on Detail View</div>
				</div>
				<div class="card-body">

					<div class="form-group row pt-1">
						<div class="col-sm-3">Show Contact Form on Details</div>
						<div class="col-sm-9">
							<div class="custom-control custom-switch">
								<input type="checkbox" name="includeContactFormOnDetails" id="includeContactFormOnDetails" value="1" onclick="showContactForm();" class="custom-control-input" <cfif local.mdSettings.includeContactFormOnDetails is 1> checked="checked"</cfif>>
								<label class="custom-control-label" for="includeContactFormOnDetails"></label>
							</div>
						</div>
					</div>

					<div id="frmbcccontact" <cfif local.mdSettings.includeContactFormOnDetails is not 1>class="d-none"</cfif>>

						<div class="form-group">
							<div class="form-label-group">
								<input type="text" name="bccContactForm" id="bccContactForm" value="#local.mdSettings.bccContactForm#" class="form-control">
								<label for="bccContactForm">Contact Form BCC E-mail address</label>
							</div>
						</div>
		
						<div class="form-group">
							<div class="form-label-group">
								<input type="hidden" name="contactContentID" value="#local.mdSettings.contactContentID#">
								#application.objWebEditor.showContentBoxWithLinks(fieldname='contactContent', fieldlabel='Contact Instructions:', contentID=local.mdSettings.contactContentID, content=local.mdSettings.contactContent, allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="card card-box mt-2">
				<div class="card-header bg-light">
				<div class="card-header--title font-weight-bold font-size-md">Member Field Sets</div>
				</div>
				<div class="card-body">
					<div class="form-group row">
						<label for="SearchFieldSet" class="col-sm-3 col-form-label-sm font-size-md">Field Set for Search Form</label>
						<div class="col-sm-9">
							<input type="hidden" name="origSearchFieldSet" value="#local.data.searchFieldSet#">
							#local.strSearchFSSelector.html#
						</div>
					</div>
					<br>
					<div class="form-group row mt-10">
						<label for="" class="col-sm-3 col-form-label-sm font-size-md">Field Sets for Results
							<small class="form-text">
								specified in order of precedence
							</small>
						</label>
						<div class="col-sm-9">
							#local.strMemberDirectoryFieldSetsResults.html#
						</div>
					</div>
					<br>
					<div class="form-group row mt-10">
						<label for="" class="col-sm-3 col-form-label-sm font-size-md">Field Sets for Details
							<small class="form-text">
								specified in order of precedence
							</small>
						</label>
						<div class="col-sm-9">
							#local.strMemberDirectoryFieldSetsDetails.html#
						</div>
					</div>
					<br>
					<cfif local.mdSettings.enableCompare is 1>
						<div class="form-group row mt-10">
							<label for="" class="col-sm-3 col-form-label-sm font-size-md">Field Sets for Comparisons
								<small class="form-text">
									specified in order of precedence
								</small>
							</label>
							<div class="col-sm-9">
								#local.strMemberDirectoryFieldSetsComparisons.html#
							</div>
						</div>
					</cfif>
				</div>
			</div>	


			<div class="form-group mt-2">
				<div class="form-label-group">
						<input type="text" name="justview1" readonly="readonly" id="justview1" onclick="this.select();" value="#local.memberLocalLink#" class="col-sm-10">
						<a target="_blank" href="#local.memberLocalLink#" class="col-sm-2">Test Link</a>
					<label for="justview1" >Internal Link</label>
				</div>
			</div>

			<div class="form-group ">
				<div class="form-label-group">
						<input type="text" name="justview2" readonly="readonly" onclick="this.select();" id="justview2" value="#local.memberExternalLink#" class="col-sm-10">
						<a target="_blank" href="#local.memberExternalLink#" class="col-sm-2">Test Link</a>
					<label for="justview2" >External Link</label>
				</div>
			</div>
				
		</form>
	</div>
</cfoutput>