<!--- If only one format, skip step 2 --->
<cfif local.qryAvailableProductFormats.recordCount is 1>
	<cflocation url="#arguments.event.getValue('mainurl')#&sa=AddToCart&ItemID=#arguments.event.getValue('itemid',0)#&Quantity_#local.qryAvailableProductFormats.formatID#_#local.qryAvailableProductFormats.rateID#=1" addtoken="no">
</cfif>

<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
<cfset local.qryPurchaser = application.objMember.getMemberInfo(val(local.store[arguments.appInstanceID].CrtMemberID))>

<cfset local.stopReg = false>

<div class="row-fluid">
	<div id="" class="span12">
		<p>
			<span class="lead"><small>Step 2 of 2: Select Product Details</small></span><br />
		</p>
	</div>
</div>

<cfoutput>
<style type="text/css">
	##chooseFmt th { border-top:0px; border-bottom:1px solid ##ddd; }
	##chooseFmt td { border-top:0px; }
	.BB { border-bottom:1px solid ##ddd; }
</style>
<form name="step2Form" method="post" action="#arguments.event.getValue('mainurl')#" onsubmit="return doS2Validate(false)">	
<input type="hidden" name="sa" Value="AddToCart" />
<input type="Hidden" name="ItemID" value="#arguments.event.getValue('itemid',0)#" />

<div class="row-fluid">
	<div class="span10">
		<cfif local.qryAvailableProductFormats.recordCount>
			<div class="lead"><em>#encodeForHTML(local.qryProducts.contenttitle)#</em> is available in several formats.</div>
			<br/>
			<div>Add one or more of the following options to your cart:</div>
			<div style="margin:20px 0 0 20px;">
				<table cellspacing="0" cellpadding="4" id="chooseFmt" class="table">
				<tr><th class="BB">Qty</th><th class="BB">&nbsp;</th><th class="BB">Product Format</th></tr>
				<cfloop query="local.qryAvailableProductFormats">
					<tr>
						<td>
							<input type="text" autocomplete="off" name="Quantity_#local.qryAvailableProductFormats.formatID#_#local.qryAvailableProductFormats.rateID#" size="2" value="0" style="width:100%;" data-inventory="#local.qryAvailableProductFormats.inventory#" data-formatpurchasedcount=#local.qryAvailableProductFormats.formatpurchasedcount# data-formatname="#encodeForHTMLAttribute(local.qryAvailableProductFormats.name)#" <cfif NOT local.qryAvailableProductFormats.isAvailable>disabled<cfelse>class="qty"</cfif>><br/>
						</td>
						<cfif NOT local.qryAvailableProductFormats.isAvailable>
							<td><b>[SOLD OUT]</b></td>
						<cfelse>
							<td>&nbsp;</td>	
						</cfif>
						<td>
							<b>#local.qryAvailableProductFormats.Name#</b>
							&nbsp; <i><cfif local.qryAvailableProductFormats.rate eq 0>FREE<cfelse>#dollarformat(local.qryAvailableProductFormats.rate)#<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1> #arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</cfif></cfif></i>
							<cfif local.qryAvailableProductFormats.numAffirmationsIncluded gt 0>
								&nbsp; (#local.qryAvailableProductFormats.numAffirmationsIncluded# affirmation<cfif local.qryAvailableProductFormats.numAffirmationsIncluded is not 1>s</cfif> included)
							</cfif>
						</td>
					</tr>
				</cfloop>
				</table>
			</div>
			<cfsavecontent variable="local.jsValidation">
				<cfoutput>
				var qtysum = 0;
				var thisqty = 0;
				$('##chooseFmt :input.qty').each(function() {
					thisqty = parseInt($(this).val().toString().replace(/[^0-9]/g,''));
					$(this).val(thisqty);
					qtysum += thisqty;
				});
				if(qtysum==0) strErr += 'No options have been selected.<br/>';
				$('##chooseFmt input.qty').each(function (){
					let enteredValue = parseInt($(this).val(), 10) || 0;;
					let inventory = parseInt($(this).data('inventory'), 10);
					let formatpurchasedcount = parseInt($(this).data('formatpurchasedcount'), 10);
					if (inventory > 0 && enteredValue + formatpurchasedcount > inventory) {
						let qtyLeft = inventory - formatpurchasedcount;
						strErr +='Order quantity exceeds available inventory.' + (qtyLeft > 0 ? (' ' + $(this).data('formatname') + ' has only '+qtyLeft+' unit'+ (qtyLeft > 1 ? 's' : '') + ' left.') : '' ) + '<br/>';
					}
				});
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.jsValidation = "">
			<div class="lead">We're Sorry...</div>
			<br/>
			<div>
				<b><i><cfif len(local.qryProducts.contenttitle)>#encodeForHTML(local.qryProducts.contenttitle)#<cfelse>This product</cfif></i> is not available for you to purchase at this time.</b><br/>
				There are no available formats for you to select from.<br/><br/>
				<cfset local.stopReg = true>
			</div>
		</cfif>
	</div>
	<div class="span2">
		<div>
			<div><b>Purchaser:</b></div>
			<div>
				<cfif local.qryPurchaser.hasPrefix is 1 and len(local.qryPurchaser.prefix)>#local.qryPurchaser.prefix# </cfif>#local.qryPurchaser.firstname#<cfif local.qryPurchaser.hasMiddleName is 1 and len(local.qryPurchaser.middlename)> #local.qryPurchaser.middlename#</cfif> #local.qryPurchaser.lastname# <cfif local.qryPurchaser.hasSuffix is 1 and len(local.qryPurchaser.suffix)>#local.qryPurchaser.suffix#</cfif> <cfif local.qryPurchaser.hasprofessionalsuffix is 1 and len(local.qryPurchaser.professionalsuffix)>#local.qryPurchaser.professionalsuffix#</cfif><br/>
				<cfif len(local.qryPurchaser.company)>#encodeForHTML(local.qryPurchaser.company)#</cfif>
			</div>
			<br />
		</div>
	</div>
</div>
	

	

<br />		


	
<div><div id="store2_err_div" style="display:none;margin:12px 0 0 0;"></div></div>
<br/><br/>
<div>
	<fieldset>
		<legend class="lead">Action Items</legend>
		<cfif NOT local.stopReg>
		<div>
			<button type="submit" name="btnSubmit" class="btn" style="width:110px;font-weight:bold;"><img src="/assets/common/images/events/plus.jpg" align="left" />Continue</button>
			Add product to cart.
		</div>
		</cfif>
		<br/>
		<div>
			<button type="button" name="btnCancel" class="btn" style="width:110px;font-weight:bold;" onClick="cancelReg(this);"><img src="/assets/common/images/events/x.jpg" align="left" />Cancel</button>
		</div>
	</fieldset>
</div>

</form>
</cfoutput>

<cfsavecontent variable="local.step2validate">
	<cfoutput>
	<script language="javascript">
	showStep2Alert = function(msg) {
		var abox = document.getElementById('store2_err_div');
			abox.innerHTML = msg;
			abox.className = 'alert';
			abox.style.display = '';
	};
	hideStep2Alert = function() {
		var abox = document.getElementById('store2_err_div');
			abox.innerHTML = '';
			abox.style.display = 'none';
	};
	doS2Validate = function(supress) {
		var _CF_this = document.forms['step2Form'];
		strErr = '';
		#local.jsValidation#
		if (strErr.length > 0) {
			if (!supress) showStep2Alert(strErr);
			return false;
		} else {
			hideStep2Alert();
			return true;
		}
	}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.step2validate)#">