<cfcomponent output="false">

	<cffunction name="getFieldSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="selectorID" type="string" required="yes" hint="id for hidden input control">
		<cfargument name="selectedValue" type="numeric" required="no" default="0" hint="selected fieldSetID value">
		<cfargument name="selectedFieldSetName" type="string" required="no" default="" hint="selected fieldSet name">
		<cfargument name="allowBlankOption" type="boolean" required="no" default="true">
		<cfargument name="inlinePreviewSectionID" type="string" required="no" default="" hint="provide only if selector is within a modal to avoid using modal again for fieldset preview/edit">
		<cfargument name="qryFieldSets" type="query" required="no" hint="override query">
		<cfargument name="getFieldSetDataFunc" type="string" required="no" default="" hint="override javascript function name that is present on the calling form">
		<cfargument name="usageMode" type="string" required="no" default="fsWidget" hint="override this value to include specific logic at places">
		<cfargument name="fieldLabel" type="string" required="no" default="" hint="floating label text">
	
		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>

		<cfif structKeyExists(arguments,"qryFieldSets")>
			<cfset local.qryFieldSets = arguments.qryFieldSets>
		<cfelse>
			<cfset local.qryFieldSets = CreateObject("component","model.admin.MemberFieldSets.MemberFieldSets").getFieldSets(siteID=arguments.siteID, returnAll=true, orderByCategory=true)>
		</cfif>

		<cfset local.selectedFieldSetID = 0>
		<cfset local.selectedFieldSetLabel = "Choose Field Set">

		<cfif arguments.selectedValue GT 0 OR len(arguments.selectedFieldSetName)>
			<cfquery name="local.qryFieldSetSelected" dbtype="query">
				select fieldSetID, fieldSetName
				from [local].qryFieldSets
				<cfif arguments.selectedValue GT 0>
					where fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.selectedValue#">
				<cfelse>
					where fieldSetName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.selectedFieldSetName#">
				</cfif>
			</cfquery>

			<cfif local.qryFieldSetSelected.recordCount>
				<cfset local.selectedFieldSetID = local.qryFieldSetSelected.fieldSetID>
				<cfset local.selectedFieldSetLabel = local.qryFieldSetSelected.fieldSetName>
			</cfif>
		</cfif>

		<cfif len(arguments.inlinePreviewSectionID)>
			<cfset local.useInlinePreview = true>
			<cfset local.mode = "stream">
		<cfelse>
			<cfset local.useInlinePreview = false>
			<cfset local.mode = "direct">
		</cfif>
		
		<cfset local.editFieldSetLink = local.objAdmin.buildLinkToTool(toolType='MemberFieldSetAdmin',mca_ta='editFieldSet') & "&usageMode=#arguments.usageMode#&uniqueWidgetSelectorID=#arguments.selectorID#&mode=#local.mode#">
		<cfset local.previewFieldSetLink = local.objAdmin.buildLinkToTool(toolType='MemberFieldSetAdmin',mca_ta='previewFieldSet') & "&mode=stream">
			
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_fieldsets_single.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getMultipleFieldSetSelector" access="public" output="false" returntype="struct">
		<cfargument name="selectorID" type="string" required="yes" hint="a unique id for this selector">
		<cfargument name="getFieldSetDataFunc" type="string" required="yes" hint="javascript function name that is present on the calling form">
		<cfargument name="addFieldSetUsageFunc" type="string" required="yes" hint="javascript function name that is present on the calling form">
		<cfargument name="removeFieldSetUsageFunc" type="string" required="yes" hint="javascript function name that is present on the calling form">
		<cfargument name="inlinePreviewSectionID" type="string" required="no" default="" hint="provide only if selector is within a modal to avoid using modal again for fieldset preview/edit">
		<cfargument name="hasPermissionAction" type="boolean" required="no" default="false">
		<cfargument name="hasOrderingAction" type="boolean" required="no" default="false">
		<cfargument name="orderFieldSetFunc" type="string" required="no" default="" hint="javascript function name that is present on the calling form - required only if hasOrderingAction is true">
		<cfargument name="selectedFSGridHeight" type="boolean" required="no" default="165" hint="override height of selected field sets grid in pixels">
		<cfargument name="usageMode" type="string" required="no" default="fsWidgetMultiple" hint="override this value to include specific logic at places">
		<cfargument name="hasEditRights" type="boolean" required="no" default="yes">
		<cfargument name="showViewUsageActionMode" type="string" required="no" default="" hint="show specific usages for the selected fieldset">
		<cfargument name="fieldLabel" type="string" required="no" default="" hint="floating label text">
	
		<cfset var local = structNew()>
		<cfset local.data = { selectorID = arguments.selectorID, html = '' }>
		<cfset local.objAdmin = CreateObject("component","model.admin.admin")>
		
		<cfif len(arguments.inlinePreviewSectionID)>
			<cfset local.useInlinePreview = true>
			<cfset local.mode = "stream">
		<cfelse>
			<cfset local.useInlinePreview = false>
			<cfset local.mode = "direct">
		</cfif>
		
		<cfset local.editFieldSetLink = local.objAdmin.buildLinkToTool(toolType='MemberFieldSetAdmin',mca_ta='editFieldSet') & "&usageMode=#arguments.usageMode#&uniqueWidgetSelectorID=#arguments.selectorID#&mode=#local.mode#">
		<cfset local.previewFieldSetLink = local.objAdmin.buildLinkToTool(toolType='MemberFieldSetAdmin',mca_ta='previewFieldSet') & "&mode=stream">
		<cfset local.viewFieldSetUsagesLink = local.objAdmin.buildLinkToTool(toolType='MemberFieldSetAdmin',mca_ta='viewFieldSetUsagesByMode') & "&usageMode=#arguments.showViewUsageActionMode#&mode=stream">
		
		<cfif arguments.hasPermissionAction>
			<cfset local.permsGotoLink = local.objAdmin.buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct'>
		</cfif>
			
		<cfsavecontent variable="local.data.html">
			<cfinclude template="dsp_fieldsets_multiple.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFieldSetsJSON" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": true, "arravailablefieldsets": [] }>
		
		<cfset local.qryFieldSets = createObject("component","model.admin.MemberFieldSets.MemberFieldSets").getFieldSets(siteID=arguments.mcproxy_siteID, returnAll=true, orderByCategory=true)>
		
		<cfoutput query="local.qryFieldSets" group="categoryID">
			<cfset local.tmpStr = { "categoryid":local.qryFieldSets.categoryID, "categoryname":local.qryFieldSets.categoryName, "arrfieldsets":[] }>
			<cfoutput>
				<cfset arrayAppend(local.tmpStr.arrfieldsets,{ "fieldsetid":local.qryFieldSets.fieldsetID, "fieldsetname":local.qryFieldSets.fieldsetName })>
			</cfoutput>
			<cfset arrayAppend(local.returnStruct.arravailablefieldsets,local.tmpStr)>
		</cfoutput>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getAvailableAndSelectedFieldSetsJSON" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="area" type="string" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="orderSelectedFieldsSetsByName" type="string" required="false" default="false">
		<cfargument name="removeUsageRightsMode" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": true, "availablefieldsetscount": 0, "arravailablefieldsets": [],	"arrselectedfieldsets": [] }>
		<cfset local.SiteSRID = application.objSiteInfo.getSiteInfo(arguments.mcproxy_siteCode).siteSiteResourceID>
				
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryFieldSets">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @controllingSiteResourceID int, @categoryTreeID int, @usageSiteResourceID int, @area varchar(20);
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
			SET @area = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.area#">;
			SET @usageSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">;
			SET @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.SiteSRID#">;

			SELECT @categoryTreeID = categoryTreeID
			FROM dbo.cms_categoryTrees 
			WHERE controllingSiteResourceID = @controllingSiteResourceID
			AND categoryTreeCode = 'MEMFIELDSETS';

			<cfif arguments.removeUsageRightsMode eq "reportsettings">
				IF OBJECT_ID('tempdb..##tmpReportFS') IS NOT NULL
					DROP TABLE ##tmpReportFS;
				CREATE TABLE ##tmpReportFS (fsuid uniqueidentifier PRIMARY KEY); 

				INSERT INTO ##tmpReportFS (fsuid)
				SELECT distinct fs1.fs.value('@uid', 'NVARCHAR(50)') as fsuid
				FROM dbo.rpt_savedReports r
				OUTER APPLY r.otherXML.nodes('/report//fieldset') as fs1(fs)
				WHERE r.siteID = @siteID
				AND fs1.fs.value('@uid', 'NVARCHAR(50)') is not null;
			</cfif>

			SELECT c.categoryID, c.categoryName, mfs.fieldSetID, mfs.fieldsetName, mfs.uid AS fieldSetUID, u.useID, u.fieldsetOrder,
				case when exists (SELECT resourceID FROM dbo.cms_siteResourceRightsCache WHERE resourceID = u.useSiteResourceID and siteID = @siteID) then 1 else 0 end AS hasrights,
				u.useSiteResourceID,
				<cfif arguments.removeUsageRightsMode eq "reportsettings">
					CASE WHEN tmpfs.fsuid is null then 1 else 0 END AS hasRemoveUsageRights
				<cfelse>
					1 AS hasRemoveUsageRights
				</cfif>
			FROM dbo.cms_categories AS c
			INNER JOIN dbo.ams_memberFieldSets AS mfs ON mfs.categoryID = c.categoryID AND mfs.siteID = @siteID
			LEFT OUTER JOIN dbo.ams_memberFieldUsage AS u 
				INNER JOIN dbo.cms_siteResources sr ON sr.siteID = @siteID and sr.siteResourceID = u.siteResourceID and sr.siteResourceStatusID = 1
				ON u.fieldsetID = mfs.fieldsetID
				AND u.siteResourceID = @usageSiteResourceID
				AND u.area = @area
			<cfif arguments.removeUsageRightsMode eq "reportsettings">
				LEFT OUTER JOIN ##tmpReportFS as tmpfs on tmpfs.fsuid = mfs.[uid]
			</cfif>
			WHERE c.categoryTreeID = @categoryTreeID
			AND c.isActive = 1
			ORDER BY c.categoryName, mfs.fieldsetName;

			<cfif arguments.removeUsageRightsMode eq "reportsettings">
				IF OBJECT_ID('tempdb..##tmpReportFS') IS NOT NULL
					DROP TABLE ##tmpReportFS;
			</cfif>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.qryAvailableFieldSets = local.qryFieldSets.filter(function(thisRow) { return NOT val(arguments.thisRow.useID); })>

		<cfset local.returnStruct["availablefieldsetscount"] = local.qryAvailableFieldSets.recordCount>

		<cfoutput query="local.qryAvailableFieldSets" group="categoryID">
			<cfset local.tmpStr = { "categoryid":local.qryAvailableFieldSets.categoryID, "categoryname":local.qryAvailableFieldSets.categoryName, "arrfieldsets":[] }>
			<cfoutput>
				<cfset arrayAppend(local.tmpStr.arrfieldsets, {"fieldsetid":local.qryAvailableFieldSets.fieldsetID, "fieldsetname":local.qryAvailableFieldSets.fieldsetName, "fieldsetuid":local.qryAvailableFieldSets.fieldSetUID})>
			</cfoutput>
			<cfset arrayAppend(local.returnStruct.arravailablefieldsets,local.tmpStr)>
		</cfoutput>

		<cfquery name="local.qrySelectedFieldSets" dbtype="query">
			SELECT useID, fieldSetID, fieldsetName, fieldSetUID, hasrights, useSiteResourceID, hasRemoveUsageRights
			FROM [local].qryFieldSets
			WHERE useID IS NOT NULL
			<cfif arguments.orderSelectedFieldsSetsByName>
				ORDER BY fieldsetName
			<cfelse>
				ORDER BY fieldsetOrder
			</cfif>
		</cfquery>

		<cfloop query="local.qrySelectedFieldSets">
			<cfset local.tmpStr = {
				"useid": local.qrySelectedFieldSets.useID,
				"fieldsetid": local.qrySelectedFieldSets.fieldSetID,
				"fieldsetname": local.qrySelectedFieldSets.fieldsetName,
				"fieldsetnameenc": encodeForJavaScript(local.qrySelectedFieldSets.fieldsetName),
				"fieldsetuid": local.qrySelectedFieldSets.fieldSetUID,
				"hasrights": local.qrySelectedFieldSets.hasrights,
				"usesiteresourceid": local.qrySelectedFieldSets.useSiteResourceID,
				"hasremoveusagerights": local.qrySelectedFieldSets.hasRemoveUsageRights
			}>
			<cfset arrayAppend(local.returnStruct.arrselectedfieldsets,local.tmpStr)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createMemberFieldUsage" access="public" output="false" returntype="struct">
		<cfargument name="fieldSetID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="area" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_createMemberFieldUsage">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" variable="@siteResourceID" value="#arguments.siteResourceID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" variable="@fieldsetID" value="#arguments.fieldSetID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" variable="@area" value="#arguments.area#">
			<cfprocparam type="in" cfsqltype="CF_SQL_BIT" variable="@createSiteResourceID" value="1">
			<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.useID">
		</cfstoredproc>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="fsRemove" access="public" output="false" returntype="struct">
		<cfargument name="useID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.fsRemove">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					declare @useID int, @useSiteResourceID int;
					set @useID = <cfqueryparam value="#arguments.useID#" cfsqltype="cf_sql_integer">;

					SELECT @useSiteResourceID = useSiteResourceID
					FROM dbo.ams_memberFieldUsage
					WHERE useID = @useID;

					BEGIN TRAN;
						IF @useSiteResourceID is not null
							UPDATE dbo.cms_siteResources
							SET siteResourceStatusID = 3
							WHERE siteResourceID = @useSiteResourceID;

						DELETE FROM	dbo.ams_memberFieldUsage
						WHERE useID = @useID;
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="fsMove" access="public" output="false" returntype="struct">
		<cfargument name="useID" type="numeric" required="yes">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_moveMemberFieldUsage">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.useID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
		</cfstoredproc>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>