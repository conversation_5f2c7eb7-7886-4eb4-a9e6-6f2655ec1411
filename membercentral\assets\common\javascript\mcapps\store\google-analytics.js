function triggerProductsViewItemList(pageName, products, listName) {
	if (Array.isArray(products) && products.length > 0) {
		var formattedData = products.map(product => {
			var item = {
				item_id: product.productID,
				item_name: product.productName,
				item_category: product.categoryName
			};
			
			return item;
		});
		gtag("event", "view_item_list", {
			item_list_id: pageName,
			item_list_name: listName,
			items: formattedData
		});
	}
}

function triggerProductViewItem(products) { 
	if (Array.isArray(products) && products.length > 0) {
		var formattedData = products.map(product => {
			var item = {
				item_id: product.productID,
				item_name: product.productName,
				item_category: product.categoryName
			};
			
			return item;
		});
		gtag("event", "view_item", {
			currency: "USD",
			value: 0,
			items: formattedData
		});
	}
}

function triggerProductEventsCart(products, totalAmount, typeName) {
	var tagName = "";
	if(typeName == "view"){
		tagName = "view_cart";
	}else if(typeName == "add"){
		tagName = "add_to_cart";
	}else if(typeName == "remove"){
		tagName = "remove_from_cart";
	}
	if (Array.isArray(products) && products.length > 0 && tagName.length) {
		var formattedData = products.map(product => {
			var item = {
				item_id: product.productID,
				item_name: product.productName,
				item_category: product.categoryName,
				quantity:product.quantity,
				price: product.rate
			};
			
			return item;
		});
        var adjustedValue = totalAmount;
        if (typeName === "remove" || typeName === "add") {
            adjustedValue = 0;
            products.forEach(product => {
                adjustedValue += product.rate*product.quantity;
            });
        } 
		gtag("event", tagName, {
			currency: "USD",
			value: adjustedValue,
			items: formattedData
		});
	}
}

function triggerProceedToCheckOutEvent(products, totalAmount) {
	if (Array.isArray(products) && products.length > 0) {
		var formattedData = products.map(product => {
			var item = {
				item_id: product.productID,
				item_name: product.productName,
				item_category: product.categoryName,
				quantity:product.quantity,
				price: product.rate
			};
			
			return item;
		});
        var adjustedValue = totalAmount;
        
		gtag("event", "begin_checkout", {
			currency: "USD",
			value: adjustedValue,
			items: formattedData
		});
	}

}
function triggerPurchaseEvent(eventPurchase) {
	if (typeof eventPurchase == 'object') {		
		gtag("event", "purchase", eventPurchase);
	}
}
function triggerAddShippingInfoEvent(eventPurchase) {
	if (typeof eventPurchase == 'object') {		
		gtag("event", "add_shipping_info", eventPurchase);
	}
}
