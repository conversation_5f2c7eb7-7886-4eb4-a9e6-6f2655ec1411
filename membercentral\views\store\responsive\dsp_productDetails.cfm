<cfset local.strData = attributes.data>
<cfset local.productList = []>
<cfif  StructKeyExists(local.strData,"qryProduct") AND local.strData.qryProduct.recordCount>
		<cfset arrayAppend(local.productList, {
			"productID": local.strData.qryProduct.productID,
			"productName": encodeForHTML(local.strData.qryProduct.contentTitle),
			"categoryName": local.strData.qryProduct.categoryName
		})>
</cfif>

<style type="text/css">
	span.prod_heading { font-weight:bold; background-color:#DEDEDE; border:1px solid #ccc; padding:2px; }
	div.prod_infoarea { margin-bottom:20px; border-left:1px solid #DEDEDE; }
	div#prodTitle { margin-top:6px; }
	div#actionswrapper div.sidebox { border:1px solid #DEDEDE; }
	div#actionswrapper div.sidebox div.sideboxtitle { padding:4px; background-color:#DEDEDE; font-weight:bold; }
	div#actionswrapper div.sidebox div.sideboxbody { padding:4px; }
	.BB { border-bottom:1px solid black; } 
	#mobileBuyBtn button { margin-right:10px; }
	.sideboxbody .span6.priceSection { max-width: 95px; }
</style>
<cfsavecontent variable="local.productDetailsJS">
	<cfoutput>
	<script language="javascript">
		<cfset local.productJSON = SerializeJSON(local.productList)>
		<cfif  StructKeyExists(local.strData,"qryProduct") AND local.strData.qryProduct.recordCount>
		$(function() {
			<cfif IsJSON(local.productJSON)>
				MCLoader.loadJS('/assets/common/javascript/mcapps/store/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {
					try {
						triggerProductViewItem(#local.productJSON#);
					} catch (error) {
						console.error("Error parsing JSON:", error.message);
					}
				});
			</cfif>
		});
		</cfif>
	</script>
	</cfoutput>
</cfsavecontent>

<cfhtmlhead text="#application.objCommon.minText(local.productDetailsJS)#">		
<div class="container-fluid">
	<cfinclude template="topbar.cfm">
	<div class="row-fluid">
		<div class="span3">
			<cfinclude template="categoryStart.cfm">
		</div>
		<div class="span9">
				<cfoutput>
					<div class="row-fluid">
						<div class="span8 well well-small">
							<span class="lead"><strong>#htmleditformat(local.strData.qryProduct.contenttitle)#</strong></span>
						</div>
						<div class="span4">
							<cfif local.strData.offerAddToCart>
								<cfform method="post" action="/?pg=store">
									<cfinput type="hidden" name="sa"  id="sa" Value="regStoreUser">
									<cfinput type="hidden" name="Quantity"  id="Quantity" value="1">
									<cfinput type="hidden" name="ItemID"  id="ItemID" Value="#local.strData.qryProduct.ItemID#">
									<cfinput type="hidden" name="cat"  id="cat" value="#local.strData.cat#">
									<button type="submit" name="btnAddToCart" class="btn btn-success span12">#local.strData.addToCartBtnLabel#</button>
								</cfform>
							<cfelse>
								<button type="button" name="btnSoldOut" class="btn span12" disabled>#local.strData.addToCartBtnLabel#</button>
							</cfif>
						</div>
					</div>
					<div class="row-fluid">
						<div class="span8">
							<cfif len(local.strData.qryProduct.rawcontent)>
								<div class="container-fluid">
									<br />
									<cfif len(local.strData.qryProduct.productDate)>
										<b>Date:</b> #DateFormat(local.strData.qryProduct.productDate,"mm/dd/yyyy")#
										<br /><br />
									</cfif>
									#local.strData.qryProduct.rawcontent#
								</div>
							</cfif>
						</div>
						<div class="span4">
							<div id="actionswrapper">
								<cfif ListFind(valueList(local.strData.cart.itemid),local.strData.qryProduct.itemId)>
									<div class="alert alert-warning"><strong>Reminder:</strong> This item is currently in your <a href="#local.strData.mainurl#&sa=view">shopping cart</a></div>
								</cfif>
						
								<cfif ListFind(valueList(local.strData.qryPurchasedProducts.productItemID),local.strData.qryProduct.itemId)>
									<br/>
									<div class="alert alert-info"><strong>Note:</strong> You have previously purchased this product.</div>
								</cfif>

									<div class="sidebox">
										<div class="sideboxtitle">Pricing</div>
										<div class="sideboxbody">

											<cfloop index="idx_Name" from="1" to="#arrayLen(local.strData.strProductPricing)#">
												<div class="row-fluid">
													<strong>#local.strData.strProductPricing[idx_Name].Name#</strong>
													<cfloop index="idx_Rate" from="1" to="#arrayLen(local.strData.strProductPricing[idx_Name].Rates)#">
														<cfif local.strData.strProductPricing[idx_Name].offerAffirmations and local.strData.strProductPricing[idx_Name].Quantity gt 0>
															<br>(includes #local.strData.strProductPricing[idx_Name].Quantity# affirmation<cfif local.strData.strProductPricing[idx_Name].Quantity gt 1>s</cfif>)
														</cfif>
														<div class="row-fluid text-left">
															<div class="span6 priceSection">
																<cfif local.strData.strProductPricing[idx_Name].Rates[idx_Rate].Rate>
																	#dollarFormat(local.strData.strProductPricing[idx_Name].Rates[idx_Rate].Rate)#
																	<cfif len(local.strData.displayedCurrencyType)> 
																		#local.strData.displayedCurrencyType#
																	</cfif>
																<cfelse>
																	FREE
																</cfif>
															</div>
															<div class="span6">
																#local.strData.strProductPricing[idx_Name].Rates[idx_Rate].rateName#
															</div>
														</div>
													</cfloop>
													<div class="row-fluid">&nbsp;</div>
												</div>
											</cfloop>
										</div>
									</div>
									<br />
									
									<cfif len(trim(local.strData.electronicDeliveryInfo))>
										<div class="sidebox">
											<div class="">
												<div class="sideboxtitle">Electronic File Delivery</div>
												<div style="padding:5px;">
													#local.strData.electronicDeliveryInfo#
												</div>	
											</div>	
										</div>
										<br />
									</cfif>
									
									<cfif local.strData.storeInfo.displayCredit is 1>
										<cfloop collection="#local.strData.strFormatCredits#" item="local.formatID">
											<cfif local.strData.strFormatCredits[local.formatID].creditInfo.creditCount gt 0>
												<div class="sidebox">
													<div class="">
														<div class="sideboxtitle"><cfoutput>Credit for #local.strData.strFormatCredits[local.formatID].formatName#</cfoutput></div>
														<div style="padding:5px;">
															<cfoutput>#local.strData.strFormatCredits[local.formatID].creditInfo.detail#</cfoutput>
														</div>	
													</div>	
												</div>
												<br />
											</cfif>
										</cfloop>
									</cfif>

							</div>
						</div>
					</div>
					<div class="row-fluid">
						<div class="span12 visible-phone">
							<cfform method="post" action="/?pg=store">
								<cfinput type="hidden" name="sa" id="sa2" Value="regStoreUser">
								<cfinput type="hidden" name="Quantity" id="Quantity2" value="1">
								<cfinput type="hidden" name="ItemID" id="ItemID2" Value="#local.strData.qryProduct.ItemID#">
								<cfinput type="hidden" name="cat" id="cat2" value="#local.strData.cat#">
								<button type="submit" name="btnAddToCart" id="btnAddToCart2" class="btn btn-success span12" <cfif not local.strData.offerAddToCart>disabled</cfif>>Add to Cart</button>
							</cfform>
						</div>					
					</div>
				</cfoutput>
		</div>
	</div>
</div>
