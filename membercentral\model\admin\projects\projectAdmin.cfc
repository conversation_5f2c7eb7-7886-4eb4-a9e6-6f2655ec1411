<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// set rights into event
		local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

		this.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);

		// set links to different functions
		this.link.listWorkspaces = buildCurrentLink(arguments.event,"listWorkspaces");
		this.link.editWorkspace = buildCurrentLink(arguments.event,"editWorkspace");
		this.link.listProjects = buildCurrentLink(arguments.event,"listProjects");
		this.link.addProject = buildCurrentLink(arguments.event,"addProject");
		this.link.editProject = buildCurrentLink(arguments.event,"editProject");
		this.link.cloneProject = buildCurrentLink(arguments.event,"cloneProject");
		this.link.saveProject = buildCurrentLink(arguments.event,"saveProject");
		this.link.editCategory = buildCurrentLink(arguments.event,"editCategory") & "&mode=direct";

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="listWorkspaces" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.workspacesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=projectJSON&meth=getWorkspaces&mode=stream";
		local.permsGotoLink = CreateObject('component', 'model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
		local.appTypeID = application.objApplications.getApplicationTypeIDFromName('workspace');
			local.addpageLink = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='PageAdmin',mca_ta='addPage');
			local.canAddInstance = CreateObject("component","model.admin.pages.appCreationProcess").canAddAppInstance(siteID=arguments.event.getValue('mc_siteInfo.siteID'), applicationTypeID=local.appTypeID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_workspaces.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editWorkspace" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objProject = createObject("component", "project");
		local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");

		local.workspaceID = arguments.event.getValue('wID',0);
		local.qryWorkspace = local.objProject.getWorkspaces(siteID=arguments.event.getValue('mc_siteinfo.siteid'), workspaceID=local.workspaceID);
		
		// security 
		local.workSpaceRights = application.objSiteResource.buildRightAssignments(siteResourceID=val(local.qryWorkspace.siteResourceID), memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'));
		if (NOT local.workSpaceRights.Edit) {
			local.data = '<h2>Not Allowed</h2><p>You do not have rights to this section.</p>';
			return returnAppStruct(local.data,"echo");
		}
		
		local.mhSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'));
		local.qryNoteCategories = CreateObject('component','model.admin.memberHistory.memberHistory').getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
								siteResourceID=local.mhSRID, checkPermission='AddEntry');

		// links
		local.editCategoryTreeName	= buildCurrentLink(arguments.event,"editCategoryTreeName") & "&csrid=#local.qryWorkspace.siteResourceID#&mode=direct";
		local.loadCategoryTree = buildCurrentLink(arguments.event,"loadCategoryTree") & "&mode=stream";
		local.loadTaskTagResourceGrid = buildCurrentLink(arguments.event,"loadTaskTagResourceGrid") & "&mode=stream";
		local.baseWorkspaceLink = getAppBaseLink(applicationInstanceID=local.qryWorkspace.applicationInstanceID);

		local.arrGridData = [];
		local.arrGridData[1] = {
			detailID=local.workspaceID,
			gridext="#this.siteResourceID#_2",
			initGridOnLoad=false,
			controllingSRID=local.qryWorkspace.siteResourceID, 
			resourceType='ProjectWorkspace', 
			areaName='ProjectWorkspace' 
		};
		local.strWorkspaceProjectFieldsGrid = createObject("component","model.admin.common.modules.customFields.customFields").getGridHTML(arrGridData=local.arrGridData);

		appendBreadCrumbs(arguments.event,{ link='', text=local.qryWorkspace.applicationInstanceName });
		</cfscript>

		<cfset local.taskTagGridData = getTaskTagGridDetails(siteID=arguments.event.getValue('mc_siteinfo.siteid'), controllingSiteResourceID=local.qryWorkspace.siteResourceID, taskFieldLabel=local.qryWorkspace.taskFieldLabel)>
		<cfset local.strTaskTagGrid = local.objResourceCustomFields.getGridHTML(arrGridData=local.taskTagGridData)>

		<cfset local.qryWorkspaceTagFields = local.objProject.getWorkspaceTagFields(workspaceID=local.workspaceID)>
		<cfset local.qryWorkspaceFeaturedFields = local.objProject.getWorkspaceFeaturedFields(workspaceID=local.workspaceID)>
		<cfset local.qryWorkspaceStatsFields = local.objProject.getWorkspaceStatsFields(workspaceID=local.workspaceID)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_workspace.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listProjects" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objProject = createObject("component", "project");
		local.qryWorkspaces = local.objProject.getWorkspaces(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
		
		local.projectsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=projectJSON&meth=getProjects&mode=stream";
		local.permsGotoLink = CreateObject('component', 'model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
		local.viewProjectTasksLink = buildCurrentLink(arguments.event,"viewProjectTasks") & "&mode=stream";
		local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_projects.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewProjectTasks" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objProject = createObject("component", "project")>
		<cfset local.projectID = arguments.event.getValue('pid',0)>
		<cfset local.qryProjectDetail = local.objProject.getProjectDetails(siteID=arguments.event.getValue('mc_siteinfo.siteid'), projectID=local.projectID)>
		<cfset local.qryWorkspaceFieldLabels = local.objProject.getWorkspaceFieldLabels(projectID=local.projectID)>
		<cfset local.projectTasksLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=projectJSON&meth=getProjectTasks&fProjectID=#local.projectID#&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_viewProjectTasks.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addProject" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objProject = createObject("component", "project");
		local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");
		local.formLink = this.link.saveProject;
		local.qryWorkspaces = local.objProject.getWorkspaces(siteID=arguments.event.getValue('mc_siteinfo.siteid'), workspaceID=arguments.event.getValue('wID',0));
		local.qryPaymentProfiles = local.objProject.getBankDraftAndCreditCardProfiles(siteID=arguments.event.getValue('mc_siteinfo.siteid'));

		// security 
		local.workSpaceRights = application.objSiteResource.buildRightAssignments(siteResourceID=val(local.qryWorkspaces.siteResourceID), memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'));		
		if (NOT local.workSpaceRights.AddProject) {
			local.data = '<h2>Not Allowed</h2><p>You do not have rights to this section.</p>';
			return returnAppStruct(local.data,"echo");
		}

		// param values
		arguments.event.setValue('projectID',0);
		arguments.event.setValue('workspaceID',val(local.qryWorkspaces.workspaceID));
		arguments.event.setValue('appInstanceName',local.qryWorkspaces.applicationInstanceName);
		arguments.event.setValue('siteResourceID',0);
		arguments.event.setValue('projectName','');
		arguments.event.setValue('hiddenFromWorkspace',1);
		arguments.event.setValue('accessStartDate',now());
		arguments.event.setValue('accessEndDate',now());
		arguments.event.setValue('accessExpirationContent','');
		arguments.event.setValue('accessExpirationContentID',0);
		arguments.event.setValue('onHoldNotificationEmails','');
		arguments.event.setValue('instructionContent','');
		arguments.event.setValue('projectContentID',0);
		arguments.event.setValue('projectDesc','');
		arguments.event.setValue('instructionContentID',0);
		arguments.event.setValue('instructionContent','');
		arguments.event.setValue('chooseProspectsInstructionContentID',0);
		arguments.event.setValue('chooseProspectsInstructionContent','');
		arguments.event.setValue('requestPayProfile',0);
		arguments.event.setValue('profileID',0);
		arguments.event.setValue('payProfileNotificationEmails','');
		arguments.event.setValue('requestPayProfileContentID',0);
		arguments.event.setValue('requestPayProfileContent','');

		local.strWorkspaceProjectFields = local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
				viewMode='bs4', resourceType='ProjectWorkspace', areaName='ProjectWorkspace', csrid=local.qryWorkspaces.siteResourceID, 
				detailID=local.qryWorkspaces.workspaceID, hideAdminOnly=0, itemType='WorkspaceProjCustom', itemID=0, trItemType='', trApplicationType='');

		appendBreadCrumbs(arguments.event,{ link='', text="New Project" });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_projects.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editProject" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objProject = createObject("component", "project");
		local.objTask = CreateObject('component', 'model.admin.tasks.task');
		local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");
		
		local.permsGotoLink = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
		local.formLink = this.link.saveProject & '&tab=setup';
		local.projectID = arguments.event.getValue('pID',0);
		local.qryProject = local.objProject.getProjectDetails(siteID=arguments.event.getValue('mc_siteinfo.siteid'), projectID=local.projectID);
		local.qryWorkspaces = local.objProject.getWorkspaces(siteID=arguments.event.getValue('mc_siteinfo.siteid'), workspaceID=local.qryProject.workspaceID);
		
		// security 
		local.workSpaceRights = application.objSiteResource.buildRightAssignments(siteResourceID=val(local.qryWorkspaces.siteResourceID), memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'));		
		if (NOT (local.workSpaceRights.editAllProjects EQ 1 OR (local.workSpaceRights.editOwnProjects EQ 1 AND local.qryProject.enteredByMemberID EQ session.cfcuser.memberdata.memberID))) {
			local.data = '<h2>Not Allowed</h2><p>You do not have rights to this section.</p>';
			return returnAppStruct(local.data,"echo");
		}
		
		local.qryPaymentProfiles = local.objProject.getBankDraftAndCreditCardProfiles(siteID=arguments.event.getValue('mc_siteinfo.siteid'));	
		local.qryTaskStatuses = local.objTask.getTaskStatuses();
		local.projectDTRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=projectJSON&mode=stream&fProjectID=#local.projectID#&meth=";

		local.solicitorsListLink = "#local.projectDTRootLink#getProjectSolicitors";
		local.classificationsListLink = "#local.projectDTRootLink#getClassifications&siteResourceID=#local.qryProject.siteResourceID#";
		local.taskListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=tasksJSON&meth=getTasks&mode=stream&fProjectID=#local.projectID#";

		local.editClassificationLink = buildCurrentLink(arguments.event,'editClassification') & '&mode=direct';
		local.taskViewLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='viewTask') & '&mode=stream';
		local.taskAddLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='addTask') & '&mode=stream';
		local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
		local.massEmailTasksLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='massEmailTasks') & '&fProjectID=#local.projectID#&mode=direct';
		local.massEditTasksLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='massEditTasks') & '&fProjectID=#local.projectID#&mode=stream';
		local.exportTasksPromptLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='exportTasksPrompt') & "&fProjectID=#local.projectID#&mode=direct";
		local.exportTasksLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='exportTasks') & "&fProjectID=#local.projectID#&mode=stream";
		local.importProjectTasksLink = buildCurrentLink(arguments.event,"importProjectTasks") & "&projectID=#local.projectID#&mode=direct";
		local.preProcessMassUpdate = buildLinkToTool(toolType='TaskAdmin',mca_ta='preProcessUpdate') & "&projectID=#local.projectID#";
		local.sampleMassUpdateTemplate = buildLinkToTool(toolType='TaskAdmin',mca_ta='sampleMassUpdateTemplate') & "&projectID=#local.projectID#&mode=stream";
		local.taskChangeHistoryLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='showTaskChangeHistory') & "&mode=stream";
		local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list') & '&mode=direct';
		local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups') & '&mode=direct';
		local.massEmailSolicitorsLink = buildCurrentLink(arguments.event,"massEmailSolicitors") & "&fProjectID=#local.projectID#&mode=direct";
		local.exportSolicitorsPromptLink = buildCurrentLink(arguments.event,"exportSolicitorsPrompt") & "&fProjectID=#local.projectID#&mode=direct";
		local.linkCCToTaskLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='linkCCToTask') & "&mode=direct";
		local.editTasksAutomationLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='editTasksAutomation') & '&fProjectID=#local.projectID#&mode=stream';
		local.massDeleteTasksLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='massDeleteTasksPrompt') & '&fProjectID=#local.projectID#&mode=direct';

		arguments.event.setValue('projectID',local.projectID);
		arguments.event.setValue('workspaceID',local.qryProject.workspaceID);
		arguments.event.setValue('appInstanceName',local.qryProject.applicationInstanceName);
		arguments.event.setValue('siteResourceID',local.qryProject.siteResourceID);
		arguments.event.setValue('projectName',local.qryProject.projectName);
		arguments.event.setValue('projectContentID',local.qryProject.projectContentID);
		arguments.event.setValue('projectDesc',local.qryProject.projectDescription);
		arguments.event.setValue('hiddenFromWorkspace',local.qryProject.hiddenFromWorkspace);
		arguments.event.setValue('accessStartDate',local.qryProject.accessStartDate);
		arguments.event.setValue('accessEndDate',local.qryProject.accessEndDate);
		arguments.event.setValue('accessExpirationContent',local.qryProject.accessExpirationContent);
		arguments.event.setValue('accessExpirationContentID',local.qryProject.accessExpirationContentID);
		arguments.event.setValue('onHoldNotificationEmails',local.qryProject.onHoldNotificationEmails);
		arguments.event.setValue('instructionContentID',local.qryProject.instructionContentID);
		arguments.event.setValue('instructionContent',local.qryProject.instructionContent);
		arguments.event.setValue('chooseProspectsInstructionContentID',local.qryProject.chooseProspectsInstructionContentID);
		arguments.event.setValue('chooseProspectsInstructionContent',local.qryProject.chooseProspectsInstructionContent);
		arguments.event.setValue('requestPayProfile',local.qryProject.requestPayProfile);
		arguments.event.setValue('profileID',local.qryProject.profileID);
		arguments.event.setValue('payProfileNotificationEmails',local.qryProject.payProfileNotificationEmails);		
		arguments.event.setValue('requestPayProfileContentID',local.qryProject.requestPayProfileContentID);
		arguments.event.setValue('requestPayProfileContent',local.qryProject.requestPayProfileContent);
		arguments.event.setValue('fieldSetID',local.qryProject.fieldSetID);
		arguments.event.setValue('showMemberPhotos',local.qryProject.showMemberPhotos);
		arguments.event.setValue('prospectContactInfoFieldSetID',local.qryProject.prospectContactFSID);
		arguments.event.setValue('reportID',local.qryProject.reportID);
		arguments.event.setValue('ruleID',local.qryProject.ruleID);
		arguments.event.setValue('mcproject_gridnum',2);

		local.strProspectAndSolicitorFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fieldSetID", selectedValue=val(arguments.event.getValue('fieldSetID')), fieldLabel="Prospect and Solicitor Field Set");
		local.strProspectContactInfoFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="prospectContactInfoFieldSetID", selectedValue=val(arguments.event.getValue('prospectContactInfoFieldSetID')), fieldLabel="Prospect Contact Info Field Set");

		local.showContactInfoUnderSameFirm = val(arguments.event.getValue('prospectContactInfoFieldSetID')) gt 0;

		local.reportInfoXML = local.objProject.getReportOtherXML(rptid=local.qryProject.reportID, csrid=local.qryProject.siteResourceID);
		local.frmgivinghistoryfrom = XMLSearch(local.reportInfoXML,"string(/report/extra/frmgivinghistoryfrom/text())");
		local.frmgivinghistoryto = XMLSearch(local.reportInfoXML,"string(/report/extra/frmgivinghistoryto/text())");
		local.frmpaymentsummaryfrom = XMLSearch(local.reportInfoXML,"string(/report/extra/frmpaymentsummaryfrom/text())");
		local.frmpaymentsummaryto = XMLSearch(local.reportInfoXML,"string(/report/extra/frmpaymentsummaryto/text())");
		local.frmlinkalloctype = XMLSearch(local.reportInfoXML,"string(/report/extra/frmlinkalloctype/text())");
		local.strEmailTemplate = local.objProject.getEmailTemplatesGridData(siteID=arguments.event.getValue('mc_siteinfo.siteid'), projectID=local.projectID);
		local.tmpProjectRights = buildRightAssignments(siteResourceID=local.qryProject.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'));

		// giving history glAccount widget
		local.strGivingHistGLAcctWidgetData = { title='Giving History: Select the revenue accounts you would like to include in this project.', titleTag='h6', description='', 
			gridext="#local.qryProject.siteResourceID#_1", gridwidth=670, gridheight=150, initGridOnLoad=false, 
			controllingSRID=local.qryProject.siteResourceID, reportID=local.qryProject.reportID, glatid=3, 
			extraNodeName='givegllist', widgetMode='report' };
		local.strGivingHistGLAcctWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strGivingHistGLAcctWidgetData);

		// payment summary glAccount widget
		local.strPaymentSummaryGLAcctWidgetData = { title='Payment Summary: Select the revenue accounts you would like to include in this project.', titleTag='h6', description='', 
			gridext="#local.qryProject.siteResourceID#_2", gridwidth=670, gridheight=150, initGridOnLoad=false, 
			controllingSRID=local.qryProject.siteResourceID, reportID=local.qryProject.reportID, glatid=3, 
			extraNodeName='paygllist', widgetMode='report' };
		local.strPaymentSummaryGLAcctWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strPaymentSummaryGLAcctWidgetData);

		// subs widget
		local.strSubsWidgetData = { title='Select the subscriptions you would like to include in this project.', titleTag='h6', 
			description='', gridext="#local.qryProject.siteResourceID#_3", gridwidth=670, gridheight=150, initGridOnLoad=true, 
			controllingSRID=local.qryProject.siteResourceID, reportID=local.qryProject.reportID, filterMode=1, excludeSteps='' };
		local.strSubscriptionWidget = createObject("component","model.admin.common.modules.subscriptionWidget.subscriptionWidget").renderWidget(strWidgetData=local.strSubsWidgetData);

		// event widget
		local.strEventWidgetData = { title='Select the events you would like to include in this project.', titleTag='h6', description='', 
			gridext="#local.qryProject.siteResourceID#_4", initGridOnLoad=false, controllingSRID=local.qryProject.siteResourceID, 
			reportID=local.qryProject.reportID, filterMode=1 };
		local.strEventWidget = createObject("component","model.admin.common.modules.eventWidget.eventWidget").renderWidget(strWidgetData=local.strEventWidgetData);

		// group set selector widgets for project classifications
		local.objGroupSetSelector = createObject("component","model.admin.common.modules.groupSetSelector.groupSetSelector");
		local.detailsGroupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
			siteID = arguments.event.getValue('mc_siteInfo.siteID'),
			orgID = arguments.event.getValue('mc_siteInfo.orgID'),
			selectorID = "projectDetailsGroupSets",
			siteResourceID = local.qryProject.siteResourceID,
			selectedGSGridHeight = 200,
			area = "details",
			editClassificationToolType = "ProjectAdmin"
		);
		local.prospectContactGroupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
			siteID = arguments.event.getValue('mc_siteInfo.siteID'),
			orgID = arguments.event.getValue('mc_siteInfo.orgID'),
			selectorID = "projectProspectContactGroupSets",
			siteResourceID = local.qryProject.siteResourceID,
			selectedGSGridHeight = 200,
			area = "prospectcontact",
			editClassificationToolType = "ProjectAdmin"
		);

		// mh widget
		local.objMHWidget = createObject("component","model.admin.common.modules.memberHistoryWidget.memberHistoryWidget");
		local.mhTypeList = 'Member History,Relationships,Notes';

		local.mhTypeIDList = '';
		if (arguments.event.getValue('mc_siteInfo.sf_memberHistory')) local.mhTypeIDList = listAppend(local.mhTypeIDList,1);
		if (arguments.event.getValue('mc_siteInfo.sf_relationships')) local.mhTypeIDList = listAppend(local.mhTypeIDList,2);
		
		local.mhTypeIDList = listAppend(local.mhTypeIDList,3);

		local.strMHWidget = structNew();
		for(local.i=1; local.i<=listLen(local.mhTypeIDList); local.i++) {
			local.typeID = listGetAt(local.mhTypeIDList,local.i);
			local.strMHWidgetData = { title='Select the #listGetAt(local.mhTypeList,local.typeID)# you''d like to include in this project.', titleTag='h6', description='', 
				gridext="#local.qryProject.siteResourceID#_5_#local.typeID#", initGridOnLoad=false, 
				controllingSRID=local.qryProject.siteResourceID, reportID=local.qryProject.reportID, typeid=local.typeID };
			local.strMHWidget[local.typeID] = local.objMHWidget.renderWidget(strWidgetData=local.strMHWidgetData);
		}

		// project custom fields
		local.arrGridData = [];
		local.arrGridData[1] = {
			detailID=local.projectID,
			gridext="#local.qryProject.siteResourceID#_6",
			initGridOnLoad=false,
			controllingSRID=local.qryProject.siteResourceID,
			resourceType='Project',
			areaName='Project'
		};
		local.strProjectFieldsGrid = local.objResourceCustomFields.getGridHTML(arrGridData=local.arrGridData);

		local.strWorkspaceProjectFields = local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
			viewMode='bs4', resourceType='ProjectWorkspace', areaName='ProjectWorkspace', csrid=local.qryProject.workspaceSiteResourceID,
			detailID=local.qryProject.workspaceID, hideAdminOnly=0, itemType='WorkspaceProjCustom', itemID=local.projectID,
			trItemType='', trApplicationType='');
		
		local.loadCategoryTree = buildCurrentLink(arguments.event,"loadCategoryTree") & "&mode=stream";
		local.loadTaskTagResourceGrid = buildCurrentLink(arguments.event,"loadTaskTagResourceGrid") & "&mode=stream";
		local.taskTagGridData = getTaskTagGridDetails(siteID=arguments.event.getValue('mc_siteinfo.siteid'), controllingSiteResourceID=local.qryProject.workspaceSiteResourceID,
				taskFieldLabel=local.qryProject.taskFieldLabel, readOnly=1);
		local.strTaskTagGrid = local.objResourceCustomFields.getGridHTML(arrGridData=local.taskTagGridData);

		local.qryProjectFields = local.objProject.getProjectFields(projectID=local.projectID);
		local.qryProjectFeaturedFields = local.objProject.getProjectFeaturedFields(projectID=local.projectID);
		local.qryProjectStatsFields = local.objProject.getProjectStatsFields(projectID=local.projectID);

		appendBreadCrumbs(arguments.event,{ link='', text=local.qryProject.projectName });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_projects.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cloneProject" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objProject = createObject("component", "project");
		local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");
		
		arguments.event.setValue('sourceProjectID',val(int(arguments.event.getValue('sourceProjectID',0))));
		local.qryProject = local.objProject.getProjectDetails(siteID=arguments.event.getValue('mc_siteinfo.siteid'), projectID=arguments.event.getValue('sourceProjectID'));
		local.qryWorkspaces = local.objProject.getWorkspaces(siteID=arguments.event.getValue('mc_siteinfo.siteid'), workspaceID=local.qryProject.workspaceID);
		local.qryPaymentProfiles = local.objProject.getBankDraftAndCreditCardProfiles(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
		local.formLink = this.link.saveProject & "&sourceProjectID=#arguments.event.getValue('sourceProjectID')#&tab=setup";

		// param values
		arguments.event.setValue('projectID',0);
		arguments.event.setValue('workspaceID',val(local.qryWorkspaces.workspaceID));
		arguments.event.setValue('appInstanceName',local.qryWorkspaces.applicationInstanceName);
		arguments.event.setValue('siteResourceID',0);
		arguments.event.setValue('projectName','Copy of #local.qryProject.projectName#');
		arguments.event.setValue('hiddenFromWorkspace',local.qryProject.hiddenFromWorkspace);
		arguments.event.setValue('accessStartDate',local.qryProject.accessStartDate);
		arguments.event.setValue('accessEndDate',local.qryProject.accessEndDate);
		arguments.event.setValue('accessExpirationContent',local.qryProject.accessExpirationContent);
		arguments.event.setValue('accessExpirationContentID',local.qryProject.accessExpirationContentID);
		arguments.event.setValue('onHoldNotificationEmails',local.qryProject.onHoldNotificationEmails);
		arguments.event.setValue('instructionContent',local.qryProject.instructionContent);
		arguments.event.setValue('projectContentID',local.qryProject.projectContentID);
		arguments.event.setValue('projectDesc',local.qryProject.projectDescription);
		arguments.event.setValue('instructionContentID',local.qryProject.instructionContentID);
		arguments.event.setValue('instructionContent',local.qryProject.instructionContent);
		arguments.event.setValue('chooseProspectsInstructionContentID',local.qryProject.chooseProspectsInstructionContentID);
		arguments.event.setValue('chooseProspectsInstructionContent',local.qryProject.chooseProspectsInstructionContent);
		arguments.event.setValue('requestPayProfile',local.qryProject.requestPayProfile);
		arguments.event.setValue('profileID',local.qryProject.profileID);
		arguments.event.setValue('payProfileNotificationEmails',local.qryProject.payProfileNotificationEmails);
		arguments.event.setValue('requestPayProfileContentID',local.qryProject.requestPayProfileContentID);
		arguments.event.setValue('requestPayProfileContent',local.qryProject.requestPayProfileContent);

		local.strWorkspaceProjectFields = local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
				viewMode='default', resourceType='ProjectWorkspace', areaName='ProjectWorkspace', csrid=local.qryWorkspaces.siteResourceID, 
				detailID=local.qryWorkspaces.workspaceID, hideAdminOnly=0, itemType='WorkspaceProjCustom', itemID=arguments.event.getValue('sourceProjectID'), 
				trItemType='', trApplicationType='');

		appendBreadCrumbs(arguments.event,{ link='', text="Clone Project" });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_projects.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveProject" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objProject = createObject("component", "project")>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.projectID = arguments.event.getValue('projectID',0)>

		<!--- Workspace-Specific Project Fields --->
		<cfquery name="local.qryWorkspace" datasource="#application.dsn.membercentral.dsn#">
			select w.workspaceID, ai.siteResourceID
			from dbo.tasks_workspaces as w 
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'workspace'
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			and w.workspaceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('workspaceID',0)#">
		</cfquery>

		<cfset local.projectWorkspaceCustomFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				resourceType='ProjectWorkspace', areaName='ProjectWorkspace', csrid=local.qryWorkspace.siteResourceID, detailID=local.qryWorkspace.workspaceID,
				hideAdminOnly=0)>
		<cfset local.projectWorkspaceCustomFieldArr = xmlParse(local.projectWorkspaceCustomFieldsXML.returnXML).xmlRoot.xmlChildren>

		<!--- put project custom fields and field types into array --->
		<cfset local.arrProjectWorkspaceFields = []>
		<cfif arrayLen(local.projectWorkspaceCustomFieldArr)>
			<cfloop array="#local.projectWorkspaceCustomFieldArr#" index="local.thisfield">
				<cfset local.tmpAtt = local.thisfield.xmlattributes>
				<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID,
										displayTypeCode=local.tmpAtt.displayTypeCode, 
										dataTypeCode=local.tmpAtt.dataTypeCode, 
										value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_','') }>
				<cfset arrayAppend(local.arrProjectWorkspaceFields,local.tmpStr)>
			</cfloop>
		</cfif>

		<cfif arguments.event.getValue('hiddenFromWorkspace',0) is 1>
			<cfset arguments.event.setValue('accessStartDate',now())>
			<cfset arguments.event.setValue('accessEndDate','1/1/2015')>
			<cfset arguments.event.setValue('accessExpirationContent','')>
		<cfelse>
			<cfset arguments.event.setValue('accessStartDate',ParseDateTime("#replace(arguments.event.getValue('accessStartDate'),' - ',' ')#"))>
			<cfset arguments.event.setValue('accessEndDate',ParseDateTime("#replace(arguments.event.getValue('accessEndDate'),' - ',' ')#"))>
		</cfif>

		<cfif arguments.event.getValue('requestPayProfile',0) is 0>
			<cfset arguments.event.setValue('profileID',0)>
			<cfset arguments.event.setValue('payProfileNotificationEmails','')>
			<cfset arguments.event.setValue('requestPayProfileContent','')>
		</cfif>

		<cfif len(arguments.event.getValue('onHoldNotificationEmails',''))>
			<cfset local.stNotify = replace(replace(arguments.event.getValue('onHoldNotificationEmails'),',',';','ALL'),' ','','ALL')>
			<cfset local.notifyEmailArr = listToArray(local.stNotify,';')>
			<cfloop from="1" to="#arrayLen(local.notifyEmailArr)#" index="local.i">
				<cfif len(local.notifyEmailArr[local.i]) and not isValid("regex",local.notifyEmailArr[local.i],application.regEx.email)>
					<cfset arrayDeleteAt(local.notifyEmailArr,local.i)>
				</cfif>
			</cfloop>
			<cfset arguments.event.setValue('onHoldNotificationEmails',arrayToList(local.notifyEmailArr,'; '))>
		</cfif>

		<cfif len(arguments.event.getValue('payProfileNotificationEmails',''))>
			<cfset local.stNotify = replace(replace(arguments.event.getValue('payProfileNotificationEmails'),',',';','ALL'),' ','','ALL')>
			<cfset local.notifyEmailArr = listToArray(local.stNotify,';')>
			<cfloop from="1" to="#arrayLen(local.notifyEmailArr)#" index="local.i">
				<cfif len(local.notifyEmailArr[local.i]) and not isValid("regex",local.notifyEmailArr[local.i],application.regEx.email)>
					<cfset arrayDeleteAt(local.notifyEmailArr,local.i)>
				</cfif>
			</cfloop>
			<cfset arguments.event.setValue('payProfileNotificationEmails',arrayToList(local.notifyEmailArr,'; '))>
		</cfif>

		<cfif local.projectID gt 0>
			<cfset local.objProject.saveProject_update(event=arguments.event, arrProjectWorkspaceFields=local.arrProjectWorkspaceFields)>
		<cfelse>
			<cfif arguments.event.getValue('sourceProjectID',0) gt 0>
				<cfset local.projectID = local.objProject.doCloneProject(event=arguments.event, arrProjectWorkspaceFields=local.arrProjectWorkspaceFields)>
			<cfelse>
				<cfset local.projectID = local.objProject.saveProject_insert(event=arguments.event, arrProjectWorkspaceFields=local.arrProjectWorkspaceFields)>
			</cfif>
		</cfif>

		<cflocation url="#this.link.editProject#&pID=#local.projectID#&tab=#arguments.event.getValue('tab','taskTab')#" addtoken="no">
	</cffunction>

	<cffunction name="editCategoryTreeName" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfset local.categoryTreeID = arguments.event.getValue('treeID',0)>
		<cfset local.qryCategoryTree = createObject("component", "project").getCategoryTree(siteID=arguments.event.getValue('mc_siteInfo.siteID'), categoryTreeID=local.categoryTreeID)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_categoryTree.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editCategory" access="public" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset arguments.event.paramValue('usageMode','TaskResultOption')>

		<cfset local.data = createObject("component","model.admin.categories.categories").getEditCategoryForm(event=arguments.event,
			saveCategoryLink=buildCurrentLink(arguments.event,"saveCategory") & "&mode=stream")>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCategory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryCategoryTree = createObject("component", "project").getCategoryTree(siteID=arguments.event.getValue('mc_siteInfo.siteID'), categoryTreeID=arguments.event.getValue('categoryTreeID',0))>
		
		<cfset local.strSaveResult =  createObject("component","model.admin.categories.categories").doSaveCategory(event=arguments.event,
			siteResourceID=local.qryCategoryTree.controllingSiteResourceID, categoryTreeName=local.qryCategoryTree.categoryTreeName)>

		<cfif local.strSaveResult.success>
			<cfreturn returnAppStruct(local.strSaveResult.html,"echo")>
		<cfelse>
			<cflocation url="#this.link.editCategory#&err=1&categoryID=#arguments.event.getValue('categoryID',0)#" addtoken="false">
		</cfif>
	</cffunction>

	<cffunction name="getTaskTagGridDetails" access="private" output="false" returntype="array">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="controllingSiteResourceID" type="numeric" required="true">
		<cfargument name="taskFieldLabel" type="string" required="true">
		<cfargument name="readOnly" type="boolean" required="false" default="0">

		<cfset var local = structNew()>

		<cfquery name="local.qryTaskTags" datasource="#application.dsn.membercentral.dsn#">
			select ct.categoryTreeID, ct.categoryTreeName, ct.siteResourceID, c.categoryID, c.categoryName,
				case when c.categoryID is null then 1 else 0 end as canDeleteCategoryTree,
				ROW_NUMBER() OVER (ORDER BY ct.sortOrder, c.sortOrder) as row
			from dbo.cms_categoryTrees as ct
			inner join dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			left outer join dbo.cms_categories as c on c.categoryTreeID = ct.categoryTreeID and c.isActive = 1
			where ct.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and ct.controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.controllingSiteResourceID#">
			order by row
		</cfquery>

		<cfset local.arrGridData = arrayNew(1)>
		<cfoutput query="local.qryTaskTags" group="categoryTreeID">
			<cfquery name="local.qryMoveCatTreeUp" dbtype="query" maxrows="1">
				select categoryTreeID
				from [local].qryTaskTags
				where row < #local.qryTaskTags.row#
				and categoryTreeID <> #local.qryTaskTags.categoryTreeID#
			</cfquery>
			<cfquery name="local.qryMoveCatTreeDown" dbtype="query" maxrows="1">
				select categoryTreeID
				from [local].qryTaskTags
				where row > #local.qryTaskTags.row#
				and categoryTreeID <> #local.qryTaskTags.categoryTreeID#
			</cfquery>

			<cfsavecontent variable="local.thisCatTreeSelection">
				<div id="MCCatTreeParent#local.qryTaskTags.categoryTreeID#" class="mccf_div_gridContainer MCCatTreeGrid#local.qryTaskTags.categoryTreeID# MCCatTree">
					<div class="stepCatTreeTitle">
						<h6 id="MCCatTreeTitle_#local.qryTaskTags.categoryTreeID#" class="border-bottom border-dark">
							<div class="catTreeDesignator">#arguments.taskFieldLabel# Result Option</div>
							<span class="MCCatTreeName#local.qryTaskTags.categoryTreeID#">#local.qryTaskTags.categoryTreeName#</span>
						</h6>
						<cfif NOT arguments.readOnly>
							<div class="taskTagButtonBar clearfix">
								<div>
									<button type="button" class="btn btn-sm btn-link" onclick="editTaskTag(#local.qryTaskTags.categoryTreeID#,0);">
										<i class="fa-regular fa-circle-plus"></i> Add #arguments.taskFieldLabel# Result Option
									</button>
								</div>
								<div>
									<button type="button" class="btn btn-sm btn-link" onclick="editCategoryTree(#local.qryTaskTags.categoryTreeID#);">
										<i class="fa-regular fa-pencil"></i> Edit
									</button>
								</div>
								<div>
									<button type="button" class="btn btn-sm btn-link<cfif not local.qryTaskTags.canDeleteCategoryTree> noActionEvent</cfif>" id="deleteCatTree#local.qryTaskTags.categoryTreeID#" onclick="deleteCategoryTree(#local.qryTaskTags.categoryTreeID#);"><i class="fa-regular fa-trash-alt text-danger"></i> Delete</button>
								</div>
								<div>
									<button type="button" class="btn btn-sm btn-link<cfif not (local.qryMoveCatTreeUp.recordcount is 1)> noActionEvent</cfif>" id="moveCatTreeUp#local.qryTaskTags.categoryTreeID#" onclick="moveCatTree(#local.qryTaskTags.categoryTreeID#,'up');"><i class="fa-regular fa-arrow-up"></i> Move Up</button>
								</div>
								<div>
									<button type="button" class="btn btn-sm btn-link<cfif not (local.qryMoveCatTreeDown.recordcount is 1)> noActionEvent</cfif>" id="moveCatTreeDown#local.qryTaskTags.categoryTreeID#" onclick="moveCatTree(#local.qryTaskTags.categoryTreeID#,'down');"><i class="fa-regular fa-arrow-down"></i> Move Down</button>
								</div>
							</div>
						</cfif>
					</div>
					<div style="clear:both;"></div>
				</div>
			</cfsavecontent>

			<cfset local.tmpStr = { 
				nonGridHTML=local.thisCatTreeSelection,
				detailID=0,
				gridext="",
				initGridOnLoad=false,
				controllingSRID=0, 
				resourceType='', 
				areaName='',
				gridClassList="",
				gridID="" 
			}>
			<cfset arrayAppend(local.arrGridData,local.tmpStr)>

			<cfoutput>
				<cfif val(local.qryTaskTags.categoryID)>
					<cfquery name="local.qryMoveCategoryUp" dbtype="query" maxrows="1">
						select categoryTreeID
						from [local].qryTaskTags
						where row < #local.qryTaskTags.row#
						and categoryTreeID = #local.qryTaskTags.categoryTreeID#
					</cfquery>
					<cfquery name="local.qryMoveCategoryDown" dbtype="query" maxrows="1">
						select categoryTreeID
						from [local].qryTaskTags
						where row > #local.qryTaskTags.row#
						and categoryTreeID = #local.qryTaskTags.categoryTreeID#
					</cfquery>

					<cfsavecontent variable="local.thisTaskTagSection">
						<div class="stepTaskTagTitle">
							<h6 id="MCTaskTagTitle_#local.qryTaskTags.categoryID#" class="border-bottom border-dark">
								<div class="catTreeDesignator">Selectable #arguments.taskFieldLabel# Result Option of <span class="MCCatTreeName#local.qryTaskTags.categoryTreeID#">#local.qryTaskTags.categoryTreeName#</span></div>
								<span class="MCTaskTagName#local.qryTaskTags.categoryID#">#local.qryTaskTags.categoryName#</span>
							</h6>
							<cfif NOT arguments.readOnly>
								<div class="taskTagButtonBar clearfix">
									<div>
										<button type="button" class="btn btn-sm btn-link" onclick="editTaskTag(#local.qryTaskTags.categoryTreeID#,#local.qryTaskTags.categoryID#);"><i class="fa-regular fa-pencil"></i> Edit</button>
									</div>
									<div>
										<button type="button" class="btn btn-sm btn-link" id="removeTaskTag#local.qryTaskTags.categoryID#" onclick="removeTaskTag(#local.qryTaskTags.categoryTreeID#,#local.qryTaskTags.categoryID#);"><i class="fa-regular fa-trash-alt text-danger"></i> Delete</button>
									</div>
									<div>
										<button type="button" class="btn btn-sm btn-link<cfif not (local.qryMoveCategoryUp.recordcount is 1)> noActionEvent</cfif>" id="moveUpTaskTag#local.qryTaskTags.categoryID#" onclick="moveTaskTag(#local.qryTaskTags.categoryTreeID#,#local.qryTaskTags.categoryID#,'up');"><i class="fa-regular fa-arrow-up"></i> Move Up</button>
									</div>
									<div>
										<button type="button" class="btn btn-sm btn-link<cfif not (local.qryMoveCategoryDown.recordcount is 1)> noActionEvent</cfif>" id="moveDownTaskTag#local.qryTaskTags.categoryID#" onclick="moveTaskTag(#local.qryTaskTags.categoryTreeID#,#local.qryTaskTags.categoryID#,'down');"><i class="fa-regular fa-arrow-down"></i> Move Down</button>
									</div>
								</div>
							</cfif>
						</div>
					</cfsavecontent>

					<cfset local.tmpStr = { 
						intro=local.thisTaskTagSection,
						detailID=local.qryTaskTags.categoryID,
						gridext="#this.siteResourceID#_tag_#local.qryTaskTags.categoryID#",
						initGridOnLoad=false,
						controllingSRID=local.qryTaskTags.siteResourceID, 
						resourceType='ProjectWorkspace', 
						areaName='TaskTag',
						gridClassList="MCCatTreeGrid#local.qryTaskTags.categoryTreeID#,MCTaskTagGrid,mb-3",
						gridID="MCTaskTag#local.qryTaskTags.categoryID#",
						readOnly=arguments.readOnly 
					}>
					<cfset arrayAppend(local.arrGridData,local.tmpStr)>
				</cfif>
			</cfoutput>
		</cfoutput>

		<cfreturn local.arrGridData>
	</cffunction>

	<cffunction name="loadCategoryTree" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objProject = createObject("component", "project")>
		
		<cfset local.qryCategoryTree = local.objProject.getCategoryTree(siteID=arguments.event.getValue('mc_siteInfo.siteID'), categoryTreeID=arguments.event.getValue('treeID',0))>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<div id="MCCatTreeParent#local.qryCategoryTree.categoryTreeID#" class="mccf_div_gridContainer MCCatTreeGrid#local.qryCategoryTree.categoryTreeID# MCCatTree">
					<div class="stepCatTreeTitle">
						<h6 id="MCCatTreeTitle_#local.qryCategoryTree.categoryTreeID#" class="border-bottom border-dark">
							<div class="catTreeDesignator">Selectable #arguments.event.getValue('taskFieldLabel','Task')# Result Option</div>
							<span class="MCCatTreeName#local.qryCategoryTree.categoryTreeID#">#local.qryCategoryTree.categoryTreeName#</span>
						</h6>
						<div class="taskTagButtonBar clearfix">
							<div>
								<button type="button" class="btn btn-sm btn-link" onclick="editTaskTag(#local.qryCategoryTree.categoryTreeID#,0);"><i class="fa-regular fa-circle-plus"></i> Add Result Option</button>
							</div>
							<div>
								<button type="button" class="btn btn-sm btn-link" onclick="editCategoryTree(#local.qryCategoryTree.categoryTreeID#);"><i class="fa-regular fa-pencil"></i> Edit</button>
							</div>
							<div>
								<button type="button" class="btn btn-sm btn-link" id="deleteCatTree#local.qryCategoryTree.categoryTreeID#" onclick="deleteCategoryTree(#local.qryCategoryTree.categoryTreeID#);"><i class="fa-regular fa-trash-alt text-danger"></i> Delete</button>
							</div>
							<div>
								<button type="button" class="btn btn-sm btn-link" id="moveCatTreeUp#local.qryCategoryTree.categoryTreeID#" onclick="moveCatTreeUp(#local.qryCategoryTree.categoryTreeID#);"><i class="fa-regular fa-arrow-up"></i> Move Up</button>
							</div>
							<div>
								<button type="button" class="btn btn-sm btn-link" id="moveCatTreeDown#local.qryCategoryTree.categoryTreeID#" onclick="moveCatTreeDown(#local.qryCategoryTree.categoryTreeID#);"><i class="fa-regular fa-arrow-down"></i> Move Down</button>
							</div>
						</div>
					</div>
					<div style="clear:both;"></div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="loadTaskTagResourceGrid" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.baseURL = "/?mode=stream&pg=admin&mca_ajaxlib=resourceCustomFields&mca_ajaxfunc=">
		<cfset local.returnStruct = { html="", js="" }>
		
		<cfquery name="local.qryTaskTag" datasource="#application.dsn.membercentral.dsn#">
			select ct.categoryTreeID, ct.categoryTreeName, ct.siteResourceID, c.categoryID, c.categoryName
			from dbo.cms_categoryTrees as ct
			inner join dbo.cms_siteResources sr on sr.siteResourceID = ct.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID  
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.cms_categories as c on c.categoryTreeID = ct.categoryTreeID and c.isActive = 1
			where ct.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			and c.categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('categoryID',0)#">
		</cfquery>

		<cfsavecontent variable="local.thisTaskTagSection">
			<cfoutput>
				<div class="stepTaskTagTitle">
					<h6 id="MCTaskTagTitle_#local.qryTaskTag.categoryID#" class="border-bottom border-dark">
						<div class="catTreeDesignator">Result Option of <span class="MCCatTreeName#local.qryTaskTag.categoryTreeID#">#local.qryTaskTag.categoryTreeName#</span></div>
						<span class="MCTaskTagName#local.qryTaskTag.categoryID#">#local.qryTaskTag.categoryName#</span>
					</h6>
					<div class="taskTagButtonBar clearfix">
						<div>
							<button type="button" class="btn btn-sm btn-link" onclick="editTaskTag(#local.qryTaskTag.categoryTreeID#,#local.qryTaskTag.categoryID#);">
								<i class="fa-regular fa-pencil"></i> Edit
							</button>
						</div>
						<div>
							<button type="button" class="btn btn-sm btn-link" id="removeTaskTag#local.qryTaskTag.categoryID#" onclick="removeTaskTag(#local.qryTaskTag.categoryTreeID#,#local.qryTaskTag.categoryID#);"><i class="fa-regular fa-trash-alt text-danger"></i> Delete</button>
						</div>
						<div>
							<button type="button" class="btn btn-sm btn-link" id="moveUpTaskTag#local.qryTaskTag.categoryID#" onclick="moveTaskTag(#local.qryTaskTag.categoryTreeID#,#local.qryTaskTag.categoryID#,'up');"><i class="fa-regular fa-arrow-up"></i> Move Up</button>
						</div>
						<div>
							<button type="button" class="btn btn-sm btn-link" id="moveDownTaskTag#local.qryTaskTag.categoryID#" onclick="moveTaskTag(#local.qryTaskTag.categoryTreeID#,#local.qryTaskTag.categoryID#,'down');"><i class="fa-regular fa-arrow-down"></i> Move Down</button>
						</div>
					</div>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfset local.strGridData = { title="", 
			intro=local.thisTaskTagSection,
			detailID=local.qryTaskTag.categoryID,
			gridext="#this.siteResourceID#_tag_#local.qryTaskTag.categoryID#",
			gridwidth=660,
			initGridOnLoad=true,
			controllingSRID=local.qryTaskTag.siteResourceID, 
			resourceType='ProjectWorkspace', 
			areaName='TaskTag',
			gridClassList="MCCatTreeGrid#local.qryTaskTag.categoryTreeID#,MCTaskTagGrid,mb-3",
			gridID="MCTaskTag#local.qryTaskTag.categoryID#" }>

		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.gridhtml = local.objCustomFields.getGridBoxHTML(strGridData=local.strGridData)>
		<cfset local.gridjs = local.objCustomFields.getGridJS(strGridData=local.strGridData, baseURL=local.baseURL)>

		<cfsavecontent variable="local.customJS">
			<cfoutput>
			<script language="javascript">
				#local.gridjs#
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.customJS#">

		<cfreturn returnAppStruct(local.gridhtml,"echo")>
	</cffunction>

	<cffunction name="importProjectTasks" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.projectID = arguments.event.getValue('projectID',0)>

		<cfset local.qryProject = createObject("component", "project").getProjectDetails(siteID=arguments.event.getValue('mc_siteinfo.siteid'), projectID=local.projectID)>
		<cfset local.processImportTasksLink = buildLinkToTool(toolType='TaskAdmin',mca_ta='preProcessImport') & "&projectID=#local.projectID#&mode=direct">
		<cfset local.sampleImportTemplate = buildLinkToTool(toolType='TaskAdmin',mca_ta='sampleImportTemplate') & "&projectID=#local.projectID#&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_importProjectTasks.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editClassification" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.classificationID = arguments.event.getValue('classificationID',0)>
		<cfset local.selectorID = arguments.event.getValue('selectorID','')>
		<cfset local.siteResourceID = arguments.event.getValue('siteResourceID',0)>
		<cfset local.qryGetClassification = createObject("component","project").getClassificationByID(classificationID=local.classificationID)>
		<cfset local.qryGroupSets = createObject("component","model.admin.MemberGroupSets.MemberGroupSets").getGroupSets(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
		<cfset local.formLink = buildCurrentLink(arguments.event,"saveClassification") & "&mode=stream">
		<cfset local.gsid = arguments.event.getValue('groupSetID',0)>
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_classification.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveClassification" access="public"  returntype="struct">
		<cfargument name="Event" type="any" />

		<cfset var local = structNew()>
		<cfset local.objProject = createObject("component","project")>
		<cfset local.classificationID = arguments.event.getValue('classificationID',0)>
		
		<cfif local.classificationID is 0>
			<cfset local.classificationID = local.objProject.insertClassification(siteResourceID=arguments.event.getValue('siteResourceID',0), 
				nameOverride=arguments.event.getTrimValue('nameOverride',''), allowSearch=0, showInSearchResults=arguments.event.getValue('showInSearchResults',''), 
				groupSetID=arguments.event.getValue('groupSetID',0), area=arguments.event.getTrimValue('area',''))>
		<cfelse>
			<cfset local.objProject.updateClassification(classificationID=local.classificationID, nameOverride=arguments.event.getTrimValue('nameOverride',''), 
				allowSearch=0, showInSearchResults=arguments.event.getValue('showInSearchResults',''), groupSetID=arguments.event.getValue('groupSetID',0))>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<cfif NOT arguments.event.getValue('classificationID',0)>
					<cfif arguments.event.getValue('selectorID','') EQ 'projectDetailsGroupSets'>
						top.reloadDetailsClassificationsTable();
					<cfelse>
						top.reloadProspectContactClassificationsTable();
					</cfif>
				</cfif>
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSolicitorsPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.exportLink = buildCurrentLink(arguments.event,"exportSolicitors") & "&fProjectID=#arguments.event.getValue('fProjectID',0)#&mode=stream">

		<cfset local.strFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fsID", allowBlankOption=false, inlinePreviewSectionID="divExportFormContainer")>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_project_solicitors_export.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSolicitors" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objProject = CreateObject("component","project")>
		<cfset local.data = "The export file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.qryWorkspaceFieldLabels = local.objProject.getWorkspaceFieldLabels(projectID=arguments.event.getValue('fProjectID',0))>

		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "#local.qryWorkspaceFieldLabels.solicitorFieldLabelPlural#.csv">
		<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
		
		<cfset local.qrySolicitors = local.objProject.getSolicitorsFromFilters(event=arguments.event, mode='solicitorDownload')>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExportSolicitors">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				IF OBJECT_ID('tempdb..##tmpSolicitors') IS NOT NULL
					DROP TABLE ##tmpSolicitors;
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..####tmpSolicitorsExport#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpSolicitorsExport#local.tmpSuffix#;
				CREATE TABLE ##tmpSolicitors (memberID int PRIMARY KEY);
				CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);

				DECLARE @orgID int, @fieldSetID int, @outputFieldsXML xml, @selectsql varchar(max), @fullsql varchar(max);
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				SET @fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fsid',0)#">;

				-- solicitors matching filters
				INSERT INTO ##tmpSolicitors
				select listitem
				from dbo.fn_intListToTable('0#ValueList(local.qrySolicitors.memberID)#',',');

				-- get fieldset data
				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmpSolicitors', @membersResultTableName='##tmpMembers',
					@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

				EXEC tempdb..sp_rename '##tmpMembers.memberID', 'MemberCentralID', 'COLUMN';

				SET @fullsql = 'SELECT mActive.firstname + '' '' + mActive.lastName as [Solicitor], mActive.memberNumber as [Solicitor MemberNumber], ISNULL(mActive.company,'''') as [Solicitor Firm Name], s.* 
					INTO ####tmpSolicitorsExport#local.tmpSuffix#
					FROM ##tmpMembers as s
					inner join dbo.ams_members as m ON m.memberID = s.MemberCentralID
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID';

				-- put into temp table for export
				EXEC(@fullsql);

				SET @selectsql = '
					SELECT *, ROW_NUMBER() OVER(order by [Solicitor]) as mcCSVorder
					*FROM* ####tmpSolicitorsExport#local.tmpSuffix#';

				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

				IF OBJECT_ID('tempdb..##tmpSolicitors') IS NOT NULL
					DROP TABLE ##tmpSolicitors;
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..####tmpSolicitorsExport#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpSolicitorsExport#local.tmpSuffix#;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doDownloadSolicitors('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEmailSolicitors" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strFilters = structNew()>
		<cfset structInsert(local.strFilters, 'fProjectID', arguments.event.getValue('fProjectID',0))>
		<cfset structInsert(local.strFilters, 'fProjSolicitorMemberID', arguments.event.getValue('fProjSolicitorMemberID',0))>
		<cfset structInsert(local.strFilters, 'fProjSolicitorGroupID', arguments.event.getValue('fProjSolicitorGroupID',0))>
		<cfset structInsert(local.strFilters, 'fMinTasksCount', arguments.event.getValue('fMinTasksCount',''))>
		<cfset structInsert(local.strFilters, 'fMaxTasksCount', arguments.event.getValue('fMaxTasksCount',''))>

		<cfset local.qryWorkspaceFieldLabels = CreateObject("component","project").getWorkspaceFieldLabels(projectID=arguments.event.getValue('fProjectID',0))>

		<cfset local.strResourceTitle = { resourceTitle=local.qryWorkspaceFieldLabels.solicitorFieldLabelPlural, resourceTitleDesc='', templateEditorLabel='Compose your message.' }>
		
		<cfset local.mergeCodeInstructionsLink = "#buildCurrentLink(arguments.event,'showMergeCodeInstructions')#&incProject=1&mode=stream">
		<cfif arguments.event.getValue('fProjectID',0) gt 0>
			<cfset local.mergeCodeInstructionsLink = "#local.mergeCodeInstructionsLink#&pid=#arguments.event.getValue('fProjectID')#">
		</cfif>

		<cfset local.argumentCollection = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), resourceType='Project',
					recipientType='Solicitors', strResourceTitle=local.strResourceTitle, strFilters=local.strFilters, arrRecipientModes=arrayNew(1),
					mergeCodeInstructionsLink=local.mergeCodeInstructionsLink,
					emailTemplateTreeCode="ETTASKS" }>

		<cfset local.data = CreateObject("component","model.admin.common.modules.massEmails.massEmails").prepMassEmails(argumentCollection=local.argumentCollection)>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>