<cfcomponent output="no">
	
	<cffunction name="getContributionAppInstances" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		
		<cfset var qryContributionAppInstances = "">

		<cfquery name="qryContributionAppInstances" datasource="#application.dsn.membercentral.dsn#">
			select ai.applicationInstanceID, ai.applicationInstanceName
			from dbo.cms_applicationInstances as ai
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID  
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and at.applicationTypeName = 'Contributions'
			order by ai.applicationInstanceName
		</cfquery>
		
		<cfreturn qryContributionAppInstances>
	</cffunction>

	<cffunction name="getApplicationInstance" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="appInstanceID" type="numeric" required="yes">
		
		<cfset var qryAppInstance = "">

		<cfquery name="qryAppInstance" datasource="#application.dsn.membercentral.dsn#">
			select applicationInstanceID, applicationInstanceName
			from dbo.cms_applicationInstances
			where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and applicationInstanceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.appInstanceID#">
		</cfquery>
		
		<cfreturn qryAppInstance>
	</cffunction>

	<cffunction name="getProgramDetails" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var qryProgram = "">
		
		<cfquery name="qryProgram" datasource="#application.dsn.membercentral.dsn#">
			select cp.programID, ai.applicationInstanceName, cp.applicationInstanceID, cp.siteResourceID, 
				cp.allowAnonymous, cp.distribSettingID, cp.invoiceGenerateDays, 
				cp.nextPaymentDate, cp.nextPaymentDateAFID, cp.nextAdvancementDate, cp.nextAdvancementDateAFID, 
				cp.payThruDate, cp.payThruDateAFID, cp.advanceAFDate, cp.advanceAFDateAFID, cp.uid, 
				srs.siteResourceStatusDesc, cp.programName, cp.fieldsSectionTitle, 
				cp.intakeConfirmContentID, programConfirmContent.rawContent as programConfirmContent,
				cp.anonymousContentID, anonymousContent.rawContent as anonymousContent, cp.noteCategoryID, 
				cp.intakeEmailFrom, cp.intakeEmailStaff, cp.defaultGLAccountID, cp.isProgramEnabled
			from dbo.cp_programs as cp
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = cp.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
			cross apply dbo.fn_getContent(cp.intakeConfirmContentID,1) as programConfirmContent
			cross apply dbo.fn_getContent(cp.anonymousContentID,1) as anonymousContent
			where cp.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>
		
		<cfreturn qryProgram>
	</cffunction>

	<cffunction name="getProgramBySiteID" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryProgram = "">
		
		<cfquery name="qryProgram" datasource="#application.dsn.membercentral.dsn#">
			select cp.programID, cp.programName
			from dbo.cp_programs as cp
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			order by cp.programName
		</cfquery>
		
		<cfreturn qryProgram>
	</cffunction>

	<cffunction name="insertProgramDetails" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="appInstanceID" type="numeric" required="yes">
		<cfargument name="programName" type="string" required="yes">
		<cfargument name="intakeConfirmContent" type="string" required="yes">
		<cfargument name="intakeEmailFrom" type="string" required="yes">
		<cfargument name="intakeEmailStaff" type="string" required="yes">
		<cfargument name="allowAnonymous" type="boolean" required="yes">
		<cfargument name="anonymousContent" type="string" required="yes">
		<cfargument name="noteCategoryID" type="numeric" required="yes">
		<cfargument name="enteredByMemberID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfstoredproc procedure="cp_createProgram" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.appInstanceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.intakeConfirmContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.intakeEmailFrom#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.intakeEmailStaff#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowAnonymous#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.anonymousContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.noteCategoryID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enteredByMemberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.programID">
		</cfstoredproc>

		<cfreturn local.programID>
	</cffunction>

	<cffunction name="updateProgramInfo" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programName" type="string" required="yes">
		<cfargument name="uid" type="string" required="yes">
		<cfargument name="intakeConfirmContent" type="string" required="yes">
		<cfargument name="intakeEmailFrom" type="string" required="yes">
		<cfargument name="intakeEmailStaff" type="string" required="yes">
		<cfargument name="allowAnonymous" type="boolean" required="yes">
		<cfargument name="anonymousContent" type="string" required="yes">
		<cfargument name="status" type="string" required="yes">
		<cfargument name="noteCategoryID" type="numeric" required="yes">
		<cfargument name="enteredByMemberID" type="numeric" required="yes">

		<cfstoredproc procedure="cp_updateProgram" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
			<cfif len(arguments.uid) and application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.uid#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.intakeConfirmContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.intakeEmailFrom#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.intakeEmailStaff#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowAnonymous#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.anonymousContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.noteCategoryID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enteredByMemberID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="getDistribSettings" access="public" output="false" returntype="query">
		<cfset var qryDistribSettings = "">

		<cfquery name="qryDistribSettings" datasource="#application.dsn.membercentral.dsn#">
			select settingID, settingDesc
			from dbo.cp_distribSettings
		</cfquery>

		<cfreturn qryDistribSettings>
	</cffunction>

	<cffunction name="getProgramRate" access="public" output="false" returntype="query">
		<cfargument name="rateID" type="numeric" required="yes">
		
		<cfset var qryProgramRate = "">

		<cfquery name="qryProgramRate" datasource="#application.dsn.membercentral.dsn#">
			select rateName, uid
			from dbo.cp_rates
			where rateID = <cfqueryparam value="#arguments.rateID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn qryProgramRate>
	</cffunction>

	<cffunction name="insertProgramRate" access="public" output="false" returntype="void">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="rateName" type="string" required="yes">

		<cfset var qryInsertProgramRate = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryInsertProgramRate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				declare @rateID int, @programID int;
				set @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;

				BEGIN TRAN;
					INSERT INTO dbo.cp_rates (programID, rateName, uid, rateOrder)
					VALUES (@programID, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateName#">, NEWID(), 255);
					SET @rateID = SCOPE_IDENTITY();

					EXEC dbo.cp_reorderProgramRates @programID=@programID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="updateProgramRate" access="public" output="false" returntype="void">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="rateName" type="string" required="yes">
		<cfargument name="uid" type="string" required="yes">

		<cfset var qryUpdateProgramRate = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdateProgramRate">
			UPDATE dbo.cp_rates
			SET rateName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateName#">
				<cfif len(arguments.uid) and application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					, uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.uid#">
				</cfif>
			WHERE rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">
			and programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;
		</cfquery>
	</cffunction>

	<cffunction name="deleteProgramRate" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="rateID" type="numeric" required="true">
		<cfargument name="programID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteRate">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @rateID int, @programID int;
					set @rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">;
					set @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;

					if not exists (
						select 1
						from dbo.cp_rates as r
						inner join dbo.cp_programs as cp on cp.programID = r.programID
						inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
							and ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						where r.programID = @programID
						and r.rateID = @rateID
					)
						raiserror('Invalid Rate',16,1);

					if exists (select 1 from dbo.cp_campaignMatrix where rateID = @rateID)
						raiserror('Rate Tied to Campaign', 16, 1);

					if exists (select 1 from dbo.cp_contributions where programID = @programID and rateID = @rateID)
						raiserror('Rate Tied to Contribution', 16, 1);

					BEGIN TRAN;
						delete from dbo.cp_rates 
						where rateID = @rateID
						and programID = @programID;

						EXEC dbo.cp_reorderProgramRates @programID=@programID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfif FindNoCase("Rate Tied to Campaign",cfcatch.detail)>
				<cfset local.data.msg = "This rate cannot be removed from the program while it is used by one or more campaigns.">
			<cfelseif FindNoCase("Rate Tied to Contribution",cfcatch.detail)>
				<cfset local.data.msg = "This rate cannot be removed from the program while it is tied to one or more contributions.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="programID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset var qryDeleteProgram = "">

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDeleteProgram">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @programID int, @siteID int, @siteResourceID int, @contribCount int, @status varchar(8);
					set @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;

					select @siteResourceID = cp.siteResourceID, @status = srs.siteResourceStatusDesc
					from dbo.cp_programs as cp
					inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
						and ai.siteID = @siteID
					inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = cp.siteResourceID
					inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					where cp.programID = @programID;

					select @contribCount = count(contributionID)
					from dbo.cp_contributions
					where programID = @programID;

					if @siteResourceID is not null and @status <> 'Deleted' and (@status <> 'Inactive' or @contribCount = 0) begin
						if @contribCount > 0
							EXEC dbo.cms_updateSiteResourceStatus @siteResourceStatusDesc='Inactive', @siteResourceID=@siteResourceID;
						else
							EXEC dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;
					end

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getCPFrequency" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var qryCPFrequency = "">

		<cfquery name="qryCPFrequency" datasource="#application.dsn.membercentral.dsn#">
			 select cf.frequencyID, cf.frequency
			 from dbo.cp_frequencies as cf
			 left outer join dbo.cp_programFrequencies as cpf on cpf.frequencyID = cf.frequencyID 
			 	and cpf.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			 where cf.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			 and cpf.frequencyID is null;
		</cfquery>
		
		<cfreturn qryCPFrequency>
	</cffunction>

	<cffunction name="saveProgramPmtFrequency" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="frequencyID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="cp_insertProgramFrequency" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.frequencyID#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.progFreqID">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteProgramPmtFrequency" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="progFreqID" type="numeric" required="true">
		<cfargument name="programID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteProgramPmtFrequency">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @progFreqID int, @programID int;
					set @progFreqID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.progFreqID#">;
					set @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;

					if not exists (
						select 1
						from dbo.cp_programFrequencies as f
						inner join dbo.cp_programs as cp on cp.programID = f.programID
						inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
							and ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						where f.programID = @programID
						and f.progFreqID = @progFreqID
					)
						raiserror('Invalid Frequency',16,1);
					
					if exists (select top 1 autoID from dbo.cp_campaignMatrix where progFreqID = @progFreqID)
						raiserror('Frequency Tied to Campaign', 16, 1);

					BEGIN TRAN;
						delete from dbo.cp_programFrequencies 
						where progFreqID = @progFreqID
						and programID = @programID;

						EXEC dbo.cp_reorderProgramFrequency @programID=@programID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfif FindNoCase("Frequency Tied to Campaign",cfcatch.detail)>
				<cfset local.data.msg = "This program frequency cannot be removed from the program while it is used by one or more campaigns.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getProgramDistribution" access="public" output="false" returntype="query">
		<cfargument name="distribID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		
		<cfset var qryProgramDistribution = "">

		<cfquery name="qryProgramDistribution" datasource="#application.dsn.membercentral.dsn#">
			select distDesc, GLAccountID, distPct, distCode, uid
			from dbo.cp_distributions
			where distribID = <cfqueryparam value="#arguments.distribID#" cfsqltype="CF_SQL_INTEGER">
			and programID = <cfqueryparam value="#arguments.programID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn qryProgramDistribution>
	</cffunction>

	<cffunction name="insertProgramDistribution" access="public" output="false" returntype="numeric">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="distDesc" type="string" required="yes">
		<cfargument name="glAccountID" type="numeric" required="yes">
		<cfargument name="distPct" type="numeric" required="yes">
		<cfargument name="distCode" type="string" required="yes">

		<cfset var qryInsertProgramDistribution = "">	
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryInsertProgramDistribution">
			INSERT INTO dbo.cp_distributions (distDesc, programID, glAccountID, distPct, distCode, uid)
			VALUES ( <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.distDesc#">,
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">,
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.glAccountID#">,
				<cfif arguments.distPct gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.distPct#"><cfelse>null</cfif>,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.distCode#">,
				NEWID() );
			
			SELECT SCOPE_IDENTITY() as distribID;
		</cfquery>

		<cfreturn qryInsertProgramDistribution.distribID>
	</cffunction>

	<cffunction name="updateProgramDistribution" access="public" output="false" returntype="void">
		<cfargument name="distribID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="distDesc" type="string" required="yes">
		<cfargument name="glAccountID" type="numeric" required="yes">
		<cfargument name="distPct" type="numeric" required="yes">
		<cfargument name="distCode" type="string" required="yes">
		<cfargument name="uid" type="string" required="yes">

		<cfset var qryUpdateProgramDistribution = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdateProgramDistribution">
			UPDATE dbo.cp_distributions
			SET distDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.distDesc#">, 
				glAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.glAccountID#">,
				distPct = <cfif arguments.distPct gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.distPct#"><cfelse>null</cfif>,
				distCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.distCode#">
				<cfif len(arguments.uid) and application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					, uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.uid#">
				</cfif>
			WHERE distribID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.distribID#">
			and programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;
		</cfquery>
	</cffunction>

	<cffunction name="deleteProgramDistribution" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="distribID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteDistribution">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @distribID int, @programID int;
					set @distribID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.distribID#">;
					set @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;

					if not exists (
						select 1
						from dbo.cp_distributions as d
						inner join dbo.cp_programs as cp on cp.programID = d.programID
						inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
							and ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						where d.programID = @programID
						and d.distribID = @distribID
					)
						raiserror('Invalid Distribution',16,1);

					if exists (select top 1 campaignID from dbo.cp_campaignDistributions where distribid = @distribID)
						raiserror('Distribution Tied to Campaign', 16, 1);

					if exists (select top 1 contribDistributionID from dbo.cp_contributionDistributions where distribid = @distribID)
						raiserror('Distribution Tied to Contribution', 16, 1);

					delete from dbo.cp_distributions 
					where distribID = @distribID
					and programID = @programID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset local.data.success = false>
			<cfif FindNoCase("Distribution Tied to Campaign",cfcatch.detail)>
				<cfset local.data.msg = "This program distribution cannot be removed from the program while it is used by one or more campaigns.">
			<cfelseif FindNoCase("Distribution Tied to Contribution",cfcatch.detail)>
				<cfset local.data.msg = "This program distribution cannot be removed from the program while it is tied to one or more contributions.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveDistribSetting" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="distribSettingID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateSettings">
			UPDATE cp
			SET cp.distribSettingID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.distribSettingID#">
			FROM dbo.cp_programs as cp
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
				AND ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			WHERE cp.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFrequency" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="frequencyID" type="numeric" required="yes">
		
		<cfset var qryFrequency = "">

		<cfquery name="qryFrequency" datasource="#application.dsn.membercentral.dsn#">
			select frequency, isRecurring, numMonths, uid
			from dbo.cp_frequencies
			where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.frequencyID#">
		</cfquery>
		
		<cfreturn qryFrequency>
	</cffunction>

	<cffunction name="insertFrequency" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="frequency" type="string" required="yes">
		<cfargument name="isRecurring" type="boolean" required="yes">
		<cfargument name="numMonths" type="numeric" required="yes">
		
		<cfset var qryInsertFrequency = "">

		<cfquery name="qryInsertFrequency" datasource="#application.dsn.membercentral.dsn#">
			insert into dbo.cp_frequencies (siteID, frequency, isRecurring, numMonths, uid)
			values (
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frequency#">,
				<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isRecurring#">,
				nullIf(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.numMonths#">,0),
				NEWID()
			)
		</cfquery>
	</cffunction>

	<cffunction name="updateFrequency" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="frequencyID" type="numeric" required="yes">
		<cfargument name="frequency" type="string" required="yes">
		<cfargument name="isRecurring" type="boolean" required="yes">
		<cfargument name="numMonths" type="numeric" required="yes">
		<cfargument name="uid" type="string" required="yes">
		
		<cfset var qryUpdateFrequency = "">

		<cfquery name="qryUpdateFrequency" datasource="#application.dsn.membercentral.dsn#">
			update dbo.cp_frequencies
			set frequency = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.frequency#">,
				isRecurring = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isRecurring#">,
				numMonths = nullIf(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.numMonths#">,0)
				<cfif len(arguments.uid) and application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					, uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.uid#">
				</cfif>
			where frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.frequencyID#">
			and siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
		</cfquery>
	</cffunction>

	<cffunction name="doRemoveFrequency" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="frequencyID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryDeleteFrequency" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @frequencyID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.frequencyID#">;

			if not exists(select 1 from dbo.cp_programFrequencies where frequencyID = @frequencyID)
				delete
				from dbo.cp_frequencies
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				and frequencyID = @frequencyID;
		</cfquery>
		
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAdvanceFormula" access="public" returnType="query">
		<cfargument name="siteID" type="numeric" required="true">	

		<cfset var qryAllAFs = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryAllAFs">
			SELECT AFID, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			ORDER BY afName
		</cfquery>

		<cfreturn qryAllAFs>
	</cffunction>

	<cffunction name="saveProgramPaymentSettings" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="nextPmtDate" type="string" required="yes">
		<cfargument name="nextPmtDateAFID" type="numeric" required="yes">
		<cfargument name="nextAdvDate" type="string" required="yes">
		<cfargument name="nextAdvDateAFID" type="numeric" required="yes">
		<cfargument name="payThruDate" type="string" required="yes">
		<cfargument name="payThruDateAFID" type="numeric" required="yes">
		<cfargument name="advAFDate" type="string" required="yes">
		<cfargument name="advAFDateAFID" type="numeric" required="yes">
		<cfargument name="invoiceGenDays" type="string" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateSettings">
				SET NOCOUNT ON;
				
				EXEC dbo.cp_saveProgramPaymentSettings
					@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
					@programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">,
					@nextPaymentDate = <cfif len(arguments.nextPmtDate)><cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.nextPmtDate#"><cfelse>NULL</cfif>,
					@nextPaymentDateAFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.nextPmtDateAFID#">,
					@nextAdvancementDate = <cfif len(arguments.nextAdvDate)><cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.nextAdvDate#"><cfelse>NULL</cfif>,
					@nextAdvancementDateAFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.nextAdvDateAFID#">,
					@payThruDate = <cfif len(arguments.payThruDate) and DateDiff('d', arguments.payThruDate, DateAdd('yyyy',100,now())) gte 0>
										<cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.payThruDate#">
									<cfelse>
										NULL
									</cfif>,
					@payThruDateAFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.payThruDateAFID#">,
					@advanceAFDate = <cfif len(arguments.advAFDate)><cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.advAFDate#"><cfelse>NULL</cfif>,
					@advanceAFDateAFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.advAFDateAFID#">,
					@invoiceGenerateDays = <cfif val(arguments.invoiceGenDays)><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.invoiceGenDays)#"><cfelse>NULL</cfif>;
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getProgramsForAddBySite" access="public" output="false" returntype="query" hint="returns list of active, form-enabled programs">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryPrograms = "">

		<cfquery name="qryPrograms" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			select cp.programID, cp.programName
			from dbo.cp_programs as cp
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID 
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.cms_siteResources as sr2 on sr2.siteResourceID = cp.siteResourceID
				and sr2.siteResourceStatusID = 1
			where ai.siteID = @siteID
			and cp.isProgramEnabled = 1
			order by cp.programName;
		</cfquery>
		
		<cfreturn qryPrograms>
	</cffunction>

	<cffunction name="getProgramsForFilterByMember" access="public" output="false" returntype="query" hint="returns list of programs this member has contributed to, regardless of current program status">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var qryPrograms = "">

		<cfquery name="qryPrograms" datasource="#application.dsn.membercentral.dsn#">
			select distinct p.programID, p.programName
			from dbo.sites as s
			inner join dbo.cms_applicationInstances as ai on ai.siteID = s.siteID
			inner join dbo.cp_programs as p on p.applicationInstanceID = ai.applicationInstanceID
			inner join dbo.cp_contributions as c on c.programID = p.programID
			inner join dbo.ams_members as m on m.memberID = c.memberID
			where s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			order by p.programName
		</cfquery>
		
		<cfreturn qryPrograms>
	</cffunction>

	<cffunction name="getProgramsForFilterBySite" access="public" output="false" returntype="query" hint="returns list of programs contributed to, regardless of current program status">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryPrograms = "">

		<cfquery name="qryPrograms" datasource="#application.dsn.membercentral.dsn#">
			select distinct p.programID, p.programName
			from dbo.sites as s
			inner join dbo.cms_applicationInstances as ai on ai.siteID = s.siteID
			inner join dbo.cp_programs as p on p.applicationInstanceID = ai.applicationInstanceID
			inner join dbo.cp_contributions as c on c.programID = p.programID
			where s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			order by p.programName
		</cfquery>
		
		<cfreturn qryPrograms>
	</cffunction>

	<cffunction name="getProgramsForMemberToContrib" access="public" output="no" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.contributeRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="ContributionProgram", functionName="contribute")>

		<cfquery name="local.qryPrograms" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @siteID int, @memberID int, @groupPrintID int, @functionID int;

			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			set @functionID =  <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.contributeRFID#">;
			select @groupPrintID = groupPrintID from dbo.ams_members where memberID = @memberID;

			select cp.programID, cp.programName
			from dbo.cp_programs as cp
			inner join dbo.cms_siteResources sr on sr.siteID = @siteID
				and sr.siteResourceID = cp.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
				and srfrp.siteResourceID = sr.siteResourceID
				and srfrp.functionID = @functionID
			inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
				and gprp.rightPrintID = srfrp.rightPrintID
				and gprp.groupPrintID = @groupPrintID
			where cp.isProgramEnabled = 1
			order by cp.programName;
		</cfquery>

		<cfreturn local.qryPrograms>
	</cffunction>

	<cffunction name="getFrequencyOptions" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryCPFrequency = "">

		<cfquery name="qryCPFrequency" datasource="#application.dsn.membercentral.dsn#">
			 select cf.frequencyID, cf.frequency
			 from dbo.cp_frequencies as cf
			 where cf.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>
		
		<cfreturn qryCPFrequency>
	</cffunction>

	<cffunction name="getCPStatuses" access="public" output="false" returntype="query">
		<cfset var qryCPStatuses = "">

		<cfquery name="qryCPStatuses" datasource="#application.dsn.membercentral.dsn#">
			 select statusID, statusCode, statusName, statusDesc
			 from dbo.cp_statuses
		</cfquery>
		
		<cfreturn qryCPStatuses>
	</cffunction>

	<cffunction name="updateCustomFieldDetails" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="fieldsSectionTitle" type="string" required="yes">
		<cfargument name="defaultGLAccountID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateDurationSettings">
			UPDATE cp
			SET fieldsSectionTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fieldsSectionTitle#">,
				defaultGLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.defaultGLAccountID#">
			FROM dbo.cp_programs as cp
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
				AND ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			WHERE cp.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="cancelContribution" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="contributionIDList" type="string" required="yes">
		<cfargument name="AROption" type="string" required="yes">
		<cfargument name="cancellationDate" type="string" required="yes">
		
		<cfset local.processImmediately = 1>
		
		<cfif ListLen(arguments.contributionIDList) GT 1>
			<cfset local.processImmediately = 0>
		</cfif>
		<cfloop list="#arguments.contributionIDList#" index="local.thisContributionID">
			<cfstoredproc procedure="cp_cancelContribution" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.thisContributionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" value="#arguments.AROption#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.cancellationDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.processImmediately#">
			</cfstoredproc>
		</cfloop>
	</cffunction>

	<cffunction name="deleteContribution" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="contributionID" type="numeric" required="yes">

		<cfset var local = structNew()>	

		<cftry>
			<cfstoredproc procedure="cp_deleteContribution" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.contributionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getContributionDetails" access="public" output="false" returntype="query">
		<cfargument name="contributionID" type="numeric" required="true">

		<cfset var qryDetails = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDetails">
			select c.contributionID, p.programName, c.isPerpetual, c.installmentAmt, c.startdate, c.endDate, 
				c.installmentCount, f.frequency, c.memberID, mActive.memberID as activeMemberID, 
				mActive.lastName + ', ' + mActive.firstname + ' (' + mActive.memberNumber + ')' as fullMemberName, mActive.company,
				(select min(dueDate) from dbo.cp_contributionSchedule where contributionID = c.contributionID) as firstPaymentDate
			from dbo.cp_contributions as c
			inner join dbo.cp_programs as p on p.programID = c.programID
			inner join dbo.cp_frequencies as f on f.frequencyID = c.frequencyID
			inner join dbo.ams_members as m on m.memberID = c.memberID
			inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			where c.contributionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.contributionID#">
		</cfquery>

		<cfreturn qryDetails>
	</cffunction>

	<cffunction name="getContributionInvDueAmount" access="public" output="false" returntype="numeric">
		<cfargument name="contributionID" type="numeric" required="true">

		<cfset var qryInvoiceDues = "">

		<cfquery name="qryInvoiceDues" datasource="#application.dsn.membercentral.dsn#">
			select isnull(sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount),0) as invDue
			from dbo.fn_cp_contributionTransactions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.contributionID#">) as ct
			inner join dbo.tr_invoiceTransactions as it on it.orgID = ct.ownedByOrgID and it.transactionID = ct.transactionID
			inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
			inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
			where invs.status in ('Open','Closed','Delinquent');
		</cfquery>

		<cfreturn val(qryInvoiceDues.invDue)>
	</cffunction>

	<cffunction name="getAllocatedInstallAmt" access="public" output="false" returntype="numeric">
		<cfargument name="contributionID" type="numeric" required="true">

		<cfset var qryContribFutureInstallments = "">

		<cfquery name="qryContribFutureInstallments" datasource="#application.dsn.membercentral.dsn#">
			select isnull(sum(t.amount),0) as allocatedFutureInstallmentAmt
			from dbo.tr_transactionInstallments as ti
			inner join dbo.cp_contributionSchedule as cs on cs.scheduleID = ti.scheduleID
			inner join dbo.tr_transactions as t on t.transactionID = ti.transactionID
			where cs.contributionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.contributionID#">
			and ti.isActive = 1
			and ti.isConverted = 0
			and ti.isPaidOnCreate = 0
			and t.statusID = 1;
		</cfquery>

		<cfreturn val(qryContribFutureInstallments.allocatedFutureInstallmentAmt)>
	</cffunction>

	<cffunction name="getTotalMoneyFieldInstallments" access="public" output="false" returntype="numeric">
		<cfargument name="contributionID" type="numeric" required="true">
		<cfargument name="installmentCount" type="numeric" required="true">

		<cfset var qryTotalMoneyFieldInstallments = "">

		<cfquery name="qryTotalMoneyFieldInstallments" datasource="#application.dsn.membercentral.dsn#">
			IF OBJECT_ID('tempdb..##tmpContribMoneyFields') is not null
				DROP TABLE ##tmpContribMoneyFields;
			CREATE TABLE ##tmpContribMoneyFields (amount decimal(18,2), withInstallment bit);

			declare @contributionID int, @installmentCount int;

			set @contributionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.contributionID#">;
			set @installmentCount = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.installmentCount#">;

			-- get possible money custom fields
			INSERT INTO ##tmpContribMoneyFields (amount, withInstallment)
			select isnull(fv.valueDecimal2,0), case when fu.areaName = 'EachInstallment' then 1 else 0 end
			from dbo.cp_contributions as c 
			inner join dbo.cp_programs as cp on cp.programID = c.programID
				and c.contributionID = @contributionID 
			inner join dbo.cf_fields as f on f.controllingSiteResourceID = cp.siteResourceID
				and f.detailID = cp.programID
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
				and ft.displayTypeCode = 'TEXTBOX'
				and ft.supportAmt = 1
				and ft.supportQty = 0
			inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			inner join dbo.cf_fieldData as fd on fd.fieldID = f.fieldID
				and fd.valueID = fv.valueID
			where fd.itemID = c.contributionID
			and fd.itemType = 'ContributionProgram'
			and isnull(fv.valueDecimal2,0) > 0
			and fu.areaName in ('FirstInstallment','EachInstallment')
				union all 
			select isnull(fv.valueInteger,0) * fd.amount, case when fu.areaName = 'EachInstallment' then 1 else 0 end
			from dbo.cp_contributions as c 
			inner join dbo.cp_programs as cp on cp.programID = c.programID
				and c.contributionID = @contributionID 
			inner join dbo.cf_fields as f on f.controllingSiteResourceID = cp.siteResourceID
				and f.detailID = cp.programID
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
				and ft.displayTypeCode = 'TEXTBOX'
				and ft.supportAmt = 1
				and ft.supportQty = 1
			inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			inner join dbo.cf_fieldData as fd on fd.fieldID = f.fieldID
				and fd.valueID = fv.valueID
			where fd.itemID = c.contributionID
			and fd.itemType = 'ContributionProgram'
			and isnull(fv.valueInteger,0) > 0
			and fd.amount > 0
			and fu.areaName in ('FirstInstallment','EachInstallment')
				union all 
			select fd.amount, case when fu.areaName = 'EachInstallment' then 1 else 0 end
			from dbo.cp_contributions as c 
			inner join dbo.cp_programs as cp on cp.programID = c.programID
				and c.contributionID = @contributionID 
			inner join dbo.cf_fields as f on f.controllingSiteResourceID = cp.siteResourceID
				and f.detailID = cp.programID
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
				and ft.displayTypeCode IN ('SELECT','RADIO','CHECKBOX')
				and ft.supportAmt = 1
			inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			inner join dbo.cf_fieldData as fd on fd.fieldID = f.fieldID
				and fd.valueID = fv.valueID
			where fd.itemID = c.contributionID
			and fd.itemType = 'ContributionProgram'
			and fd.amount > 0
			and fu.areaName in ('FirstInstallment','EachInstallment');

			select sum(case when withInstallment = 1 then amount * @installmentCount else amount end) as totalMoneyFieldAmt
			from ##tmpContribMoneyFields;

			IF OBJECT_ID('tempdb..##tmpContribMoneyFields') is not null
				DROP TABLE ##tmpContribMoneyFields;
		</cfquery>

		<cfreturn val(qryTotalMoneyFieldInstallments.totalMoneyFieldAmt)>
	</cffunction>

	<cffunction name="getContributorDues" access="public" output="false" returntype="query">
		<cfargument name="contributionID" type="numeric" required="true">

		<cfset var qryContributorDues = "">

		<cfquery name="qryContributorDues" datasource="#application.dsn.membercentral.dsn#">
			select sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) as amountDue, 
				it.invoiceID
			from dbo.fn_cp_contributionTransactions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.contributionID#">) as ct
			inner join dbo.tr_invoiceTransactions as it on it.orgID = ct.ownedByOrgID and it.transactionID = ct.transactionID
			inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
			inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
			where invs.status in ('Open','Closed','Delinquent')
			group by it.invoiceID;
		</cfquery>

		<cfreturn qryContributorDues>
	</cffunction>

	<cffunction name="getTotalFeesAndPaidByContribution" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="contributionID" type="numeric" required="true">

		<cfset var qryTotalFeesAndPaid = "">

		<cfquery name="qryTotalFeesAndPaid" datasource="#application.dsn.membercentral.dsn#">
			select isnull(totalSaleAmount,0) as totalSaleAmount, isnull(totalPaid,0) as totalPaid
			from dbo.fn_cp_totalFeeAndPaid(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.contributionID#">)
		</cfquery>

		<cfreturn qryTotalFeesAndPaid>
	</cffunction>

	<cffunction name="loadProgramStatusInfo" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<cftry>
			<cfset local.data.programinfo = "">
			<cfset local.qryContributionProgram = getProgramDetails(siteID=arguments.siteID, programID=arguments.programID)>
			<cfset local.strEnableFormMeasures = getEnableFormMeasures(qryContributionProgram=local.qryContributionProgram)>
			<cfset local.data.isProgramEnabled = local.strEnableFormMeasures.isProgramEnabled>

			<cfif arrayLen(local.strEnableFormMeasures.arrProgramIssues)>
				<cfsavecontent variable="local.data.programinfo">
					<cfoutput>
						<div id="issueAccordion" class="alert alert-warning" onclick="toggleProgramIssuesPanel();">
							<cfif arrayLen(local.strEnableFormMeasures.arrProgramIssues)>
								Program is disabled. <span id="showIssue" class="text-primary cursor-pointer">Click for more info</span><span id="hideIssue" class="text-primary cursor-pointer d-none">Click to hide</span>.
							</cfif>
							<div id="issuePanel" class="d-none">
								<ul class="pl-3 mt-2">
								<cfloop array="#local.strEnableFormMeasures.arrProgramIssues#" index="local.thisIssue">	
									<li class="py-1">#local.thisIssue#</li>
								</cfloop>
								</ul>
							</div>
						</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEnableFormMeasures" access="public" output="false" returntype="struct">
		<cfargument name="qryContributionProgram" type="query" required="yes">

		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		<cfset local.strReturn.arrProgramIssues = arrayNew(1)>
		<cfset local.strReturn.isProgramEnabled = arguments.qryContributionProgram.isProgramEnabled>

		<cfset local.programID = val(arguments.qryContributionProgram.programID)>
		<cfset local.isProgramWithRate = isProgramWithRate(programID=local.programID)>
		<cfset local.isProgramWithDistribution = isProgramWithDistribution(programID=local.programID)>

		<!--- program checks --->
		<cfif not len(arguments.qryContributionProgram.programName)>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Program name must be defined.")>
		</cfif>
		<cfif val(arguments.qryContributionProgram.noteCategoryID) is 0>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Program must have a Note Category Selected.")>
		</cfif>
		<cfif not local.isProgramWithRate and not isProgramWithMonetaryField(programID=local.programID)>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Program must have at least one rate or one monetary custom field defined.")>
		</cfif>
		<cfif not isProgramWithPmtFrequency(programID=local.programID)>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"There must be at least one frequency option.")>
		</cfif>
		<cfif local.isProgramWithRate and not local.isProgramWithDistribution>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"There must be at least one distribution when rates are defined.")>
		</cfif>
		<cfif local.isProgramWithDistribution and programDistributionTotalPct(programID=local.programID) neq 100>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Distribution percentages must total to 100%.")>
		</cfif>
		<cfif arguments.qryContributionProgram.invoiceGenerateDays lte 0>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Generate Invoice Days before next scheduled payment must be defined.")>
		</cfif>
		<cfif not len(arguments.qryContributionProgram.nextPaymentDate)>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Next Payment Date must be defined.")>
		<cfelseif val(arguments.qryContributionProgram.nextPaymentDateAFID) is 0>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Advancement Formula must be choosen to advance 'Next Payment Date' forward.")>
		</cfif>
		<cfif not len(arguments.qryContributionProgram.nextAdvancementDate)>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Next Advancement Date must be defined.")>
		<cfelseif val(arguments.qryContributionProgram.nextAdvancementDateAFID) is 0>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Advancement Formula must be choosen to advance 'Next Advancement Date' forward.")>
		</cfif>
		<cfif not len(arguments.qryContributionProgram.payThruDate) or DateDiff('d',now(),arguments.qryContributionProgram.payThruDate) lt 0>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Write out payments through must be a date in the future.")>
		<cfelseif val(arguments.qryContributionProgram.payThruDateAFID) is 0>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Advancement Formula must be choosen to advance 'write out payments date' forward.")>
		</cfif>
		<cfif not len(arguments.qryContributionProgram.advanceAFDate) or DateDiff('d',now(),arguments.qryContributionProgram.advanceAFDate) lt 0>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"When should we advance this date must be a date in the future.")>
		<cfelseif val(arguments.qryContributionProgram.advanceAFDateAFID) is 0>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Advancement Formula must be choosen to advance 'when should we advance this date' forward.")>
		</cfif>
		<cfif not len(arguments.qryContributionProgram.fieldsSectionTitle)>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Custom Fields Section Title must be defined.")>
		</cfif>
		<cfif not len(arguments.qryContributionProgram.intakeEmailFrom)>
			<cfset arrayAppend(local.strReturn.arrProgramIssues,"Reply-to must be defined for confirmation emails.")>
		</cfif>

		<cfif not arrayLen(local.strReturn.arrProgramIssues) and arguments.qryContributionProgram.isProgramEnabled is 0>
			<cfset enableProgram(programID=local.programID)>
			<cfset local.strReturn.isProgramEnabled = 1>
		<cfelseif arrayLen(local.strReturn.arrProgramIssues) gt 0 and arguments.qryContributionProgram.isProgramEnabled is 1>
			<cfset disableProgram(programID=local.programID)>
			<cfset local.strReturn.isProgramEnabled = 0>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="isProgramWithRate" access="public" output="false" returntype="boolean">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var qryCheckProgram = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCheckProgram">
			select top 1 rateID
			from dbo.cp_rates 
			where programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
		</cfquery>

		<cfreturn qryCheckProgram.recordCount is 1>
	</cffunction>

	<cffunction name="isProgramWithPmtFrequency" access="public" output="false" returntype="boolean">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var qryCheckProgram = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCheckProgram">
			select top 1 progFreqID
			from dbo.cp_programFrequencies
			where programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
		</cfquery>

		<cfreturn qryCheckProgram.recordCount is 1>
	</cffunction>

	<cffunction name="isProgramWithMonetaryField" access="public" output="false" returntype="boolean">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var qryCheckProgram = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCheckProgram">
			select top 1 f.fieldID
			from dbo.cf_fields as f
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cp_programs as p on p.programID = f.detailID and f.controllingSiteResourceID = p.siteResourceID
			left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
			where p.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and f.isActive = 1
			and case 
				when ft.displayTypeCode = 'TEXTBOX' and ( (ft.supportQty = 1 and f.amount > 0) or ft.supportAmt = 1 )  then 1
				when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') and fv.amount > 0 then 1
				else 0 end = 1;
		</cfquery>

		<cfreturn qryCheckProgram.recordCount is 1>
	</cffunction>

	<cffunction name="isProgramWithDistribution" access="public" output="false" returntype="boolean">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var qryCheckProgram = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCheckProgram">
			select top 1 distribID
			from dbo.cp_distributions
			where programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
		</cfquery>

		<cfreturn qryCheckProgram.recordCount is 1>
	</cffunction>

	<cffunction name="programDistributionTotalPct" access="public" output="false" returntype="numeric">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var qryProgramDistrib = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryProgramDistrib">
			select sum(isnull(distPct,0)) as totalPct 
			from dbo.cp_distributions 
			where programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
		</cfquery>

		<cfreturn val(qryProgramDistrib.totalPct)>
	</cffunction>

	<cffunction name="getAllSitePrograms" access="public" output="false" returntype="query" hint="returns resultset of all site programs">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryPrograms = "">

		<cfquery name="qryPrograms" datasource="#application.dsn.membercentral.dsn#">
			select cp.programID, ai.applicationInstanceName, cp.programName, srs.siteResourceStatusDesc
			from dbo.cp_programs as cp
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = cp.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and srs.siteResourceStatusDesc <> 'Deleted'		
			order by ai.applicationInstanceName, cp.programName
		</cfquery>

		<cfreturn qryPrograms>
	</cffunction>

	<cffunction name="getProgramDistributions" access="public" output="false" returntype="query">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var qryDistributions = "">

		<cfquery name="qryDistributions" datasource="#application.dsn.membercentral.dsn#">
			select distribID, distDesc
			from dbo.cp_distributions
			where programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			order by distDesc
		</cfquery>

		<cfreturn qryDistributions>
	</cffunction>

	<cffunction name="getMonetaryProgramCustomFields" access="public" output="false" returntype="query">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var qryFields = "">

		<cfquery name="qryFields" datasource="#application.dsn.membercentral.dsn#">
			select distinct f.fieldID, f.fieldText, f.fieldOrder
			from dbo.cf_fields as f
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cp_programs as p on p.programID = f.detailID and f.controllingSiteResourceID = p.siteResourceID
			left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
			where p.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and f.isActive = 1
			and case 
				when ft.displayTypeCode = 'TEXTBOX' and ( (ft.supportQty = 1 and f.amount > 0) or ft.supportAmt = 1 )  then 1
				when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') and fv.amount > 0 then 1
				else 0 end = 1
			order by f.fieldOrder
		</cfquery>

		<cfreturn qryFields>
	</cffunction>

	<cffunction name="getNonMonetaryProgramCustomFields" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var qryFields = "">
		<cfset var ContributionAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='ContributionAdmin',siteID=arguments.siteID)>

		<cfquery name="qryFields" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @contributionAdminSRID int, @usageID int;
			set @contributionAdminSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#ContributionAdminSRID#">;
			select @usageID = dbo.fn_cf_getUsageID('ContributionAdmin','Role',NULL);

			select f.fieldID, f.fieldText, 1 as section, f.fieldOrder
			from dbo.cf_fields as f
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			where f.controllingSiteResourceID = @contributionAdminSRID
			and f.usageID = @usageID
			and f.isActive = 1
			and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
			and ft.supportAmt = 0
			and exists (select 1 from dbo.cf_fieldValues where fieldID = f.fieldID)
				union all
			select f.fieldID, f.fieldText, 2 as section, f.fieldOrder
			from dbo.cf_fields as f
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cp_programs as p on p.programID = f.detailID and f.controllingSiteResourceID = p.siteResourceID
			where p.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and f.isActive = 1
			and ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX')
			and ft.supportAmt = 0
			and exists (select 1 from dbo.cf_fieldValues where fieldID = f.fieldID)
			order by section, fieldOrder;
		</cfquery>

		<cfreturn qryFields>
	</cffunction>

	<cffunction name="getProgramRates" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var qryRates = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryRates">
			select r.rateID, r.rateName
			from dbo.cp_rates as r
			inner join dbo.cp_programs as cp on cp.programID = r.programID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
				and ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			where r.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			order by r.rateName
		</cfquery>

		<cfreturn qryRates>
	</cffunction>

	<cffunction name="getProgramRatesForFilters" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct['arrrates'] = arrayNew(1)>

		<cfset local.qryRates = getProgramRates(siteID=arguments.mcproxy_siteID,programID=arguments.programID)>

		<cfloop query="local.qryRates">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['rateid'] = local.qryRates.rateID>
			<cfset local.tmp['ratename'] = local.qryRates.rateName>
			<cfset arrayAppend(local.returnStruct['arrrates'], local.tmp)>
		</cfloop>

		<cfset local.returnStruct["success"] = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getMaxInstallmentDate" access="public" output="false" returntype="string">
		<cfargument name="contributionID" type="numeric" required="true">

		<cfset var qryMaxInstallmentDate = "">

		<cfquery name="qryMaxInstallmentDate" datasource="#application.dsn.membercentral.dsn#">
			select max(cs.dueDate) as maxInstallmentDate
			from dbo.cp_contributionSchedule as cs 
			inner join dbo.tr_transactionInstallments as ti on cs.scheduleID = ti.scheduleID
			where cs.contributionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.contributionID#">
		</cfquery>

		<cfreturn dateFormat(qryMaxInstallmentDate.maxInstallmentDate,'m/d/yyyy')>
	</cffunction>

	<cffunction name="cancelInstallmentSchedule" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="AROption" type="string" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfstoredproc procedure="cp_cancelInstallmentSchedule" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_CHAR" value="#arguments.AROption#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="enableProgram" access="public" output="false" returntype="void">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var qryEnableProgram = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryEnableProgram">
			UPDATE dbo.cp_programs
			SET isProgramEnabled = 1
			WHERE programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and isProgramEnabled = 0
		</cfquery>
	</cffunction>

	<cffunction name="disableProgram" access="public" output="false" returntype="void">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var qryDisableProgram = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDisableProgram">
			UPDATE dbo.cp_programs
			SET isProgramEnabled = 0
			WHERE programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and isProgramEnabled = 1
		</cfquery>
	</cffunction>

	<cffunction name="getCampaignDetails" access="public" output="false" returntype="struct">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="campaignID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCampaign">
			select cpc.campaignID, cpc.programID, cp.applicationInstanceID, cpc.campaignName, cpc.status, cpc.isHidden, cpc.startDate, cpc.endDate, cpc.goalAmount,
				cpc.allowSetStartDate, cpc.skipMemberLookup, cpc.newAcctFieldsetID, cpc.newAcctFormTitle,
				cpc.showProfLicensesForNewAcct, cp.payThruDate, cpc.intakeFormTitle, cpc.confirmationEmailSubjectLine, 
				cpc.intakeFormInstContentID, campaignFormInstContent.rawContent as campaignFormInstContent,
				cpc.intakeFormNonQualContentID, campaignFormNonQualContent.rawContent as campaignFormNonQualContent
			from dbo.cp_campaigns as cpc 
			inner join dbo.cp_programs as cp on cp.programID = cpc.programID
			cross apply dbo.fn_getContent(cpc.intakeFormInstContentID,1) as campaignFormInstContent
			cross apply dbo.fn_getContent(cpc.intakeFormNonQualContentID,1) as campaignFormNonQualContent
			where cpc.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and cpc.campaignID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.campaignID#">
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCampaignDistribs">
			select distribid
			from dbo.cp_campaignDistributions
			where campaignID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.campaignID#">
		</cfquery>

		<cfreturn local>
	</cffunction>

	<cffunction name="getAllProgramRates" access="public" output="false" returntype="query">
		<cfargument name="programID" type="numeric" required="yes">
		
		<cfset var qryProgramRates = "">

		<cfquery name="qryProgramRates" datasource="#application.dsn.membercentral.dsn#">
			select rateID, rateName
			from dbo.cp_rates
			where programID = <cfqueryparam value="#arguments.programID#" cfsqltype="CF_SQL_INTEGER">
			order by rateOrder
		</cfquery>
		
		<cfreturn qryProgramRates>
	</cffunction>

	<cffunction name="getAllProgramDistributions" access="public" output="false" returntype="query">
		<cfargument name="programID" type="numeric" required="yes">
		
		<cfset var qryProgramDistributions = "">

		<cfquery name="qryProgramDistributions" datasource="#application.dsn.membercentral.dsn#">
			select distribid, distDesc
			from dbo.cp_distributions
			where programID = <cfqueryparam value="#arguments.programID#" cfsqltype="CF_SQL_INTEGER">
			order by distDesc
		</cfquery>
		
		<cfreturn qryProgramDistributions>
	</cffunction>

	<cffunction name="getAllProgramFrequencies" access="public" output="false" returntype="query">
		<cfargument name="programID" type="numeric" required="yes">

		<cfset var qryFrequencies = "">

		<cfquery name="qryFrequencies" datasource="#application.dsn.membercentral.dsn#">
			 select cpf.progFreqID, cf.frequency, cf.isRecurring
			 from dbo.cp_frequencies as cf
			 inner join dbo.cp_programFrequencies as cpf on cpf.frequencyID = cf.frequencyID 
			 where cpf.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			 order by cpf.freqOrder
		</cfquery>
		
		<cfreturn qryFrequencies>
	</cffunction>

	<cffunction name="doChangeProgramInstance" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="newAppInstanceID" type="numeric" required="yes">
		
		<cfset var qryChangeAppInstance = "">

		<cfquery name="qryChangeAppInstance" datasource="#application.dsn.membercentral.dsn#">
			EXEC dbo.cp_changeProgramInstance 
				@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">,
				@newAppInstanceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.newAppInstanceID#">;
		</cfquery>
	</cffunction>

	<cffunction name="saveCampaign" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="campaignID" type="numeric" required="yes">
		<cfargument name="campaignName" type="string" required="yes">
		<cfargument name="status" type="string" required="yes">
		<cfargument name="isHidden" type="boolean" required="yes">
		<cfargument name="intakeFormTitle" type="string" required="yes">
		<cfargument name="confirmationEmailSubjectLine" type="string" required="yes">
		<cfargument name="intakeFormInstContent" type="string" required="yes">
		<cfargument name="intakeFormNonQualContent" type="string" required="yes">
		<cfargument name="startDate" type="string" required="yes">
		<cfargument name="endDate" type="string" required="yes">
		<cfargument name="goalAmount" type="numeric" required="yes">
		<cfargument name="distribIDList" type="string" required="yes">
		<cfargument name="allowSetStartDate" type="boolean" required="yes">
		<cfargument name="skipMemberLookup" type="boolean" required="yes">
		<cfargument name="newAcctFieldsetID" type="numeric" required="no" default="0">
		<cfargument name="newAcctFormTitle" type="string" required="no" default="">
		<cfargument name="showProfLicensesForNewAcct" type="boolean" required="no" default="0">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfif NOT (len(arguments.intakeFormInstContent) and len(arguments.intakeFormNonQualContent))>
			<cfset arguments.status = 'I'>
		</cfif>

		<cfif NOT isValidCampaignName(programID=arguments.programID, campaignID=arguments.campaignID, campaignName=arguments.campaignName)>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.msg = "That campaign name already exists for this program.">
			<cfreturn local.returnStruct>
		</cfif>

		<cfif arguments.campaignID gt 0 >
			<cfset updateCampaign(siteID=arguments.siteID, programID=arguments.programID, campaignID=arguments.campaignID, campaignName=arguments.campaignName, status=arguments.status,
				isHidden=arguments.isHidden, intakeFormTitle=arguments.intakeFormTitle, confirmationEmailSubjectLine=arguments.confirmationEmailSubjectLine, intakeFormInstContent=arguments.intakeFormInstContent, intakeFormNonQualContent=arguments.intakeFormNonQualContent,
				startDate=arguments.startDate, endDate=arguments.endDate, goalAmount=arguments.goalAmount, 
				distribIDList=arguments.distribIDList, allowSetStartDate=arguments.allowSetStartDate, skipMemberLookup=arguments.skipMemberLookup,
				newAcctFieldsetID=arguments.newAcctFieldsetID, newAcctFormTitle=arguments.newAcctFormTitle, showProfLicensesForNewAcct=arguments.showProfLicensesForNewAcct)>
		<cfelse>
			<cfset local.returnStruct.campaignID = createCampaign(siteID=arguments.siteID, programID=arguments.programID, campaignName=arguments.campaignName, status=arguments.status,
				isHidden=arguments.isHidden, intakeFormTitle=arguments.intakeFormTitle, confirmationEmailSubjectLine=arguments.confirmationEmailSubjectLine, intakeFormInstContent=arguments.intakeFormInstContent, intakeFormNonQualContent=arguments.intakeFormNonQualContent,
				startDate=arguments.startDate, endDate=arguments.endDate, goalAmount=arguments.goalAmount, 
				distribIDList=arguments.distribIDList, allowSetStartDate=arguments.allowSetStartDate, skipMemberLookup=arguments.skipMemberLookup,
				newAcctFieldsetID=arguments.newAcctFieldsetID, newAcctFormTitle=arguments.newAcctFormTitle, showProfLicensesForNewAcct=arguments.showProfLicensesForNewAcct)>
		</cfif>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createCampaign" access="private" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="campaignName" type="string" required="yes">
		<cfargument name="status" type="string" required="yes">
		<cfargument name="isHidden" type="boolean" required="yes">
		<cfargument name="intakeFormTitle" type="string" required="yes">
		<cfargument name="confirmationEmailSubjectLine" type="string" required="yes">
		<cfargument name="intakeFormInstContent" type="string" required="yes">
		<cfargument name="intakeFormNonQualContent" type="string" required="yes">
		<cfargument name="startDate" type="string" required="yes">
		<cfargument name="endDate" type="string" required="yes">
		<cfargument name="goalAmount" type="numeric" required="yes">
		<cfargument name="distribIDList" type="string" required="yes">
		<cfargument name="allowSetStartDate" type="boolean" required="yes">
		<cfargument name="skipMemberLookup" type="boolean" required="yes">
		<cfargument name="newAcctFieldsetID" type="numeric" required="yes">
		<cfargument name="newAcctFormTitle" type="string" required="no" default="">
		<cfargument name="showProfLicensesForNewAcct" type="boolean" required="no" default="0">

		<cfset var local = structNew()>

		<cfstoredproc procedure="cp_createCampaign" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.campaignName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isHidden#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.intakeFormTitle#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.confirmationEmailSubjectLine#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.intakeFormInstContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.intakeFormNonQualContent#">
			<cfif len(arguments.startDate) and len(arguments.endDate)>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.startDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.endDate#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="yes">
			</cfif>
			<cfif val(arguments.goalAmount) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.goalAmount#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.distribIDList#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowSetStartDate#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.skipMemberLookup#">
			<cfif arguments.skipMemberLookup>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.newAcctFieldsetID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.newAcctFormTitle#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.showProfLicensesForNewAcct#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.campaignID">
		</cfstoredproc>

		<cfreturn local.campaignID>
	</cffunction>

	<cffunction name="updateCampaign" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="campaignID" type="numeric" required="yes">
		<cfargument name="campaignName" type="string" required="yes">
		<cfargument name="status" type="string" required="yes">
		<cfargument name="isHidden" type="boolean" required="yes">
		<cfargument name="intakeFormTitle" type="string" required="yes">
		<cfargument name="confirmationEmailSubjectLine" type="string" required="yes">
		<cfargument name="intakeFormInstContent" type="string" required="yes">
		<cfargument name="intakeFormNonQualContent" type="string" required="yes">
		<cfargument name="startDate" type="string" required="yes">
		<cfargument name="endDate" type="string" required="yes">
		<cfargument name="goalAmount" type="numeric" required="yes">
		<cfargument name="distribIDList" type="string" required="yes">
		<cfargument name="allowSetStartDate" type="boolean" required="yes">
		<cfargument name="skipMemberLookup" type="boolean" required="yes">
		<cfargument name="newAcctFieldsetID" type="numeric" required="yes">
		<cfargument name="newAcctFormTitle" type="string" required="no" default="">
		<cfargument name="showProfLicensesForNewAcct" type="boolean" required="no" default="0">

		<cfset var local = structNew()>

		<cfstoredproc procedure="cp_updateCampaign" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.campaignID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.campaignName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isHidden#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.intakeFormTitle#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.confirmationEmailSubjectLine#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.intakeFormInstContent#">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.intakeFormNonQualContent#">
			<cfif len(arguments.startDate) and len(arguments.endDate)>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.startDate#">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.endDate#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="yes">
			</cfif>
			<cfif val(arguments.goalAmount) gt 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.goalAmount#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" null="yes">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.distribIDList#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowSetStartDate#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.skipMemberLookup#">
			<cfif arguments.skipMemberLookup>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.newAcctFieldsetID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.newAcctFormTitle#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.showProfLicensesForNewAcct#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="deleteCampaign" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="campaignID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteCampaign">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @campaignID int, @programID int;
					set @campaignID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.campaignID#">;
					set @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;

					IF NOT EXISTS (
						select 1
						from dbo.cp_campaigns as c
						inner join dbo.cp_programs as cp on cp.programID = c.programID
						inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
							and ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						where c.programID = @programID
						and c.campaignID = @campaignID
					)
						RAISERROR('invalid campaign',16,1);

					IF EXISTS (select 1 from dbo.cp_contributions where campaignID = @campaignID)
						RAISERROR('unable to delete campaign',16,1);

					BEGIN TRAN;
						delete from dbo.cp_campaignMatrix where campaignID = @campaignID;
						delete from dbo.cp_campaignDistributions where campaignID = @campaignID;
						delete from dbo.cp_campaigns where programID = @programID and campaignID = @campaignID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getProgramCampaigns" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="programID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.arrcampaigns" returntype="array">
			select c.campaignID as campaignid, c.campaignName as campaignname
			from dbo.cp_campaigns as c
			inner join dbo.cp_programs as cp on cp.programID = c.programID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
				and ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			where c.programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and c.[status] = 'A'
			order by c.campaignName
		</cfquery>

		<cfset local.returnStruct["success"] = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="isValidCampaignName" access="private" output="false" returntype="numeric">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="campaignID" type="numeric" required="true">
		<cfargument name="campaignName" type="string" required="true">

		<cfset var qryCheckCampaignName = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCheckCampaignName">
			select top 1 campaignID
			from dbo.cp_campaigns
			where programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			and campaignName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.campaignName#">
			<cfif arguments.campaignID gt 0>
				and campaignID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.campaignID#">
			</cfif>
		</cfquery>

		<cfreturn qryCheckCampaignName.recordCount is 0>
	</cffunction>

	<cffunction name="deleteCampaignMatrixOption" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="camid" type="numeric" required="true">
		<cfargument name="aid" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif arguments.camid lte 0>
				<cfthrow message="Invalid campaign">
			</cfif>
			<cfif arguments.aid lte 0>
				<cfthrow message="Invalid campaign option">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteMatrix">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @campaignID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.camid#">,
						@autoID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.aid#">;

					IF NOT EXISTS (
						select 1
						from dbo.cp_campaignMatrix as m
						inner join dbo.cp_campaigns as c on c.campaignID = m.campaignID
						inner join dbo.cp_programs as cp on cp.programID = c.programID
						inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
							and ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
						where m.autoID = @autoID
						and m.campaignID = @campaignID
					)
						RAISERROR('Invalid option for this campaign.',16,1);
						
					DELETE from dbo.cp_campaignMatrix
					WHERE autoID = @autoID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveCampaignMatrixOption" access="public" output="false" returntype="struct">
		<cfargument name="camid" type="numeric" required="true">
		<cfargument name="aid" type="numeric" required="true">
		<cfargument name="rid" type="numeric" required="true">
		<cfargument name="fid" type="numeric" required="true">
		<cfargument name="lbl" type="string" required="true">
		<cfargument name="isr" type="boolean" required="true">
		<cfargument name="setamt" type="string" required="true">
		<cfargument name="minamt" type="string" required="true">
		<cfargument name="maxamt" type="string" required="true">
		<cfargument name="defamt" type="numeric" required="true">
		<cfargument name="isperp" type="boolean" required="true">
		<cfargument name="isfixd" type="boolean" required="true">
		<cfargument name="defgiv" type="string" required="true">
		<cfargument name="minins" type="numeric" required="true">
		<cfargument name="admonly" type="boolean" required="true">
		
		<cfset var local = structNew()>

		<cftry>
			<cfif len(trim(arguments.setamt))>
				<cfset arguments.setamt = val(ReReplace(arguments.setamt,'[^0-9\.\-]','','ALL'))>
			</cfif>
			<cfif len(trim(arguments.minamt))>
				<cfset arguments.minamt = val(ReReplace(arguments.minamt,'[^0-9\.\-]','','ALL'))>
			</cfif>
			<cfif len(trim(arguments.maxamt))>
				<cfset arguments.maxamt = val(ReReplace(arguments.maxamt,'[^0-9\.\-]','','ALL'))>
			</cfif>
			<cfset arguments.defamt = val(ReReplace(arguments.defamt,'[^0-9\.\-]','','ALL'))>

			<cfif arguments.camid lte 0>
				<cfthrow message="Invalid campaign">
			</cfif>
			<cfif arguments.aid lt 0>
				<cfthrow message="Invalid campaign option">
			</cfif>
			<cfif arguments.rid lte 0>
				<cfthrow message="Invalid rate">
			</cfif>
			<cfif arguments.fid lte 0>
				<cfthrow message="Invalid frequency">
			</cfif>
			<cfif len(trim(arguments.lbl)) is 0>
				<cfthrow message="Invalid on-screen label">
			</cfif>
			<cfif arguments.isr is 0 and (len(trim(arguments.setamt)) is 0 or arguments.setamt lte 0)>
				<cfthrow message="Invalid installment amount">
			</cfif>
			<cfif arguments.isr is 1 and len(trim(arguments.minamt)) is 0 and len(trim(arguments.maxamt)) is 0>
				<cfthrow message="Invalid installment range">
			</cfif>
			<cfif arguments.isr is 1 and len(trim(arguments.minamt)) and arguments.minamt lte 0>
				<cfthrow message="Invalid installment range">
			</cfif>
			<cfif arguments.isr is 1 and len(trim(arguments.maxamt)) and arguments.maxamt lte 0>
				<cfthrow message="Invalid installment range">
			</cfif>
			<cfif arguments.isr is 1 and len(trim(arguments.minamt)) and len(trim(arguments.maxamt)) and arguments.minamt gte arguments.maxamt>
				<cfthrow message="Invalid installment range">
			</cfif>
			<cfif NOT isValid("integer",arguments.minins) OR arguments.minins lte 0>
				<cfthrow message="Invalid number of installments">
			</cfif> 
			<cfif arguments.isr is 0 and arguments.setamt neq arguments.defamt>
				<cfthrow message="Invalid default installment amount">
			</cfif>
			<cfif arguments.isr is 1 and len(trim(arguments.minamt)) and arguments.defamt lt arguments.minamt>
				<cfthrow message="Invalid default installment amount">
			</cfif>
			<cfif arguments.isr is 1 and len(trim(arguments.maxamt)) and arguments.defamt gt arguments.maxamt>
				<cfthrow message="Invalid default installment amount">
			</cfif>

			<cfset local.perpetualAsDefOption = 0>
			<cfset local.fixedInstallmentsAsDefOption = 0>
			<cfif arguments.isperp eq 1 AND arguments.defgiv eq 'perpetual'>
				<cfset local.perpetualAsDefOption = 1>
			<cfelseif arguments.isfixd eq 1 AND arguments.defgiv eq 'fixed'>
				<cfset local.fixedInstallmentsAsDefOption = 1>
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySaveMatrix">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @installSetAmt decimal(14,2), @installMinAmt decimal(14,2), @installMaxAmt decimal(14,2),
						@campaignID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.camid#">, 
						@autoID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.aid#">,
						@rateID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rid#">,
						@progFreqID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fid#">,
						@label varchar(200) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.lbl#">,
						@isRange bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isr#">,
						@installDefAmt decimal(14,2) = <cfqueryparam cfsqltype="CF_SQL_FLOAT" value="#arguments.defamt#">,
						@minInstallments int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.minins#">,
						@allowPerpetual bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isperp#">,
						@allowFixedInstallments bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isfixd#">,
						@perpetualAsDefOption bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.perpetualAsDefOption#">,
						@fixedInstallmentsAsDefOption bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.fixedInstallmentsAsDefOption#">,
						@adminOnly bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.admonly#">;

					<cfif len(arguments.setamt)>
						set @installSetAmt = <cfqueryparam cfsqltype="CF_SQL_FLOAT" value="#arguments.setamt#">;
					</cfif>
					<cfif len(arguments.minamt)>
						set @installMinAmt = <cfqueryparam cfsqltype="CF_SQL_FLOAT" value="#arguments.minamt#">;
					</cfif>
					<cfif len(arguments.maxamt)>
						set @installMaxAmt = <cfqueryparam cfsqltype="CF_SQL_FLOAT" value="#arguments.maxamt#">;
					</cfif>

					IF @autoID = 0 BEGIN
						IF EXISTS (select autoID from dbo.cp_campaignMatrix where campaignID = @campaignID and rateID = @rateID and progFreqID = @progFreqID)
							RAISERROR('Option already exists for this campaign.',16,1);

						INSERT INTO dbo.cp_campaignMatrix (campaignID, rateID, progFreqID, label, isRange, installSetAmt, installMinAmt, installMaxAmt, installDefAmt,
							allowPerpetual, perpetualAsDefOption, allowFixedInstallments, fixedInstallmentsAsDefOption, adminOnly, minInstallments)
						VALUES (@campaignID, @rateID, @progFreqID, @label, @isRange, @installSetAmt, @installMinAmt, @installMaxAmt, @installDefAmt,
							@allowPerpetual, @perpetualAsDefOption, @allowFixedInstallments, @fixedInstallmentsAsDefOption, @adminOnly, @minInstallments);
					END ELSE BEGIN
						IF NOT EXISTS (select 1 from dbo.cp_campaignMatrix where autoID = @autoID and campaignID = @campaignID)
							RAISERROR('Invalid option for this campaign.',16,1);
						IF EXISTS (select autoID from dbo.cp_campaignMatrix where campaignID = @campaignID and rateID = @rateID and progFreqID = @progFreqID and autoID <> @autoID)
							RAISERROR('Option already exists for this campaign.',16,1);

						UPDATE dbo.cp_campaignMatrix
						SET rateID = @rateID,
							progFreqID = @progFreqID,
							label = @label,
							isRange = @isRange,
							installSetAmt = @installSetAmt,
							installMinAmt = @installMinAmt,
							installMaxAmt = @installMaxAmt,
							installDefAmt = @installDefAmt,
							allowPerpetual = @allowPerpetual,
							perpetualAsDefOption = @perpetualAsDefOption,
							allowFixedInstallments = @allowFixedInstallments,
							fixedInstallmentsAsDefOption = @fixedInstallmentsAsDefOption,
							adminOnly = @adminOnly,
							minInstallments = @minInstallments
						WHERE autoID = @autoID;
					END

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getFinalInstallmentDate" access="public" output="false" returntype="struct">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="campaignID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="progFreqID" type="numeric" required="yes">
		<cfargument name="startDate" type="string" required="yes">
		<cfargument name="installmentAmount" type="numeric" required="yes">
		<cfargument name="pledgedAmount" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>

		<!--- cfquery instead of cfstoredproc because lucee returned the wrong date in queryparam --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetFinalInstallmentDate">
			SET NOCOUNT ON;

			DECLARE @finalInstallmentDate date;

			EXEC dbo.cp_getFinalInstallmentDate @programID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">,
				@campaignID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.campaignID#">,
				@rateID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateID#">,
				@progFreqID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.progFreqID#">,
				@startDate=<cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.startDate#">,
				@installmentAmount=<cfqueryparam cfsqltype="CF_SQL_DECIMAL" value="#arguments.installmentAmount#">,
				@pledgedAmount=<cfqueryparam cfsqltype="CF_SQL_DECIMAL" value="#arguments.pledgedAmount#">,
				@finalInstallmentDate=@finalInstallmentDate OUTPUT;

			SELECT @finalInstallmentDate as finalInstallmentDate;
		</cfquery>
		
		<cfset local.data.finalInstallmentDate = DateFormat(local.qryGetFinalInstallmentDate.finalInstallmentDate,"m/d/yyyy")>
		<cfset local.data.success = true>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="copyCampaign" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="campaignID" type="numeric" required="yes">
		<cfargument name="incAdditionalSettings" type="boolean" required="yes">
		<cfargument name="incMatrixOptions" type="boolean" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfstoredproc procedure="cp_copyCampaign" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.campaignID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incAdditionalSettings#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incMatrixOptions#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.returnStruct.newCampaignID">
		</cfstoredproc>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getContributionsFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="false">

		<cfset var local = structNew()>

		<cfset local.chkAll = arguments.event.getValue('chkAll',0)>
		<cfset local.contributionIDList = arguments.event.getValue('contributionIDList','')>
		<cfset local.notContributionIDList = arguments.event.getValue('notContributionIDList','')>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryContributions" result="local.qryContributionsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpContributionsSearch') IS NOT NULL 
					DROP TABLE ##tmpContributionsSearch;
				CREATE TABLE ##tmpContributionsSearch (contributionID int PRIMARY KEY);

				DECLARE @siteID int, @orgID int;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				
				<cfif arguments.event.getValue('associatedMemberID',0) gt 0>
					DECLARE @memberID int;
					DECLARE @tblMembers as TABLE (memberID int PRIMARY KEY);

					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('associatedMemberID')#">;

					INSERT INTO @tblMembers
					SELECT @memberID as memberID
					FROM dbo.ams_members as m 
					WHERE memberID = @memberID
					
					<cfif arguments.event.getValue('linkedRecords','') is "all">
						UNION
							
						SELECT allchildMember.memberID
						FROM dbo.ams_members as m WITH(NOLOCK)
						INNER JOIN dbo.ams_recordRelationships as rr WITH(NOLOCK) ON rr.orgID = @orgID
							AND rr.masterMemberID = m.memberID 
							AND rr.isActive = 1
						INNER JOIN dbo.ams_members as childMember WITH(NOLOCK) ON rr.childMemberID = childMember.memberID
						INNER JOIN dbo.ams_members as allchildMember WITH(NOLOCK) ON allchildMember.activeMemberID = childMember.memberID
						WHERE childMember.status <> 'D'
						AND m.memberID = @memberID
					</cfif>
				</cfif>

				INSERT INTO ##tmpContributionsSearch (contributionID)
				select c.contributionID
				from dbo.sites as s
				inner join dbo.cms_applicationInstances as ai on ai.siteID = s.siteID
				inner join dbo.cp_programs as p on p.applicationInstanceID = ai.applicationInstanceID
				inner join dbo.cp_contributions as c on c.programID = p.programID
				left outer join dbo.cp_campaigns as cpc on cpc.campaignID = c.campaignID
				inner join dbo.ams_members as m on m.memberID = c.memberID
				inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				<cfif arguments.mode is "massCancel">
					inner join dbo.cp_statuses st on st.statusID = c.statusID
						and st.statusCode in ('P','A','Q')
				</cfif>
				<cfif arguments.event.getValue('associatedMemberID',0) gt 0>
					inner join @tblMembers as m2 on m2.memberID = mActive.memberID
				</cfif>
				<cfif arguments.event.getValue('associatedGroupID',0) gt 0>
					inner join dbo.cache_members_groups mg ON mg.orgID = @orgID
						and mg.memberID = mActive.memberID 
						and mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('associatedGroupID')#">
				</cfif>
				where s.siteID = @siteID
				<cfif local.chkAll is 0 and len(local.contributionIDList) and arrayLen(reMatch("[^0-9,]",local.contributionIDList)) is 0>
					and c.contributionID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.contributionIDList#" list="yes">)
				<cfelse>
					<cfif local.chkAll is 1 and len(local.notContributionIDList) and arrayLen(reMatch("[^0-9,]",local.notContributionIDList)) is 0>
						and c.contributionID not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.notContributionIDList#" list="yes">)
					</cfif>
					<cfif arguments.event.getValue('memberID',0) gt 0>
						and m.activeMemberID = <cfqueryparam value="#arguments.event.getValue('memberID')#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
					<cfif ListLen(arguments.event.getValue('fProgram',''))>
						and p.programID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fProgram')#" list="true">)
					</cfif>
					<cfif ListLen(arguments.event.getValue('fCampaign',''))>
						and cpc.campaignID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fCampaign')#" list="true">)
					</cfif>
					<cfif len(arguments.event.getValue('fStartDateFrom',''))>
						and c.startdate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('fStartDateFrom')#">
					</cfif>
					<cfif len(arguments.event.getValue('fStartDateTo',''))>
						and c.startdate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getValue('fStartDateTo')#">
					</cfif>
					<cfif len(arguments.event.getValue('fEndDateFrom',''))>
						and c.enddate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('fEndDateFrom')#">
					</cfif>
					<cfif len(arguments.event.getValue('fEndDateTo',''))>
						and c.enddate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getValue('fEndDateTo')#">
					</cfif>
					<cfif ListLen(arguments.event.getValue('fFrequency',''))>
						and c.frequencyID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fFrequency')#" list="true">)
					</cfif>
					<cfif ListLen(arguments.event.getValue('fStatus',''))>
						and c.statusID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fStatus')#" list="true">)
					</cfif>
					<cfif ListLen(arguments.event.getValue('fRate',''))>
						and c.rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fRate')#" list="true">)
					</cfif>
					<cfif len(arguments.event.getValue('fHasCard',''))>
						<cfif arguments.event.getValue('fHasCard') eq 'Y'>
							and exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
						<cfelseif arguments.event.getValue('fHasCard') eq 'N'>
							and not exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
						</cfif>
					</cfif>
				</cfif>;

				<cfif arguments.mode is 'cpTabEmailGrid'>
					DECLARE @posStart int, @posStartAndCount int, @emailTagTypeID int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
					SET @emailTagTypeID = <cfqueryparam value="#arguments.event.getValue('emailTagType',0)#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tmpContributions') IS NOT NULL 
						DROP TABLE ##tmpContributions;
					CREATE TABLE ##tmpContributions (contributionID int, memberID int, memberFirstname varchar(75), memberLastname varchar(75), membernumber varchar(50),
						memberCompany varchar(200), memberEmail varchar(400), row int);

					INSERT INTO ##tmpContributions
					select distinct c.contributionID, mActive.memberID, mActive.firstname, mActive.lastname, mActive.membernumber, mActive.company, me.email, 
						ROW_NUMBER() OVER (ORDER BY (mActive.lastname + mActive.firstname + mActive.membernumber) #arguments.event.getValue('orderDir')#) as row
					from ##tmpContributionsSearch as tmp
					inner join dbo.cp_contributions as c on c.contributionID = tmp.contributionID
					inner join dbo.ams_members as m ON m.memberID = c.memberID
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.memberID = mActive.memberID
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
						and metag.memberID = me.memberID
						and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
						and metagt.emailTagTypeID = metag.emailTagTypeID
						and metagt.emailTagTypeID = @emailTagTypeID;

					select @totalCount = @@ROWCOUNT;

					select *, @totalCount as totalCount
					from ##tmpContributions
					where row > @posStart
					and row <= @posStartAndCount
					order by row;

					IF OBJECT_ID('tempdb..##tmpContributions') IS NOT NULL 
						DROP TABLE ##tmpContributions;
				<cfelseif arguments.mode is 'cpTabEmail'>
					DECLARE @membersWithEmail int, @emailTagTypeID int;
					SET @emailTagTypeID = <cfqueryparam value="#arguments.event.getValue('emailTagType',0)#" cfsqltype="CF_SQL_INTEGER">;

					select @membersWithEmail = count(*)
					from ##tmpContributionsSearch as tmp
					inner join dbo.cp_contributions as c on c.contributionID = tmp.contributionID
					inner join dbo.ams_members as m ON m.orgID = @orgID and m.memberID = c.memberID
					inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.memberID = mActive.memberID
						and me.email <> ''
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
						and metag.memberID = me.memberID 
						and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
						and metagt.emailTagTypeID = metag.emailTagTypeID
						and metagt.emailTagTypeID = @emailTagTypeID;

					select contributionID, @membersWithEmail as membersWithEmail
					from ##tmpContributionsSearch;
				<cfelse>
					select contributionID
					from ##tmpContributionsSearch;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpContributionsSearch') IS NOT NULL 
					DROP TABLE ##tmpContributionsSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryContributions>
	</cffunction>

	<cffunction name="getContributorDataForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="contributionID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.retStruct.qryData = getContributionDetails(contributionID=arguments.contributionID)>
		<cfset local.retStruct.extendedLinkedMergeCode = "">
		<cfset local.retStruct.arrResTypeMergeCodes = arrayNew(1)>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getFilteredContributorsForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = { itemIDList='', toolType='ContributionAdmin', catTreeCode='ETCONTRIBUTIONS', extendedLinkedMergeCode='', extraMergeTagList='', errorCode='' }>

		<cfset local.qryContributions = getContributionsFromFilters(event=arguments.event, mode='cpTabEmail')>

		<cfif local.qryContributions.recordcount is 0>
			<cfset local.retStruct.errorCode = 'norecipient'>
			<cfreturn local.retStruct>
		<cfelse>
			<cfset local.retStruct.itemIDList = valueList(local.qryContributions.contributionID)>
		</cfif>

		<cfif arguments.event.getValue('operationMode',"") neq "">
			<cfset local.retStruct.operationMode = arguments.event.getValue('operationMode')>
		</cfif>

		<!--- no email ids defined --->
		<cfif val(local.qryContributions.membersWithEmail) is 0>
			<cfset local.retStruct.errorCode = 'noemailrecipient'>
			<cfreturn local.retStruct>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="importCPCardsOnFile" access="package" output="false" returntype="struct">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="cpStatusCodeList" type="string" required="yes">

		<cfscript>
		var local = structNew();
		local.objImport = CreateObject("component","model.admin.common.modules.import.import");
		</cfscript>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errorCode = 999>
		<cfset local.returnStruct.errorInfo = StructNew()>

		<!--- upload files --->
		<cfset local.uploadResult = local.objImport.uploadFile(sitecode=arguments.sitecode, formFieldName='importAssocFile')>
		<cfif local.uploadResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.uploadResult.errMsg)>
			<cfreturn local.returnStruct>
		</cfif>

		<cfif local.uploadResult.ext eq "xls">
			<cfset local.parseResult = local.objImport.parseXLS(strFilePath=local.uploadResult.uploadedFile)>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfset local.csvFile = replaceNoCase(local.uploadResult.uploadedFile, ".xls", ".csv")>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfreturn local.returnStruct>
			</cfif>

			<!--- if only one sheet --->
			<cfif arrayLen(local.parseResult.arrSheets) is 1>
				<cfset local.parseResult = local.objImport.parseXLSSheet(strFilePath=local.uploadResult.uploadedFile,strFilePathCSV=local.csvFile,sheetIndex=0)>
				<cfset local.returnStruct.success = local.parseResult.success>
				<cfif NOT local.returnStruct.success>
					<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
					<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
					<cfreturn local.returnStruct>
				</cfif>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 7>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'The uploaded Excel file contains #arrayLen(local.parseResult.arrSheets)# sheets. Edit the file to contain only one sheet and try again.')>
				<cfreturn local.returnStruct>
			</cfif>
			<!--- Files was successfully read in --->
			<cfset local.uploadResult.uploadedFile = local.csvFile>
		</cfif>

		<!--- parse CSVs --->
		<cfset local.parseResult = local.objImport.parseCSV(stFilePath=local.uploadResult.uploadedFile, stFilePathTmp="#local.uploadResult.uploadedFilePath#/#local.uploadResult.uploadFilenameWithoutExt#Parsed.csv")>
		<cfif local.parseResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 2>
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfreturn local.returnStruct>
		<cfelse>
			<cfset local.uploadResult.columnNames = ToString(local.parseResult.strTableColumnNames)>
		</cfif>

		<!--- import files --->
		<cfset local.importToSQLResult = importCPCardsOnFileToSQL(strImportFile=local.uploadResult, columnNames=local.uploadResult.columnNames, siteID=arguments.siteID, programID=arguments.programID, cpStatusCodeList=arguments.cpStatusCodeList)>
 		<cfif local.importToSQLResult.isErr>
			<cfset local.returnStruct.success = false>
			<cfif isDefined("local.importToSQLResult.importResultXML")>
				<cfset local.returnStruct.errorCode = 5>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.importToSQLResult.importResultXML)>
			<cfelse>
				<cfset local.returnStruct.errorCode = 4>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.importToSQLResult.errMsg)>
			</cfif>
			<cfreturn local.returnStruct>
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="importCPCardsOnFileToSQL" access="private" output="false" returntype="struct">
		<cfargument name="strImportFile" type="struct" required="yes">
		<cfargument name="columnNames" type="string" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="cpStatusCodeList" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.rs = { isErr=0, errMsg='', errCount=0 } >

  		<cftry>
	        <cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @siteID int, @programID int, @importResult xml, @statusCodeList varchar(20), @recordedByMemberID int;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
					set @programID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">;
					set @statusCodeList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.cpStatusCodeList#">;
					set @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

					-- bulk insert from file
					BEGIN TRY
						IF OBJECT_ID('tempdb..##cp_ContributorCOFImport') IS NOT NULL 
							DROP TABLE ##cp_ContributorCOFImport;
						CREATE TABLE ##cp_ContributorCOFImport (
							<cfloop list="#arguments.columnnames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max)<cfif local.thisCol neq listLast(arguments.columnnames,chr(7))>, </cfif>
							</cfloop>
						);
						BULK INSERT ##cp_ContributorCOFImport 
							FROM '#arguments.strImportFile.uploadedFilePathUNC#\#arguments.strImportFile.uploadFilenameWithoutExt#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.cp_importAssociateCOF @siteID=@siteID, @programID=@programID, @statusCodeList=@statusCodeList, 
							@recordedByMemberID=@recordedByMemberID, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					on_done:
					declare @errCount int;
					select @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;

					IF OBJECT_ID('tempdb..##cp_ContributorCOFImport') IS NOT NULL 
						DROP TABLE ##cp_ContributorCOFImport;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.rs.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.rs.errCount = local.qryImport.errCount>
			<cfif local.rs.errCount gt 0>
				<cfset local.rs.isErr = 1>
			</cfif>

			<cfcatch type="Any">
				<cfset local.rs.isErr = 1>
				<cfset local.rs.errMsg = "There was a problem importing the file. Try the upload again or contact us for assistance.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfcatch>
		</cftry>
		<cfreturn local.rs>
	</cffunction>

	<cffunction name="showImportResults" access="public" output="false" returntype="string">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<h4>Associate Cards on File Import Results</h4>

			<cfif NOT arguments.strResult.success>
				<div class="alert alert-danger">
					<div class="font-weight-bold mb-3">The import was stopped and requires your attention.</div>

					<cfif arguments.strResult.errorCode is 5>
						<cfset local.arrErrors = XMLSearch(arguments.strResult.errorInfo[arguments.strResult.errorCode],"/import/errors/error")>
						<div>
						<cfif arrayLen(local.arrErrors) gt 200>
							<b>Only the first 200 errors are shown.</b><br/><br/>
						</cfif>
						<cfset local.thisErrNum = 0>
						<cfloop array="#local.arrErrors#" index="local.thisErr">
							<cfset local.thisErrNum = local.thisErrNum + 1>
							#local.thisErr.xmlAttributes.msg#<br/>
							<cfif local.thisErrNum is 200>
								<cfbreak>
							</cfif>
						</cfloop>
						</div>
					<cfelse>
						<div>#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
					</cfif>

					<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary mt-2" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
				</div>
			<cfelse>
				<div class="alert alert-success">
					<div class="font-weight-bold mb-3">Import Has Been Completed</div>
					<div>The import of the uploaded file has been completed.</div>
					<button type="button" class="btn btn-sm btn-secondary mt-2" onclick="top.reloadContributionsGrid();top.MCModalUtils.hideModal();">Close</button>
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getContributionsChangeLogFromFilters" access="public" output="false" returntype="any">
		<cfargument name="Event" type="any">
		<cfargument name="mode" type="string" required="true" hint="grid or export">
		<cfargument name="fieldsetID" type="numeric" required="false">
		<cfargument name="folderPathUNC" type="string" required="false">
		<cfargument name="reportFileName" type="string" required="false">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>
		
		<cfset local.programIDList = arguments.event.getValue('fProgram2','')>
		<cfset local.campaignIDList = arguments.event.getValue('fCampaign2','')>
		<cfset local.rateIDList = arguments.event.getValue('fRate2','')>
		<cfset local.frequencyIDList = arguments.event.getValue('fFrequency2','')>
		<cfset local.statusIDList = arguments.event.getValue('fStatus2','')>
		<cfset local.fStartDateFrom = arguments.event.getValue('fStartDateFrom2','')>
		<cfset local.fStartDateTo = arguments.event.getValue('fStartDateTo2','')>
		<cfset local.fEndDateFrom = arguments.event.getValue('fEndDateFrom2','')>
		<cfset local.fEndDateTo = arguments.event.getValue('fEndDateTo2','')>
		<cfset local.fHasCardOnFile = arguments.event.getValue('fHasCard2','')>
		<cfset local.fActivityFrom = arguments.event.getValue('fActivityFrom2','')>
		<cfset local.fActivityTo = arguments.event.getValue('fActivityTo2','')>
		<cfset local.fConStatusFrom = arguments.event.getValue('fConStatusFrom2','')>
		<cfset local.fConStatusTo = arguments.event.getValue('fConStatusTo2','')>
		
		<cfif len(local.fStartDateFrom)>
			<cfset local.fStartDateFrom = DateFormat(local.fStartDateFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fStartDateTo)>
			<cfset local.fStartDateTo = DateFormat(local.fStartDateTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>
		<cfif len(local.fEndDateFrom)>
			<cfset local.fEndDateFrom = DateFormat(local.fEndDateFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fEndDateTo)>
			<cfset local.fEndDateTo = DateFormat(local.fEndDateTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>
		<cfif len(local.fActivityFrom)>
			<cfset local.fActivityFrom = DateFormat(local.fActivityFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fActivityTo)>
			<cfset local.fActivityTo = DateFormat(local.fActivityTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>

		<cfif arguments.mode is 'grid'>
			<cfset local.orderDir = "#arguments.event.getValue('orderDir')#">
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"m.lastName #local.orderDir#, m.firstName, m.memberNumber")>
			<cfset arrayAppend(local.arrCols,"tmp.contributionID #local.orderDir#")>
			<cfset arrayAppend(local.arrCols,"tmp.currentStatus #local.orderDir#")>
			<cfset arrayAppend(local.arrCols,"tmp.previousStatus #local.orderDir#")>
			<cfset arrayAppend(local.arrCols,"tmp.updateDate #local.orderDir#")>
			<cfset arrayAppend(local.arrCols,"am.firstName #local.orderDir#, am.lastName")>
			<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>
		<cfelse>
			<cfset local.orderby = "m.lastName, m.firstName, m.memberNumber">
		</cfif>
		
		<cfquery name="local.qryContributionChangeLogs" result="local.qryContributionChangeLogsResult" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @totalRows int, @contributionIDList varchar(max), @orgID int, @siteID int, @totalCount INT;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			IF OBJECT_ID('tempdb..##tmpContributions') IS NOT NULL 
				DROP TABLE ##tmpContributions;
			IF OBJECT_ID('tempdb..##tmpContributions2') IS NOT NULL 
				DROP TABLE ##tmpContributions2;
			IF OBJECT_ID('tempdb..##tmpContributionsPledged') IS NOT NULL 
				DROP TABLE ##tmpContributionsPledged;			
			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmpFinal') IS NOT NULL 
				DROP TABLE ##tmpFinal;
			
			CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
			CREATE TABLE ##tmpContributions (programID int, campaignID int, programName varchar(200), campaignName varchar(400), rateName varchar(200), frequency varchar(20), contributionID int PRIMARY KEY, 
				memberID int, isPerpetual bit, startdate date, endDate date, statusName varchar(30), statusCode char(1), membername varchar(210), membercompany varchar(200));
			CREATE TABLE ##tmpContributions2 (updateDate DATETIME, currentStatus VARCHAR(30), previousStatus VARCHAR(30), actorMemberName VARCHAR(250), 
				programID int, campaignID int, programName varchar(200), campaignName varchar(400), rateName varchar(200), frequency varchar(20), contributionID int, 
				memberID int, isPerpetual bit, startdate date, endDate date, statusName varchar(30), statusCode char(1), membername varchar(210), membercompany varchar(200), totalPaid decimal(18,2), 
				payProfileID int, firstPaymentDate date, totalPledgeFirst decimal(18,2), totalPledgeRecurring decimal(18,2), pledgedValue decimal(18,2), totalRows int, row int);
			CREATE TABLE ##tmpContributionsPledged (contributionID int, totalPledgeFirst decimal(18,2), totalPledgeRecurring decimal(18,2), pledgedValue decimal(18,2));
			
			DECLARE @memberID int;
			<cfif arguments.event.getValue('associatedMemberID2',0) gt 0>
				DECLARE @tblMembers as TABLE (memberID int PRIMARY KEY);

				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('associatedMemberID2')#">;

				INSERT INTO @tblMembers
				SELECT @memberID as memberID
				FROM dbo.ams_members as m 
				WHERE memberID = @memberID
				
				<cfif arguments.event.getValue('linkedRecords2','') is "all">
					UNION
						
					SELECT allchildMember.memberID
					FROM dbo.ams_members as m 
					INNER JOIN dbo.ams_recordRelationships as rr ON rr.orgID = @orgID and rr.masterMemberID = m.memberID AND rr.isActive = 1
					INNER JOIN dbo.ams_members as childMember ON childMember.orgID = @orgID and rr.childMemberID = childMember.memberID
					INNER JOIN dbo.ams_members as allchildMember ON allchildMember.orgID = @orgID and allchildMember.activeMemberID = childMember.memberID
					WHERE m.orgID = @orgID
					AND m.memberID = @memberID
					AND childMember.status <> 'D'
				</cfif>
			</cfif>
			
			<cfif arguments.event.getValue('updtMemberID2',0) gt 0>
				DECLARE @tblUpdtMembers as TABLE (memberID int PRIMARY KEY);

				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('updtMemberID2')#">;

				INSERT INTO @tblUpdtMembers
				SELECT @memberID as memberID
				FROM dbo.ams_members as m 
				WHERE memberID = @memberID
				
				<cfif arguments.event.getValue('updtLinkedRecords2','') is "all">
					UNION
						
					SELECT allchildMember.memberID
					FROM dbo.ams_members as m 
					INNER JOIN dbo.ams_recordRelationships as rr ON rr.orgID = @orgID and rr.masterMemberID = m.memberID AND rr.isActive = 1
					INNER JOIN dbo.ams_members as childMember ON childMember.orgID = @orgID and rr.childMemberID = childMember.memberID
					INNER JOIN dbo.ams_members as allchildMember ON allchildMember.orgID = @orgID and allchildMember.activeMemberID = childMember.memberID
					WHERE m.orgID = @orgID
					AND m.memberID = @memberID
					AND childMember.status <> 'D'
				</cfif>
			</cfif>

			INSERT INTO ##tmpContributions (programID, campaignID, programName, campaignName, rateName, frequency, contributionID, memberID, isPerpetual, startdate, endDate, 
				statusName, statusCode, memberName, memberCompany)
			select p.programID, c.campaignID, p.programName, cpc.campaignName, cr.rateName, f.frequency, c.contributionID, mActive.memberID, c.isPerpetual, c.startdate, c.endDate, 
				cps.statusName, cps.statusCode, mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as memberName, mActive.company as memberCompany
			from dbo.sites as s
			inner join dbo.cms_applicationInstances as ai on ai.siteID = s.siteID
			inner join dbo.cp_programs as p on p.applicationInstanceID = ai.applicationInstanceID
			inner join dbo.cp_contributions as c on c.programID = p.programID
			left outer join dbo.cp_campaigns as cpc on cpc.campaignID = c.campaignID
			inner join dbo.cp_statuses as cps on cps.statusID = c.statusID
			inner join dbo.cp_frequencies as f on f.frequencyID = c.frequencyID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = c.memberID
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			left outer join dbo.cp_rates as cr on cr.rateID = c.rateID
			<cfif arguments.event.getValue('associatedMemberID2',0) gt 0>
				inner join @tblMembers as m2 on m2.memberID = mActive.memberID
			</cfif>
			<cfif arguments.event.getValue('associatedGroupID2',0) gt 0>
				inner join dbo.cache_members_groups mg ON mg.orgID = @orgID
					and mg.memberID = mActive.memberID 
					and mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('associatedGroupID2')#">
			</cfif>
			where s.siteID = @siteID
			<cfif ListLen(local.programIDList)>
				and p.programID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programIDList#" list="true">)
			</cfif>
			<cfif ListLen(local.campaignIDList)>
				and cpc.campaignID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.campaignIDList#" list="true">)
			</cfif>
			<cfif len(local.fStartDateFrom)>
				and c.startdate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fStartDateFrom#">
			</cfif>
			<cfif len(local.fStartDateTo)>
				and c.startdate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fStartDateTo#">
			</cfif>
			<cfif len(local.fEndDateFrom)>
				and c.enddate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fEndDateFrom#">
			</cfif>
			<cfif len(local.fEndDateTo)>
				and c.enddate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fEndDateTo#">
			</cfif>
			<cfif ListLen(local.frequencyIDList)>
				and c.frequencyID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.frequencyIDList#" list="true">)
			</cfif>
			<cfif ListLen(local.statusIDList)>
				and c.statusID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.statusIDList#" list="true">)
			</cfif>
			<cfif ListLen(local.rateIDList)>
				and c.rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateIDList#" list="true">)
			</cfif>
			<cfif len(local.fHasCardOnFile)>
				<cfif local.fHasCardOnFile eq 'Y'>
					and exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
				<cfelseif local.fHasCardOnFile eq 'N'>
					and not exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
				</cfif>
			</cfif>;

			IF OBJECT_ID('tempdb..##tblContributionChangeLogs') IS NOT NULL 
				DROP TABLE ##tblContributionChangeLogs;
				
			CREATE TABLE ##tblContributionChangeLogs (updateDate DATETIME, currentStatus VARCHAR(30), previousStatus VARCHAR(30), contributionID int, actorMemberName VARCHAR(250), row INT);

			;WITH allChangeLogs AS (
				SELECT 
					sh.updateDate,
					CASE WHEN st.statusID IS NOT NULL THEN st.statusName ELSE 'N/A' END AS currentStatus,
					CASE WHEN ost.statusID IS NOT NULL THEN ost.statusName ELSE 'N/A' END AS previousStatus,
					tmpCon.contributionID, tmpCon.memberID, am.activeMemberID AS actorMemberID
				FROM dbo.cp_statusHistory sh
				INNER JOIN ##tmpContributions tmpCon ON tmpCon.contributionID = sh.contributionID 
				LEFT OUTER JOIN dbo.cp_statuses st ON st.statusID = sh.statusID
				LEFT OUTER JOIN dbo.cp_statuses ost ON ost.statusID = sh.oldStatusID						
				INNER JOIN dbo.ams_members am ON am.memberID = sh.enteredByMemberID
				WHERE 1=1
				<cfif local.fConStatusFrom neq "0">
					AND ost.statusID IS NOT NULL AND ost.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.fConStatusFrom#">
				</cfif>
				<cfif local.fConStatusTo neq "0">
					AND st.statusID IS NOT NULL AND st.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.fConStatusTo#">
				</cfif>
			)
			INSERT INTO ##tblContributionChangeLogs
			SELECT DISTINCT tmp.updateDate, tmp.currentStatus, tmp.previousStatus, tmp.contributionID, 
			am.firstName + ' ' + am.lastName AS actorMemberName, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) AS row
			FROM allChangeLogs tmp
			INNER JOIN dbo.ams_members m ON m.orgID = @orgID AND m.memberID = tmp.memberID
			INNER JOIN dbo.ams_members am ON am.memberID = tmp.actorMemberID
			<cfif arguments.event.getValue('updtMemberID2',0) gt 0>
				INNER JOIN @tblUpdtMembers AS m2 ON m2.memberID = am.memberID
			</cfif>
			<cfif arguments.event.getValue('updtGroupID2',0) gt 0>
				INNER JOIN dbo.cache_members_groups mg ON mg.orgID = @orgID
					AND mg.memberID = am.memberID
					AND mg.groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('updtGroupID2',0)#">
			</cfif>
			WHERE 1=1				
			<cfif len(local.fActivityFrom) gt 0>
				AND tmp.updateDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.fActivityFrom#">
			</cfif>
			<cfif len(local.fActivityTo) gt 0>
				AND tmp.updateDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.fActivityTo#">
			</cfif>;
			
			SELECT @totalCount = @@ROWCOUNT;
			
			<cfif arguments.mode is 'grid'>
				DECLARE @posStart INT, @posStartAndCount INT;
				SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
				SET @posStartAndCount = @posStart + <cfqueryparam value="# arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
				
				INSERT INTO ##tmpContributions2 (updateDate, currentStatus, previousStatus, actorMemberName, programID, campaignID, programName, campaignName, rateName, frequency, contributionID, memberID, memberName, memberCompany, 
				isPerpetual, startdate, endDate, statusName, statusCode, row, totalRows)
				SELECT 
					updateDate, currentStatus, previousStatus, actorMemberName, programID, campaignID, programName, campaignName, rateName, frequency, 
					tmpCon.contributionID, memberID, memberName, memberCompany, isPerpetual, startdate, endDate, statusName, statusCode, row, @totalCount AS totalCount
				FROM ##tblContributionChangeLogs tmpSubCL
				INNER JOIN ##tmpContributions tmpCon on tmpCon.contributionID = tmpSubCL.contributionID
				WHERE row > @posStart
				AND row <= @posStartAndCount
				ORDER BY row;
				
				<!--- get pledged amount. 10/2017 - pledged amount should not consider actual transactions/adjustments --->
				select @contributionIDList = COALESCE(@contributionIDList + ',','') + cast(contributionID as varchar(10)) from ##tmpContributions2;
				INSERT INTO ##tmpContributionsPledged (contributionID, totalPledgeFirst, totalPledgeRecurring, pledgedValue)
				EXEC dbo.cp_getContributionAmountSplit @contributionIDList=@contributionIDList;

				UPDATE tmp2
				set tmp2.firstPaymentDate = tmp2FP.firstPaymentDate,
					tmp2.totalPaid = isnull(contribPaid.totalPaid,0),
					tmp2.payProfileID = tmpCC.payProfileID,
					tmp2.totalPledgeFirst = ple.totalPledgeFirst,
					tmp2.totalPledgeRecurring = ple.totalPledgeRecurring,
					tmp2.pledgedValue = ple.pledgedValue
				from ##tmpContributions2 as tmp2
				inner join (
					select cs.contributionID, min(cs.dueDate) as firstPaymentDate
					from dbo.cp_contributionSchedule as cs
					inner join ##tmpContributions2 as tmp on tmp.contributionID = cs.contributionID
					group by cs.contributionID
				) as tmp2FP on tmp2FP.contributionID = tmp2.contributionID
				inner join (
					select tmp.contributionID, max(isnull(cpp.payProfileID,0)) as payProfileID
					from ##tmpContributions2 as tmp
					left outer join dbo.cp_contributionPayProfiles as cpp on cpp.contributionID = tmp.contributionID
					group by tmp.contributionID
				) as tmpCC on tmpCC.contributionID = tmp2.contributionID
				cross apply dbo.fn_cp_totalFeeAndPaid(@orgID,tmp2.contributionID) as contribPaid
				inner join ##tmpContributionsPledged as ple on ple.contributionID = tmp2.contributionID;
				
				select c.updateDate, c.currentStatus, c.previousStatus, c.actorMemberName, c.programID, c.campaignID, c.programName, c.campaignName, c.rateName, c.frequency, c.contributionID, c.memberID, c.memberName, c.memberCompany, 
					c.isPerpetual, c.startdate, c.endDate, c.statusName, c.statusCode, c.totalPledgeFirst, c.totalPledgeRecurring, c.pledgedValue, c.firstPaymentDate, c.row, c.totalRows
				from ##tmpContributions2 as c
				order by c.row;
				
				
			<cfelseif arguments.mode is 'export'>
				DECLARE @fieldsetID int, @outputFieldsXML xml;
				SET @fieldsetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldSetID#">;

				-- prep final data
				INSERT INTO ##tmpContributions2 (updateDate, currentStatus, previousStatus, actorMemberName, programID, campaignID, programName, campaignName, rateName, frequency, contributionID, memberID, memberName, memberCompany, 
				isPerpetual, startdate, endDate, statusName, statusCode, row, totalRows)
				SELECT 
					updateDate, currentStatus, previousStatus, actorMemberName, programID, campaignID, programName, campaignName, rateName, frequency, 
					tmpCon.contributionID, memberID, memberName, memberCompany, isPerpetual, startdate, endDate, statusName, statusCode, row, @totalCount AS totalCount
				FROM ##tblContributionChangeLogs tmpSubCL
				INNER JOIN ##tmpContributions tmpCon on tmpCon.contributionID = tmpSubCL.contributionID;
				
				<!--- get pledged amount. 10/2017 - pledged amount should not consider actual transactions/adjustments --->
				select @contributionIDList = COALESCE(@contributionIDList + ',','') + cast(contributionID as varchar(10)) from ##tmpContributions2;
				INSERT INTO ##tmpContributionsPledged (contributionID, totalPledgeFirst, totalPledgeRecurring, pledgedValue)
				EXEC dbo.cp_getContributionAmountSplit @contributionIDList=@contributionIDList;

				UPDATE tmp2
				set tmp2.firstPaymentDate = tmp2FP.firstPaymentDate,
					tmp2.totalPaid = isnull(contribPaid.totalPaid,0),
					tmp2.payProfileID = tmpCC.payProfileID,
					tmp2.totalPledgeFirst = ple.totalPledgeFirst,
					tmp2.totalPledgeRecurring = ple.totalPledgeRecurring,
					tmp2.pledgedValue = ple.pledgedValue
				from ##tmpContributions2 as tmp2
				inner join (
					select cs.contributionID, min(cs.dueDate) as firstPaymentDate
					from dbo.cp_contributionSchedule as cs
					inner join ##tmpContributions2 as tmp on tmp.contributionID = cs.contributionID
					group by cs.contributionID
				) as tmp2FP on tmp2FP.contributionID = tmp2.contributionID
				inner join (
					select tmp.contributionID, max(isnull(cpp.payProfileID,0)) as payProfileID
					from ##tmpContributions2 as tmp
					left outer join dbo.cp_contributionPayProfiles as cpp on cpp.contributionID = tmp.contributionID
					group by tmp.contributionID
				) as tmpCC on tmpCC.contributionID = tmp2.contributionID
				cross apply dbo.fn_cp_totalFeeAndPaid(@orgID,tmp2.contributionID) as contribPaid
				inner join ##tmpContributionsPledged as ple on ple.contributionID = tmp2.contributionID;

				-- get fieldset data
				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmpContributions2', @membersResultTableName='##tmpMembers',
					@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

				SELECT tmp.contributionID, tmp.programName, tmp.campaignName, tmp.rateName, tmp.frequency, case when tmp.isPerpetual = 1 then 'YES' when tmp.isPerpetual = 0 then 'NO' end as isPerpetual, tmp.startdate, tmp.endDate, tmp.statusName as [Current Status],
				tmp.firstPaymentDate, tmp.totalPaid, tmp.totalPledgeFirst, tmp.totalPledgeRecurring, tmp.pledgedValue, tmp.currentStatus as [Status Changed To], tmp.previousStatus as [Status Changed From], tmp.updateDate as [Activity Date], tmp.actorMemberName as [Updated By], tmp.row, m.* 
				INTO ##tmpFinal
				FROM ##tmpContributions2 as tmp
				left JOIN ##tmpMembers as m on m.memberID = tmp.memberID;

				EXEC tempdb..sp_rename '##tmpFinal.memberID', 'MemberCentralID', 'COLUMN';
					
				DECLARE @selectsql varchar(max) = '
					SELECT *, row as mcCSVorder 
					*FROM* ##tmpFinal';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#arguments.folderPathUNC#/#arguments.reportFileName#",'\')#', @returnColumns=0;
			</cfif>
			
			IF OBJECT_ID('tempdb..##tmpContributions') IS NOT NULL 
				DROP TABLE ##tmpContributions;
			IF OBJECT_ID('tempdb..##tmpContributions2') IS NOT NULL 
				DROP TABLE ##tmpContributions2;
			IF OBJECT_ID('tempdb..##tmpContributionsPledged') IS NOT NULL 
				DROP TABLE ##tmpContributionsPledged;
			IF OBJECT_ID('tempdb..##tblContributionChangeLogs') IS NOT NULL
				DROP TABLE ##tblContributionChangeLogs;
			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmpFinal') IS NOT NULL 
				DROP TABLE ##tmpFinal;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfif arguments.mode is 'grid'>
			<cfreturn local.qryContributionChangeLogs>
		</cfif>
	</cffunction>
	
	<cffunction name="removeContributionAppInstance" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric">
		<cfargument name="applicationInstanceID" type="numeric">
		<cfargument name="appResourceID" type="numeric">
		<cfargument name="pageResourceID" type="numeric">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID INT, @applicationInstanceID INT, @appResourceID INT, @pageResourceID INT;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
					SET @appResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.appResourceID#">;
					SET @pageResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pageResourceID#">;					
					SET @applicationInstanceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.applicationInstanceID#">;

					IF NOT EXISTS (
						SELECT 1 
						FROM dbo.cp_programs AS cp
						INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = cp.applicationInstanceID AND ai.siteID = @siteID AND ai.siteResourceID = @appResourceID
						INNER JOIN dbo.cms_applicationTypes AS at ON at.applicationTypeID = ai.applicationTypeID AND at.applicationTypeName = 'Contributions'
						INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = cp.siteResourceID
						WHERE ai.applicationInstanceID = @applicationInstanceID
					)
					BEGIN
						EXEC dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@pageResourceID;
					END

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>			

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>