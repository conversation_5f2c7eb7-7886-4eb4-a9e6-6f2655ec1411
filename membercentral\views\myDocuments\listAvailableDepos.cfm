<cfoutput>
<cfif local.qryDepos.recordCount>
	<div class="d-flex justify-content-between align-items-center bg-white border rounded px-3 py-2 mb-2 shadow-sm flex-sm-row flex-column">
		<div>
			<cfif local.totalCount GT local.maxPerPage>
				Showing <span class="fw-semibold">#local.startRow#-<cfif ((local.startRow + local.maxPerPage) - 1) lte local.totalCount>#local.startRow + local.maxPerPage - 1#<cfelse>#local.totalCount#</cfif> of #NumberFormat(local.totalCount,",")#</span> Depositions
			<cfelse>
				Showing <span class="fw-semibold">#local.totalCount#</span> Deposition<cfif local.totalCount GT 1>s</cfif>
			</cfif>
		</div>
		<nav>
			<ul class="pagination pagination-sm mb-0">
				<cfif local.numCurrentPage GT 1>
					<li class="page-item">
						<a href="##" class="page-link" onclick="PTSAI_filterAvailDepos(#local.startRow - local.maxPerPage#);return false;">Previous</a>
					</li>
				</cfif>
				<cfif local.numTotalPages GT 1>
					<li class="page-item text-nowrap disabled"><span class="page-link">Page #local.numCurrentPage# of #local.endPage#</span></li>
				</cfif>
				<cfif local.numCurrentPage NEQ local.endPage>
					<li class="page-item">
						<a href="##" class="page-link" onclick="PTSAI_filterAvailDepos(#local.startRow + local.maxPerPage#);return false;">Next</a>
					</li>
				</cfif>
			</ul>
		</nav>
	</div>

	<cfloop query="local.qryDepos">
		<cfset local.selectedCard = listFind(local.selectedDocIDs,local.qryDepos.documentID)>
		<cfset local.currentDocument.load(local.qryDepos.documentid)>
		<cfset local.mycost = local.currentDocument.getPrice(membertype=local.cfcuser_membertype, billingstate=local.cfcuser_billingstate, billingzip=local.cfcuser_billingzip, websiteorgcode=session.mcstruct.sitecode, depomemberdataid=session.cfcuser.memberdata.depomemberdataid)>
		<div class="card mb-3 shadow-sm add-depo-card<cfif local.selectedCard> selected</cfif>" role="button">
			<div class="card-body d-flex justify-content-between align-items-start">
				<div class="d-flex align-items-start">
					<div class="form-check me-2">
						<input type="checkbox" class="form-check-input add-depo-checkbox" name="add-depo-#local.qryDepos.documentID#" id="add-depo-#local.qryDepos.documentID#" value="#local.qryDepos.documentID#"<cfif local.selectedCard> checked</cfif>>
						<label class="form-check-label visually-hidden" for="add-depo-#local.qryDepos.documentID#">Select Deposition</label>
					</div>
					<div>
						<h6 class="fw-bold mb-1"><cfif len(trim(local.qryDepos.expertName))>#trim(local.qryDepos.expertName)#<cfelse>Unknown Expert</cfif></h6>
						<small class="text-muted">#local.qryDepos.style# (###local.qryDepos.documentID#)</small><br>
						<cfif len(local.qryDepos.Description)><small class="text-muted">#local.qryDepos.Description#</small></cfif>
						<cfif len(local.qryDepos.billingFirm)><small class="text-muted"> (#local.qryDepos.billingFirm#)</small></cfif>
						
					</div>
				</div>
				<div class="text-end text-nowrap ps-2">
					<small class="text-muted">#DateFormat(local.qryDepos.documentDate,"mmm d, yyyy")#</small>
					<div class="fw-semibold text-success mt-1">#replace(dollarformat(precisionEvaluate(local.mycost.price + val(local.qryDepoCaseActiveBillingPlan.feePerUpload))),".00","")#</div>
				</div>
			</div>
		</div>
	</cfloop>

	<cfif local.numTotalPages gt 1>
		<nav aria-label="Deposition navigation">
			<ul class="pagination justify-content-center">
				<cfif local.numCurrentPage gt 1>
					<cfif local.numTotalPages GT local.maxPage>
						<li class="page-item">
							<a href="##" onclick="PTSAI_filterAvailDepos(1);return false;" title="First Page" class="page-link">&lt;&lt;</a>
						</li>
					</cfif>
					<li class="page-item">
						<a href="##" onclick="PTSAI_filterAvailDepos(#local.startRow - local.maxPerPage#);return false;" title="Previous Page" class="page-link">Previous</a>
					</li>
				</cfif>

				<cfloop from="#local.startPage#" to="#local.endPage#" index="local.i">
					<cfset local.thisStartRow = local.i>
					<cfif local.i gt 1>
						<cfset local.thisStartRow = (local.maxPerPage * (local.i-1)) + 1>
					</cfif>

					<cfif local.numCurrentPage eq local.i>
						<li class="active"><a href="##" onclick="return false;" class="page-link">#local.i#</a></li>
					<cfelse>
						<li class="page-item"><a href="##" onclick="PTSAI_filterAvailDepos(#local.thisStartRow#);return false;" class="page-link">#local.i#</a></li>
					</cfif>
				</cfloop>

				<cfif local.numCurrentPage lt local.numTotalPages>
					<li class="page-item">
						<a href="##" onclick="PTSAI_filterAvailDepos(#local.startRow + local.maxPerPage#);return false;" title="Next Page" class="page-link">
							Next
						</a>
					</li>
					<cfif local.numTotalPages GT local.maxPage>
						<li class="page-item">
							<a href="##" onclick="PTSAI_filterAvailDepos(#(local.maxPerPage * (local.numTotalPages-1)) + 1#);return false;" title="Last Page" class="page-link">
								&gt;&gt;
							</a>
						</li>
					</cfif>
				</cfif>
			</ul>
		</nav>
	</cfif>

<cfelse>
	<div class="alert alert-light border rounded-3 shadow-sm text-center py-4 mt-3">
		<div class="d-flex justify-content-center align-items-center mb-2">
			<i class="fa-regular fa-folder-open fa-2x text-muted"></i>
		</div>
		<h5 class="fw-semibold text-muted mb-1">No Depositions Found</h5>
	</div>
</cfif>
</cfoutput>