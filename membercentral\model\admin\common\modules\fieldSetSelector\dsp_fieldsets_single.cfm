<cfsavecontent variable="local.fsSelectorJS">
	<cfoutput>
	<script language="javascript">
		function onChangeFieldSetSelection_#arguments.selectorID#(collapse) {
			var el = $('input[type="radio"][name="radio_#arguments.selectorID#"]:checked');
			var value = $(el).val();
			var idVal = $(el).attr("id");
			var labelVal = $("label[for='"+idVal+"']").text();
			$('###arguments.selectorID#').val(value).trigger('change');
			$('##selectedFSLabel_#arguments.selectorID#').text(labelVal).toggleClass('text-grey', (value == 0 ? true : false));
			$('.list-group.list-group-#arguments.selectorID# li.list-group-item').removeClass('bg-neutral-success');
			if(value > 0) $(el).closest('li.list-group-item').addClass('bg-neutral-success');
			if(collapse) $('##collapse_#arguments.selectorID#').collapse('hide');
		}
		
		<cfif not local.useInlinePreview>
			function editFieldSet_#arguments.selectorID#(fsID) {
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: fsID > 0 ? 'Edit Field Set' : 'Create a Field Set',
					iframe: true,
					contenturl: '#local.editFieldSetLink#&fsID='+fsID,
					strmodalfooter: {
						classlist: 'text-right',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttononclickhandler: 'saveMemFieldSet_#arguments.selectorID#',
						extrabuttonlabel: 'Save'
					}
				});
			}
			function saveMemFieldSet_#arguments.selectorID#() {
				$('##MCModalBodyIframe')[0].contentWindow.validateAndSaveFieldSet();
			}
			function previewFieldSet_#arguments.selectorID#(fsID) {
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					title: 'Preview Field Set',
					contenturl: '#local.previewFieldSetLink#&fsID='+fsID,
					strmodalfooter: { showclose: true }
				});
			}
		<cfelse>
			function editFieldSet_#arguments.selectorID#(fsID, fscreated) {
				$('###arguments.inlinePreviewSectionID#').addClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview').removeClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview .headerLabel').html(fsID > 0 ? 'Edit Field Set' : 'Create a Field Set');
				$('###arguments.inlinePreviewSectionID#_fsPreview .divFSFormContainer').html(mca_getLoadingHTML()).load('#local.editFieldSetLink#&fsID='+fsID + '&isModalContent=0' + (fscreated ? '&fscreated=1' : ''));
			}
			function previewFieldSet_#arguments.selectorID#(fsID) {
				$('###arguments.inlinePreviewSectionID#').addClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview').removeClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview .headerLabel').html('Preview Field Set');
				$('###arguments.inlinePreviewSectionID#_fsPreview .divFSFormContainer').html(mca_getLoadingHTML()).load('#local.previewFieldSetLink#&fsID='+fsID);
			}
			function cancelFSPreviewForm_#arguments.selectorID#(){
				$('###arguments.inlinePreviewSectionID#_fsPreview .divFSFormContainer').html('');
				$('###arguments.inlinePreviewSectionID#_fsPreview').addClass('d-none');
				$('###arguments.inlinePreviewSectionID#').removeClass('d-none');
			}
		</cfif>
		
		<cfif arguments.allowBlankOption>
			function clearFieldSetSelection_#arguments.selectorID#() {
				$("##radio_#arguments.selectorID#_0").prop("checked", true).change();
			}
		</cfif>
		function loadFieldSetGrids_#arguments.selectorID#(newFieldSetID) {
			let loadFSResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					let fsListSource = $('##mc_FSList_#arguments.selectorID#').html();
					let fsListTemplate = Handlebars.compile(fsListSource);
					$('##fsGridContainer_#arguments.selectorID#').html(fsListTemplate(r));
					mcActivateTooltip($('##fsGridContainer_#arguments.selectorID#'));
					bindFSRadioChangeEvent_#arguments.selectorID#();

					/* select newly created fieldset or previous selection */
					var fsIDToSelect = (newFieldSetID && newFieldSetID) > 0 ? newFieldSetID : $('###arguments.selectorID#').val();
					$('##radio_#arguments.selectorID#_' + fsIDToSelect).prop("checked", true);
					<cfif not arguments.allowBlankOption>
						/* if blank option is not allowed and no selection is made yet, try to auto-select the first option */
						if ((fsIDToSelect || 0) == 0){
							var radioElms = $('input:not(##radio_#arguments.selectorID#_0)[type="radio"][name="radio_#arguments.selectorID#"]');
							if(radioElms.length > 0) $(radioElms[0]).prop("checked", true);
						}
					</cfif>
					onChangeFieldSetSelection_#arguments.selectorID#(false);
				} else {
					let reloadHTML = '<div class="text-center mt-5"><span class="d-block text-danger mb-2">Sorry, we were unable to load the data.</span><i class="fa-solid fa-rotate-right fa-2x cursor-pointer" onclick="loadFieldSetGrids_#arguments.selectorID#()"></i><span class="d-block">Reload</span></div>';
					$('##fsGridContainer_#arguments.selectorID#').html(reloadHTML);
				}
			};

			$('##fsGridContainer_#arguments.selectorID#').html(mca_getLoadingHTML());
			<cfif len(arguments.getFieldSetDataFunc)>
				#arguments.getFieldSetDataFunc#(loadFSResult);
			<cfelse>
				getFieldSetsJSON_#arguments.selectorID#(loadFSResult);
			</cfif>
		}
		function getFieldSetsJSON_#arguments.selectorID#(onCompleteFunc){
			TS_AJX('FIELDSETWIDGET','getFieldSetsJSON',{},onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}
		function bindFSRadioChangeEvent_#arguments.selectorID#(){
			$('input[type=radio][name=radio_#arguments.selectorID#]').off('change').change(function() {
				onChangeFieldSetSelection_#arguments.selectorID#(true);
			});
		}

		$(function() {
			loadFieldSetGrids_#arguments.selectorID#();
			<cfif local.useInlinePreview>
				if($('###arguments.inlinePreviewSectionID#_fsPreview').length == 0){
					$('###arguments.inlinePreviewSectionID#').after($('##mc_FSPreviewContainer_#arguments.selectorID#').html());
				}
			</cfif>
			$(document).on('shown.bs.collapse', '##collapse_#arguments.selectorID#', function (e) {
				let listGrpItem = $('ul.list-group-#arguments.selectorID#').find('li.list-group-item.bg-neutral-success');	
				let fsGridCard = $('##fsGridCard_#arguments.selectorID#');
				if (listGrpItem.length) {
					fsGridCard.scrollTop(fsGridCard.scrollTop() + listGrpItem.position().top - fsGridCard.height()/2 + listGrpItem.height()/2); 
				}
			});
		});
	</script>
	<style>
		##accordion_#arguments.selectorID# > .card { box-shadow: none; border-bottom: 1px solid rgba(122, 123, 151, 0.3); }
		##fsGridContainer_#arguments.selectorID# .btn-xs.btnPreviewFS { padding-right: 0.3rem; padding-left: 0.3rem; }
		##fsGridContainer_#arguments.selectorID# .btn-xs.btnEditFS { padding-right: 0.32rem; padding-left: 0.32rem; }
		##fsGridContainer_#arguments.selectorID# .custom-control-label:before { border-color: ##a8a8a8; }
		<cfif len(arguments.fieldLabel)>
		##accordion_#arguments.selectorID# ##heading_#arguments.selectorID#.card-header .field-header-label {
			position: absolute;
			top: 0.25rem;
			left: .5rem;
			font-size: 0.75rem;
			color: ##6c757d;
			padding: 0 0.25rem;
			pointer-events: none; /* don't block clicks */
			z-index: 2;
		}
		##accordion_#arguments.selectorID# ##heading_#arguments.selectorID#.card-header span.field-header-label + .btn-link span {
			margin-top: .8rem;
		}
		</cfif>
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.fsSelectorJS)#">

<cfoutput>
<input type="hidden" name="#arguments.selectorID#" id="#arguments.selectorID#" value="#local.selectedFieldSetID#" />

<div class="row no-gutters">
	<div class="col-12">
		<div class="accordion" id="accordion_#arguments.selectorID#">
			<div class="card card-box rounded">
				<div class="card-header" id="heading_#arguments.selectorID#">
					<cfif len(arguments.fieldLabel)>
						<span class="field-header-label">#arguments.fieldLabel#</span>
					</cfif>
					<button class="btn btn-link d-flex align-items-center justify-content-between collapsed" type="button" data-toggle="collapse" data-target="##collapse_#arguments.selectorID#" aria-expanded="false" aria-controls="collapse_#arguments.selectorID#">
						<span id="selectedFSLabel_#arguments.selectorID#">#local.selectedFieldSetLabel#</span>
						<i class="fa-solid fa-caret-up font-size-xl"></i>
					</button>
				</div>
				<div id="collapse_#arguments.selectorID#" class="collapse" aria-labelledby="heading_#arguments.selectorID#" data-parent="##accordion_#arguments.selectorID#" style="">
					<div class="card">
						<div class="card-header bg-light rounded-0 py-2">
							<div class="card-header--title font-size-xs d-flex">
								<span class="font-italic">Choose From Available Field Sets</span>
								<cfif arguments.allowBlankOption>
									<span class="ml-auto mr-4 px-2 btn-link cursor-pointer text-grey font-size-xs" onclick="clearFieldSetSelection_#arguments.selectorID#()">clear selection</span>
								</cfif>
							</div>
							<div class="card-header--actions">
								<a href="##" name="btnCreateFieldSet" onclick="editFieldSet_#arguments.selectorID#(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create a Field Set">
									<i class="fa-regular fa-circle-plus fa-lg"></i>
								</a>
							</div>
						</div>
						<div id="fsGridCard_#arguments.selectorID#" class="card-body bg-secondary p-2" style="height:250px;overflow-y:auto;">
							<div class="d-none">
								<input type="radio" id="radio_#arguments.selectorID#_0" name="radio_#arguments.selectorID#" value="0" <cfif arguments.allowBlankOption and local.selectedFieldSetID eq 0>checked</cfif>>
								<label for="radio_#arguments.selectorID#_0">Choose Field Set</label>
							</div>
							<div id="fsGridContainer_#arguments.selectorID#"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script id="mc_FSList_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##if arravailablefieldsets}}
		{{##each arravailablefieldsets}}
			<div class="bg-secondary mb-3">
				<div><i class="fa-regular fa-tag font-size-md text-warning mr-2"></i><b class="font-size-sm">{{categoryname}}</b></div>
				<ul class="list-group list-group-#arguments.selectorID# mt-2">
					{{##each arrfieldsets}}
						<li class="list-group-item py-1">
							<div class="row no-gutters align-items-center">
								<div class="col font-size-sm">
									<div class="custom-control custom-radio">
										<input type="radio" id="radio_#arguments.selectorID#_{{fieldsetid}}" name="radio_#arguments.selectorID#" value="{{fieldsetid}}" class="custom-control-input">
										<label class="custom-control-label" for="radio_#arguments.selectorID#_{{fieldsetid}}">{{fieldsetname}}</label>
									</div>
								</div>
								<div class="col-auto pl-2">
									<a href="##" onclick="$(this).tooltip('hide');editFieldSet_#arguments.selectorID#({{fieldsetid}});return false;" class="btn btn-xs btn-outline-primary btnEditFS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit this Field Set">
										<i class="fa-solid fa-pencil"></i>
									</a>
									<a href="##" onclick="$(this).tooltip('hide');previewFieldSet_#arguments.selectorID#({{fieldsetid}});return false;" class="btn btn-xs btn-outline-info btnPreviewFS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Preview this Field Set">
										<i class="fa-solid fa-eye"></i>
									</a>
								</div>
							</div>
						</li>
					{{/each}}
				</ul>
			</div>
		{{/each}}
	{{else}}
		<div class="text-center py-3">No Field Sets Available.</div>
	{{/if}}
</script>
<cfif local.useInlinePreview>
	<script id="mc_FSPreviewContainer_#arguments.selectorID#" type="text/html">
		<div id="#arguments.inlinePreviewSectionID#_fsPreview" class="p-3 d-none">
			<div class="mb-3"><a href="##" onclick="return cancelFSPreviewForm_#arguments.selectorID#();"><i class="fa-solid fa-chevrons-left"></i> Return to Field Set Selection</a></div>
			<div class="card card-box">
				<div class="card-header py-2 bg-light">
					<div class="card-header--title font-size-lg headerLabel"></div>
				</div>
				<div class="card-body p-2 divFSFormContainer"></div>
			</div>
		</div>
	</script>
</cfif>
</cfoutput>