ALTER PROC dbo.store_getAvailableProductFormatsForCart
@storeID int, 
@itemID int,
@memberID int,
@qualifyFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @orgID int, @groupPrintID int;
	DECLARE @tmpProductFormats TABLE (formatID int PRIMARY KEY);
	DECLARE @tmpProductFormatInventoryCount TABLE (formatID int PRIMARY KEY, formatPurchasedCount int);

	select @siteID = siteID
	from dbo.store
	where storeID = @storeID;

	select @orgID = orgID, @groupPrintID = groupPrintID
	from dbo.ams_members
	where memberID = @memberID;

	INSERT INTO @tmpProductFormats (formatID)
	SELECT Formatid
	FROM dbo.store_ProductFormats
	WHERE itemid = @itemID
	AND [status] = 'A';

	INSERT INTO @tmpProductFormatInventoryCount (formatID, formatPurchasedCount)
	SELECT od.formatID, SUM(od.Quantity)
	FROM @tmpProductFormats AS tmp
	INNER JOIN dbo.store_orderDetails AS od ON od.formatID = tmp.formatID
	GROUP BY od.formatID;

	SELECT DISTINCT spf.formatid, fr.rateid, spf.Name, fr.rate, spf.formatOrder,
		ISNULL(spf.inventory,0) AS inventory, ISNULL(tmp.formatPurchasedCount,0) AS formatPurchasedCount,
		CASE WHEN spf.inventory IS NULL THEN 1
			WHEN ISNULL(spf.inventory,0) > 0 AND spf.inventory > ISNULL(tmp.formatPurchasedCount,0) THEN 1
			ELSE 0 END AS isAvailable,
		CASE WHEN s.offerAffirmations = 1 AND spf.offerAffirmations = 1 THEN spf.quantity
		ELSE 0 END AS numAffirmationsIncluded
	from dbo.store_Products as sp
	inner join dbo.store as s on s.siteID = @siteID 
		and s.storeID = @storeID
		and sp.showAvailable = 1
	inner join dbo.store_ProductFormats as spf on sp.itemid = spf.itemid 
		and spf.status = 'A'
	inner join dbo.store_rates as sr on spf.formatid = sr.formatid 
	inner join dbo.cms_siteResources as c on c.siteID = @siteID 
		and c.siteResourceID = sr.siteResourceID 
		and c.siteResourceStatusID = 1
	inner join dbo.cms_siteResourceRights as srr on c.siteResourceID = srr.resourceID 
		and srr.siteID = @siteID
	inner join dbo.ams_groups as g on g.orgID = @orgID 
		and g.groupid = srr.groupid
	inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
		and srfrp.siteresourceID = c.siteResourceID
		and srfrp.functionID = @qualifyFID
	inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
		and gprp.rightPrintID = srfrp.rightPrintID
		and gprp.groupPrintID = @groupPrintID
	left outer join @tmpProductFormatInventoryCount as tmp on tmp.formatID = spf.formatID
	cross apply dbo.fn_store_getBestFormatRate(spf.formatid,@qualifyFID,@memberID,@siteID) as fr
	where sp.storeID = @storeID
	and spf.itemid = @itemID
	and spf.status = 'A'
	and (sr.startDate is null or CAST( CONVERT( char(8), sr.startDate, 112) AS datetime) <= CAST( CONVERT( char(8), getDate(), 112) as datetime))
	and (sr.endDate is null or DATEADD (d , 1, CAST( CONVERT( char(8), sr.endDate, 112) AS datetime) ) > getdate())
	order by spf.formatOrder, spf.Name;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO