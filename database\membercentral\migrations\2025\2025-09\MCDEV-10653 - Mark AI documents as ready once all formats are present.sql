use platformQueue;
GO
ALTER PROC dbo.queue_chatDocumentPrep_moveWaiting

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpCaseExpertDocs') IS NOT NULL 
		DROP TABLE #tmpCaseExpertDocs;
	IF OBJECT_ID('tempdb..#tmpCaseExpertDocsOnline') IS NOT NULL 
		DROP TABLE #tmpCaseExpertDocsOnline;
	IF OBJECT_ID('tempdb..#tmpCaseExpertAllDocsOnline') IS NOT NULL 
		DROP TABLE #tmpCaseExpertAllDocsOnline;
	CREATE TABLE #tmpCaseExpertDocs (caseExpertID int, documentID int);
	CREATE TABLE #tmpCaseExpertDocsOnline (caseExpertID int, documentID int, isOnline bit);
	CREATE TABLE #tmpCaseExpertAllDocsOnline (caseExpertID int);

	DECLARE @queueTypeID int, @statusWaiting int, @statusReady int, @numFileTypes smallint;
	DECLARE @tmpFileTypes TABLE (fileType varchar(10));

	EXEC dbo.queue_getQueueTypeID @queueType='chatDocumentPrep', @queueTypeID=@queueTypeID OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='waitingToProcess', @queueStatusID=@statusWaiting OUTPUT;
	EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

	INSERT INTO @tmpFileTypes (fileType)
	VALUES ('pagejson'),('insights'),('semantic');
	
	SELECT @numFileTypes = COUNT(*) FROM @tmpFileTypes;

	-- get case expert docs in waiting to process status
	INSERT INTO #tmpCaseExpertDocs (caseExpertID, documentID)
	SELECT DISTINCT qid.caseExpertID, d.documentID
	FROM dbo.queue_chatDocumentPrep AS qid
	INNER JOIN trialsmith.dbo.depomemberdataCaseExpertDocuments AS d ON d.caseID = qid.caseID
		AND d.caseExpertID = qid.caseExpertID
		AND d.isActive = 1
	WHERE qid.statusID = @statusWaiting;

	INSERT INTO #tmpCaseExpertDocsOnline (caseExpertID, documentID, isOnline)
	SELECT tmp.caseExpertID, tmp.documentID, CASE WHEN COUNT(DISTINCT ft.fileType) = @numFileTypes THEN 1 ELSE 0 END
	FROM #tmpCaseExpertDocs AS tmp
	INNER JOIN trialsmith.dbo.depoDocumentsFilesOnline AS fo ON fo.documentID = tmp.documentID
	INNER JOIN @tmpFileTypes AS ft ON ft.fileType = fo.fileType
	GROUP BY tmp.caseExpertID, tmp.documentID;

	INSERT INTO #tmpCaseExpertAllDocsOnline (caseExpertID)
	SELECT DISTINCT caseExpertID
	FROM #tmpCaseExpertDocsOnline
	WHERE isOnline = 1
		EXCEPT
	SELECT DISTINCT caseExpertID
	FROM #tmpCaseExpertDocsOnline
	WHERE isOnline = 0;


	-- mark newly completed files as ready
	update d set dateReady = getdate()
	FROM #tmpCaseExpertDocsOnline tmp 
	INNER JOIN trialsmith.dbo.depomemberdataCaseExpertDocuments AS d 
		ON d.documentID = tmp.documentID
		and d.caseExpertID = tmp.caseExpertID
		and tmp.isOnline = 1 
		and d.dateReady is null


	-- mark as readyToProcess
	UPDATE qi
	SET qi.statusID = @statusReady,
		qi.dateUpdated = GETDATE()
	FROM dbo.queue_chatDocumentPrep as qi
	INNER JOIN #tmpCaseExpertAllDocsOnline AS tmp ON tmp.caseExpertID = qi.caseExpertID
	WHERE qi.statusID = @statusWaiting;

	IF OBJECT_ID('tempdb..#tmpCaseExpertDocs') IS NOT NULL 
		DROP TABLE #tmpCaseExpertDocs;
	IF OBJECT_ID('tempdb..#tmpCaseExpertDocsOnline') IS NOT NULL 
		DROP TABLE #tmpCaseExpertDocsOnline;
	IF OBJECT_ID('tempdb..#tmpCaseExpertAllDocsOnline') IS NOT NULL 
		DROP TABLE #tmpCaseExpertAllDocsOnline;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

use trialsmith;
GO

ALTER PROC dbo.ts_updateDepoDocumentIndexInfo
    @documentID int,
    @transcript_date varchar(25),
    @case_citation varchar(255),
    @jurisdiction varchar(50),
    @expert_firstname varchar(500),
    @expert_middlename varchar(50),
    @expert_lastname varchar(500),
    @expert_full_name varchar(500)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
    DECLARE @valid_date DATE;
    DECLARE @valid_jurisdiction varchar(2);

    SET @valid_date = TRY_CAST(@transcript_date AS DATE);
    SET @valid_jurisdiction = CASE
        WHEN LEN(@jurisdiction) > 2 THEN
            (SELECT top 1 Code FROM dbo.states WHERE Name = @jurisdiction order by orderPref)
        WHEN LEN(@jurisdiction) = 2 THEN
            (SELECT top 1 Code FROM dbo.states WHERE Code = UPPER(@jurisdiction) order by orderPref)
        ELSE NULL
    END;

    UPDATE dbo.depoDocuments
    SET
        ExpertName = @expert_full_name,
        fname = NULLIF(@expert_firstname,''),
        mname = NULLIF(@expert_middlename,''),
        lname = NULLIF(@expert_lastname,''),
        STYLE = @case_citation,
        Jurisdiction =  @valid_jurisdiction,
        DateLastmodified = getdate(),
        DocumentDate = @valid_date
    WHERE DocumentID = @documentID
    AND DocumentTypeID = 1
    AND STYLE IS NULL
    AND fname IS NULL
    AND lname IS NULL;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
