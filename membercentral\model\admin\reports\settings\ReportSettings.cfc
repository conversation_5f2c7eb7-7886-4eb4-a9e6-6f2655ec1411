<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// build quick links ------------------------------------------------------------------------ ::
			this.link.edit = buildCurrentLink(arguments.event,"edit");
			this.link.message = buildCurrentLink(arguments.event,"message");
			this.link.saveSettings = buildCurrentLink(arguments.event,"saveSettings");

			local.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);
			this.reportImageExt = xmlSearch(local.appInstanceSettings.settingsXML,'string(/settings/setting[@name="reportHeaderImage"]/@value)');
			
			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strReportFieldSetsSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getMultipleFieldSetSelector(
			selectorID="fsSelectorRptSettings",
			getFieldSetDataFunc="getAvailableAndSelectedFieldSetsForRptSettings",
			addFieldSetUsageFunc="createMemberFieldUsageForRptSettings",
			removeFieldSetUsageFunc="removeMemberFieldUsage",
			showViewUsageActionMode="usagebyreportsonly",
			fieldLabel="Report Settings Field Sets")>

		<cfset local.conditionWord = 'Report Condition' >
		<cfset local.conditionsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=virtualGroupsJSON&meth=getConditions&dtMode=reports&mode=stream">
		<cfset local.editConditionUID = buildLinkToTool(toolType='VirtualGroup',mca_ta='editConditionUID') & "&mode=direct">
		<cfset local.conditionID = arguments.event.getValue('conditionID','0')>
		<cfset local.arrConditionAreas = createObject("component","model.admin.virtualGroups.virtualGroups").getConditionAreasForDropdowns(conditionType="Reports",mc_siteInfo=arguments.event.getValue('mc_siteInfo'))>
 	
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_reportSettings.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<!--- handle image deletion --->
		<cfif Len(arguments.event.getValue('deleteImage','')) and fileExists("#application.paths.RAIDUserAssetRoot.path#common/reports/#arguments.event.getValue('mc_siteinfo.siteid')#.#this.reportImageExt#")>
			<cffile action="delete" file="#application.paths.RAIDUserAssetRoot.path#common/reports/#arguments.event.getValue('mc_siteinfo.siteid')#.#this.reportImageExt#">
		</cfif>

		<!--- handle image uploading --->
		<cfif len(arguments.event.getValue('reportImage',""))>
			<cfif fileExists("#application.paths.RAIDUserAssetRoot.path#common/reports/#arguments.event.getValue('mc_siteinfo.siteid')#.#this.reportImageExt#")>
				<cffile action="delete" file="#application.paths.RAIDUserAssetRoot.path#common/reports/#arguments.event.getValue('mc_siteinfo.siteid')#.#this.reportImageExt#">
			</cfif>

			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.destination = "#application.paths.RAIDUserAssetRoot.path#common/reports/">
			<cffile action="upload" filefield="reportImage" destination="#local.strFolder.folderPath#" nameConflict="makeUnique" result="local.upload">
			
			<cfif listFindNoCase("png,gif",local.upload.ServerFileExt)>
				<cfscript>
				local.imageOK = true;
				try {
					local.origPhoto = "#local.upload.ServerDirectory#/#local.upload.ServerFile#";
					local.origPhotoInfo = application.objCommon.thumborImageInfo(filePath=local.origPhoto);

					if (local.origPhotoInfo.imageInfo.source.width is not 275 OR local.origPhotoInfo.imageInfo.source.height is not 75) {
						local.imageOK = false;
					}
				}
				catch( any excpt ){
					local.imageOK = false;
					application.objError.sendError(cfcatch=excpt);
				}
				</cfscript>

				<cfif local.imageOK>
					<cffile action="move" destination="#local.destination##arguments.event.getValue('mc_siteinfo.siteid')#.#local.upload.ServerFileExt#" source="#local.upload.ServerDirectory#/#local.upload.ServerFile#">
					<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
						update ai
						set settingsXML.modify('replace value of (/settings/setting[@name=''reportHeaderImage'']/@value)[1] with ''#local.upload.ServerFileExt#''')
						from dbo.cms_applicationInstances as ai
						inner join dbo.sites as s on s.siteID = ai.siteID and s.siteID = #arguments.event.getValue('mc_siteInfo.siteID')#
						where ai.applicationInstanceName = 'admin'
					</cfquery>
				</cfif>
			<cfelse>
				<cfset local.imageOK = false>
			</cfif>

			<cfif NOT local.imageOK and FileExists("#local.upload.ServerDirectory#/#local.upload.ServerFile#")>
				<cffile action="delete" file="#local.upload.ServerDirectory#/#local.upload.ServerFile#">
				<cflocation url="#this.link.edit#&err=1" addtoken="no">
			</cfif>
		</cfif>
		
		<cflocation url="#this.link.edit#" addtoken="no">
	</cffunction>

	<cffunction name="fsRemove" access="public" output="false" returntype="struct">
		<cfargument name="useID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "errmsg":"" }>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getUseSiteResourceID">
			SELECT u.useSiteResourceID, msf.uid
			FROM dbo.ams_memberFieldUsage as u
			INNER JOIN dbo.ams_memberFieldSets as msf on msf.fieldSetID = u.fieldsetID
			WHERE u.useID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.useID#">
		</cfquery>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.fsRemove">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					
					DECLARE @useID int, @useSiteResourceID int;
					SET @useID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.useID#">;
					SET @useSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.getUseSiteResourceID.useSiteResourceID#">;

					IF EXISTS (SELECT 1 FROM dbo.rpt_savedReports
								WHERE otherXML.exist('/report/fieldsets/fieldset[@uid="#ucase(local.getUseSiteResourceID.uid)#"]') = 1
								OR otherXML.exist('/report/linkedfieldsets/fieldset[@uid="#ucase(local.getUseSiteResourceID.uid)#"]') = 1)
						RAISERROR('Field Set in use.',16,1);

					BEGIN TRAN;
						IF @useSiteResourceID IS NOT NULL
							UPDATE dbo.cms_siteResources
							SET siteResourceStatusID = 3
							WHERE siteResourceID = @useSiteResourceID;

						DELETE FROM	dbo.ams_memberFieldUsage
						WHERE useID = @useID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field Set in use", cfcatch.detail)>
				<cfset local.returnStruct.errmsg = "That Field Set is in use by at least one report and cannot be removed from Report Settings.">
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfif>
		</cfcatch>
		</cftry>

		<cfset local.returnStruct.success = len(local.returnStruct.errmsg) EQ 0>
		
		<cfreturn local.returnStruct>
	</cffunction>
 
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('msg')>
					<p>
						<cfswitch expression="#arguments.event.getValue('msg')#">
							<cfcase value="1"><b>You do not have rights to this page.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>