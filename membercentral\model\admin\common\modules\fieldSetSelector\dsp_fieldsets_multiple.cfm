<cfsavecontent variable="local.fsSelectorJS">
	<cfoutput>
	<script language="javascript">
		<cfif arguments.hasPermissionAction>
			var #ToScript(local.permsGotoLink,"mca_perms_link_#arguments.selectorID#")#
		</cfif>
		function loadFieldSetGrids_#arguments.selectorID#() {
			let loadFSResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					let availFSListSource = $('##mc_AvailFSList_#arguments.selectorID#').html();
					let availFSListTemplate = Handlebars.compile(availFSListSource);
					$('##availFSGridContainer_#arguments.selectorID#').html(availFSListTemplate(r));
					mcActivateTooltip($('##availFSGridContainer_#arguments.selectorID#'));

					let incFSListSource = $('##mc_incFSList_#arguments.selectorID#').html();
					let incFSListTemplate = Handlebars.compile(incFSListSource);
					$('##incFSGridContainer_#arguments.selectorID#').html(incFSListTemplate(r));
					mcActivateTooltip($('##incFSGridContainer_#arguments.selectorID#'));
					
					$('##fsGridContainer_#arguments.selectorID# .selFSCount').text(r.arrselectedfieldsets.length);
					$('##fsGridContainer_#arguments.selectorID# .availFSCount').text(r.availablefieldsetscount);
					$('##fsGridContainer_#arguments.selectorID# .fsCountLoadingSpan').toggleClass('d-none', true);
					$('##fsGridContainer_#arguments.selectorID# .fsCountSpan').toggleClass('d-none', false);
				} else {
					let reloadHTML = '<div class="text-center mt-5"><span class="d-block text-danger mb-2">Sorry, we were unable to load the data.</span><i class="fa-solid fa-rotate-right fa-2x cursor-pointer" onclick="loadFieldSetGrids_#arguments.selectorID#()"></i><span class="d-block">Reload</span></div>';
					$('##availFSGridContainer_#arguments.selectorID#, ##incFSGridContainer_#arguments.selectorID#').html(reloadHTML);
				}
			};

			$('##incFSGridContainer_#arguments.selectorID#, ##availFSGridContainer_#arguments.selectorID#').html(mca_getLoadingHTML());
			$('##fsGridContainer_#arguments.selectorID# .fsCountSpan').toggleClass('d-none', true);
			$('##fsGridContainer_#arguments.selectorID# .fsCountLoadingSpan').toggleClass('d-none', false);

			#arguments.getFieldSetDataFunc#(loadFSResult);
		}

		<cfif not local.useInlinePreview>
			function editFieldSet_#arguments.selectorID#(fsID) {
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: fsID > 0 ? 'Edit Field Set' : 'Create a Field Set',
					iframe: true,
					contenturl: '#local.editFieldSetLink#&fsID='+fsID,
					strmodalfooter: {
						classlist: 'text-right',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttononclickhandler: 'saveMemFieldSet_#arguments.selectorID#',
						extrabuttonlabel: 'Save'
					}
				});
			}
			function saveMemFieldSet_#arguments.selectorID#() {
				$('##MCModalBodyIframe')[0].contentWindow.validateAndSaveFieldSet();
			}
			function previewFieldSet_#arguments.selectorID#(fsID) {
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					title: 'Preview Field Set',
					contenturl: '#local.previewFieldSetLink#&fsID='+fsID,
					strmodalfooter: {
						showclose: true
					}
				});
			}
			function viewFieldSetUsages_#arguments.selectorID#(fsID, fsName) {
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					title: 'Usages of ' + fsName,
					contenturl: '#local.viewFieldSetUsagesLink#&fsID='+fsID,
					strmodalfooter: {
						showclose: true
					}
				});
			}
			<cfif arguments.hasPermissionAction>
				function doPermsForFieldSetUsage_#arguments.selectorID#(srID, resourceName){
					mca_showPermissions(srID,resourceName,loadFieldSetGrids_#arguments.selectorID#,null,null,mca_perms_link_#arguments.selectorID#);
				}
			</cfif>
		<cfelse>
			function editFieldSet_#arguments.selectorID#(fsID, fscreated) {
				$('###arguments.inlinePreviewSectionID#').addClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview').removeClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview .headerLabel').html(fsID > 0 ? 'Edit Field Set' : 'Create a Field Set');
				$('###arguments.inlinePreviewSectionID#_fsPreview .divFSFormContainer').html(mca_getLoadingHTML()).load('#local.editFieldSetLink#&fsID='+fsID + '&isModalContent=0' + (fscreated ? '&fscreated=1' : ''));
			}
			function previewFieldSet_#arguments.selectorID#(fsID) {
				$('###arguments.inlinePreviewSectionID#').addClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview').removeClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview .headerLabel').html('Preview Field Set');
				$('###arguments.inlinePreviewSectionID#_fsPreview .divFSFormContainer').html(mca_getLoadingHTML()).load('#local.previewFieldSetLink#&fsID='+fsID);
			}
			function viewFieldSetUsages_#arguments.selectorID#(fsID, fsName) {
				$('###arguments.inlinePreviewSectionID#').addClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview').removeClass('d-none');
				$('###arguments.inlinePreviewSectionID#_fsPreview .headerLabel').html('Usages of ' + fsName);
				$('###arguments.inlinePreviewSectionID#_fsPreview .divFSFormContainer').html(mca_getLoadingHTML()).load('#local.viewFieldSetUsagesLink#&fsID='+fsID);
			}
			function cancelFSPreviewForm_#arguments.selectorID#(){
				var doReload = $('##reloadOnCancel#arguments.selectorID#').val() || 0;
				$('###arguments.inlinePreviewSectionID#_fsPreview .divFSFormContainer').html('');
				$('###arguments.inlinePreviewSectionID#_fsPreview').addClass('d-none');
				$('###arguments.inlinePreviewSectionID#').removeClass('d-none');
				if(doReload) loadFieldSetGrids_#arguments.selectorID#();
			}
			<cfif arguments.hasPermissionAction>
				function doPermsForFieldSetUsage_#arguments.selectorID#(srID, resourceName){
					var thelink = mca_perms_link_#arguments.selectorID# + '&cmsRID=' + srID + '&resourceName=' + resourceName + '&displayMode=inline';
					$('###arguments.inlinePreviewSectionID#').addClass('d-none');
					$('###arguments.inlinePreviewSectionID#_fsPreview').removeClass('d-none');
					$('###arguments.inlinePreviewSectionID#_fsPreview .headerLabel').html('Edit Permissions');

					var iframeHTML = '<input type="hidden" id="reloadOnCancel#arguments.selectorID#" value="1">'
						+ '<div id="permissionIframeLoading#arguments.selectorID#" class="mt-4 text-center"><div class="spinner-border"></div><div class="font-weight-bold mt-2">Loading..</div></div>'
						+ '<iframe id="permissionIframe#arguments.selectorID#" class="d-none" width="100%" height="100%" frameborder="0" scrolling="no" src="' + thelink + '" onload="onLoadIframe_#arguments.selectorID#(this);" style="min-height: 230px;"></iframe>';

					$('###arguments.inlinePreviewSectionID#_fsPreview .divFSFormContainer').html(iframeHTML);
				}
				function onLoadIframe_#arguments.selectorID#(obj) {
					$('##permissionIframeLoading#arguments.selectorID#').addClass('d-none');
					$('##permissionIframe#arguments.selectorID#').removeClass('d-none');
					obj.style.height = obj.contentWindow.document.body.scrollHeight + 100 + 'px';
				}
			</cfif>
		</cfif>
		
		function addFieldSetUsage_#arguments.selectorID#(fsID, fsUID) {
			let addFSUsageResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					loadFieldSetGrids_#arguments.selectorID#();
				} else { 
					$('##btnAddFS_#arguments.selectorID#_'+fsID).removeClass('disabled');
					showFSErrorMessage_#arguments.selectorID#('Unable to Add Field Set', 'We were unable to add this Member Field Set.');
				}
			};
			$('##btnAddFS_#arguments.selectorID#_'+fsID).addClass('disabled');
			#arguments.addFieldSetUsageFunc#(fsID, addFSUsageResult, fsUID);
		}
		function removeFieldSetUsage_#arguments.selectorID#(useID, fsID, fsUID) {
			let removeFSUsageResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					loadFieldSetGrids_#arguments.selectorID#();
				} else {
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					let errMsg = r.errmsg ? r.errmsg : 'We were unable to remove this Member Field Set.';
					showFSErrorMessage_#arguments.selectorID#('Unable to Remove Field Set', errMsg);
				}
			};

			let delBtn = $('##removeFSBtn_#arguments.selectorID#_'+fsID);
			mca_initConfirmButton(delBtn, function(){
				#arguments.removeFieldSetUsageFunc#(useID, removeFSUsageResult, fsUID);
			});
		}
		<cfif arguments.hasOrderingAction>
			function moveFieldSetUsageRow_#arguments.selectorID#(useID, dir){
				let moveRowResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						loadFieldSetGrids_#arguments.selectorID#();
					}
				};
				#arguments.orderFieldSetFunc#(useID, dir, moveRowResult);
			}
		</cfif>
		function showFSErrorMessage_#arguments.selectorID#(title, msg){
			MCModalUtils.showModal({
				isslideout: false,
				iframe: false,
				size: 'md',
				title: title,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				strmodalbody: { 
					content: msg
				},
				strmodalfooter: {
					showclose: true
				}
			});
		}
		function addFieldSetUsageExt_#arguments.selectorID#(fsID, onCompleteFunc, fsUID) {
			#arguments.addFieldSetUsageFunc#(fsID, onCompleteFunc, fsUID);
		}

		$(function() {
			loadFieldSetGrids_#arguments.selectorID#();
			<cfif local.useInlinePreview>
				if($('###arguments.inlinePreviewSectionID#_fsPreview').length == 0){
					$('###arguments.inlinePreviewSectionID#').after($('##mc_FSPreviewContainer_#arguments.selectorID#').html());
				}
			</cfif>
		});
	</script>
	<style>
		##availFSAccordion_#arguments.selectorID# > .card { box-shadow: none; }
		##fsGridContainer_#arguments.selectorID# .btn-xs.btnPreviewFS { padding-right: 0.3rem; padding-left: 0.3rem; }
		##fsGridContainer_#arguments.selectorID# .btn-xs.btnEditFS { padding-right: 0.32rem; padding-left: 0.32rem; }
		##fsGridContainer_#arguments.selectorID# .btn-xs.btnAddFS { padding-right: 0.4rem; padding-left: 0.4rem; }
		<cfif len(arguments.fieldLabel)>
		##availFSAccordion_#arguments.selectorID# ##heading_#arguments.selectorID#.card-header .field-header-label {
			position: absolute;
			top: 0.25rem;
			left: .5rem;
			font-size: 0.75rem;
			color: ##6c757d;
			padding: 0 0.25rem;
			pointer-events: none; /* don't block clicks */
			z-index: 2;
		}
		##availFSAccordion_#arguments.selectorID# ##heading_#arguments.selectorID#.card-header span.field-header-label + .btn-link span {
			margin-top: .8rem;
		}
		</cfif>
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.fsSelectorJS)#">

<cfoutput>
<div class="row no-gutters" id="fsGridContainer_#arguments.selectorID#">
	<div class="col-12">
		<div class="card card-box">
			<div class="card-header bg-light">
				<div class="card-header--title font-weight-bold font-size-sm">
					Selected Field Sets
				</div>
				<span class="fsCountSpan small d-none"><span class="selFSCount pr-1"></span>selected</span>
				<span class="fsCountLoadingSpan small">loading..</span>
			</div>
			<div class="card-body p-0">
				<div id="incFSGridContainer_#arguments.selectorID#" style="height:#arguments.selectedFSGridHeight#px;overflow-y:auto;">
				</div>
				<div class="accordion" id="availFSAccordion_#arguments.selectorID#">
					<div class="card card-box rounded-bottom">
						<div class="card-header bg-light rounded-0" id="heading_#arguments.selectorID#">
							<cfif len(arguments.fieldLabel)>
								<span class="field-header-label">#arguments.fieldLabel#</span>
							</cfif>
							<button class="btn btn-link d-flex align-items-center justify-content-between font-size-sm collapsed" type="button" data-toggle="collapse" data-target="##collapse_#arguments.selectorID#" aria-expanded="false" aria-controls="collapse_#arguments.selectorID#">
								<span class="fsCountSpan d-none"><span class="availFSCount pr-1"></span>Additional Field Sets Available</span>
								<span class="fsCountLoadingSpan">loading..</span>
								<span>
									<i class="fa-solid fa-caret-up font-size-xl"></i>
								</span>
							</button>
						</div>
						<div id="collapse_#arguments.selectorID#" class="collapse" aria-labelledby="heading_#arguments.selectorID#" data-parent="##availFSAccordion_#arguments.selectorID#" style="">
							<div class="card">
								<cfif arguments.hasEditRights>
									<div class="card-header py-2">
										<div class="card-header--title font-size-xs text-grey">
											Choose from the following Field Sets:
										</div>
										<div class="card-header--actions">
											<a href="##" name="btnCreateFieldSet" onclick="editFieldSet_#arguments.selectorID#(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create a Field Set">
												<i class="fa-regular fa-circle-plus fa-lg"></i>
											</a>
										</div>
									</div>
								</cfif>
								<div class="card-body bg-secondary p-2" id="availFSGridContainer_#arguments.selectorID#" style="height:250px;overflow-y:auto;"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script id="mc_incFSList_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##if arrselectedfieldsets}}
		<table class="table table-sm table-borderless table-striped mb-0">
		{{##each arrselectedfieldsets}}
			<tr class="border-bottom">
				<cfset local.actionColWidthPct = 25>
				<cfif arguments.hasOrderingAction>
					<cfset local.actionColWidthPct += 8>
				</cfif>
				<cfif arguments.hasPermissionAction>
					<cfset local.actionColWidthPct += 4>
				</cfif>
				<cfif len(arguments.showViewUsageActionMode)>
					<cfset local.actionColWidthPct += 4>
				</cfif>
				<cfset local.nameColWidthPct = 100 - local.actionColWidthPct>

				<td width="#local.nameColWidthPct#%" class="pl-2 font-size-sm">{{fieldsetname}}<td>
				<td width="#local.actionColWidthPct#%" class="text-right">
					<cfif arguments.hasEditRights AND arguments.hasOrderingAction>
						<span class="d-inline-block">
							<a href="##" id="moveUpFSBtn_#arguments.selectorID#_{{fieldsetid}}"  
								{{##compare @index '!=' 0}}
									class="btn btn-xs btn-outline-dark"
									onclick="$(this).tooltip('hide');moveFieldSetUsageRow_#arguments.selectorID#({{useid}},'up');return false;"
									data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Field Set Up"
								{{/compare}}
								{{##compare @index '==' 0}}
									class="btn btn-xs btn-outline-dark invisible"
								{{/compare}}
								>
								<i class="fa-solid fa-up"></i>
							</a>
							<a href="##" id="moveDownFSBtn_#arguments.selectorID#_{{fieldsetid}}" 
								{{##compare (math @index "+" 1) '!=' ../arrselectedfieldsets.length}}
									class="btn btn-xs btn-outline-dark"
									onclick="$(this).tooltip('hide');moveFieldSetUsageRow_#arguments.selectorID#({{useid}},'down');return false;" 
									data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Field Set Down"
								{{/compare}}
								{{##compare (math @index "+" 1) '==' ../arrselectedfieldsets.length}}
									class="btn btn-xs btn-outline-dark invisible"
								{{/compare}}
								>
								<i class="fa-solid fa-down"></i>
							</a>
						</span>
					</cfif>
					<cfif arguments.hasEditRights AND arguments.hasPermissionAction>
						<span class="d-inline-block">
							<a href="##" onclick="$(this).tooltip('hide');doPermsForFieldSetUsage_#arguments.selectorID#({{usesiteresourceid}},'{{fieldsetnameenc}}');return false;" class="btn btn-xs btn-outline-warning" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit Permissions">
								{{##if hasrights}}
									<i class="fa-solid fa-lock-keyhole"></i>
								{{else}}
									<i class="fa-solid fa-unlock-keyhole"></i>
								{{/if}}
							</a>
						</span>
					</cfif>
					<span class="d-inline-block">
						<cfif arguments.hasEditRights>
							<a href="##" onclick="$(this).tooltip('hide');editFieldSet_#arguments.selectorID#({{fieldsetid}});return false;" class="btn btn-xs btn-outline-primary btnEditFS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit this Field Set">
								<i class="fa-solid fa-pencil"></i>
							</a>
						</cfif>
						<a href="##" onclick="$(this).tooltip('hide');previewFieldSet_#arguments.selectorID#({{fieldsetid}});return false;" class="btn btn-xs btn-outline-info btnPreviewFS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Preview this Field Set">
							<i class="fa-solid fa-eye"></i>
						</a>
					</span>
					<cfif len(arguments.showViewUsageActionMode)>
						<span class="d-inline-block">
							<a href="##" onclick="$(this).tooltip('hide');viewFieldSetUsages_#arguments.selectorID#({{fieldsetid}},'{{fieldsetnameenc}}');return false;" class="btn btn-xs btn-outline-dark btnViewUsagesFS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="View Usages for Field Set">
								<i class="fas fa-map-marker-alt"></i>
							</a>
						</span>
					</cfif>
					<cfif arguments.hasEditRights>
						<span class="d-inline-block">
							<a id="removeFSBtn_#arguments.selectorID#_{{fieldsetid}}"
								{{##if hasremoveusagerights}}
									href="##" title="Remove this Field Set From Selections"
									onclick="$(this).tooltip('hide');removeFieldSetUsage_#arguments.selectorID#({{useid}}, {{fieldsetid}}, '{{fieldsetuid}}');return false;"
									class="btn btn-xs btn-outline-danger" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-confirm="0"
								{{else}}
									href="javascript:void(0);" class="btn btn-xs btn-outline-secondary text-muted"
								{{/if}}
								>
								<i class="fa-solid fa-trash-can"></i>
							</a>
						</span>
					</cfif>
				</td>
			</tr>
		{{/each}}
		</table>
	{{else}}
		<div class="text-center py-3">No Field Sets Selected.</div>
	{{/if}}
</script>
<script id="mc_AvailFSList_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##if arravailablefieldsets}}
		{{##each arravailablefieldsets}}
			<div class="bg-secondary mb-3">
				<div><i class="fa-regular fa-tag font-size-md text-warning mr-2"></i><b class="font-size-sm">{{categoryname}}</b></div>
				<ul class="list-group mt-2">
					{{##each arrfieldsets}}
						<li class="list-group-item py-1">
							<div class="row no-gutters align-items-center">
								<div class="col font-size-sm">{{fieldsetname}}</div>
								<div class="col-auto pl-2">
									<cfif arguments.hasEditRights>
										<a href="##" id="btnAddFS_#arguments.selectorID#_{{fieldsetid}}" onclick="$(this).tooltip('hide');addFieldSetUsage_#arguments.selectorID#({{fieldsetid}}, '{{fieldsetuid}}');return false;" class="btn btn-xs btn-outline-success btnAddFS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Add this Field Set ">
											<i class="fa-solid fa-plus"></i>
										</a>
										<a href="##" onclick="$(this).tooltip('hide');editFieldSet_#arguments.selectorID#({{fieldsetid}});return false;" class="btn btn-xs btn-outline-primary btnEditFS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit this Field Set">
											<i class="fa-solid fa-pencil"></i>
										</a>
									</cfif>
									<a href="##" onclick="$(this).tooltip('hide');previewFieldSet_#arguments.selectorID#({{fieldsetid}});return false;" class="btn btn-xs btn-outline-info btnPreviewFS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Preview this Field Set">
										<i class="fa-solid fa-eye"></i>
									</a>
								</div>
							</div>
						</li>
					{{/each}}
				</ul>
			</div>
		{{/each}}
	{{else}}
		<div class="text-center py-3">No Field Sets Available.</div>
	{{/if}}
</script>
<cfif local.useInlinePreview>
	<script id="mc_FSPreviewContainer_#arguments.selectorID#" type="text/html">
		<div id="#arguments.inlinePreviewSectionID#_fsPreview" class="p-3 d-none">
			<div class="mb-3"><a href="##" onclick="return cancelFSPreviewForm_#arguments.selectorID#();"><i class="fa-solid fa-chevrons-left"></i> Return to Field Set Selection</a></div>
			<div class="card card-box">
				<div class="card-header py-2 bg-light">
					<div class="card-header--title font-size-lg headerLabel"></div>
				</div>
				<div class="card-body p-2 divFSFormContainer"></div>
			</div>
		</div>
	</script>
</cfif>
</cfoutput>