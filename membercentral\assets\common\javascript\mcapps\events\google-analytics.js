function triggerEventsViewItemList(pageName, events, listName) {
	if (Array.isArray(events) && events.length > 0) {
		var formattedData = events.map(event => {
			var item = {
				item_id: event.eventID,
				item_name: event.title,
			};
			var categories = event.categories || [];
			categories.slice(0, 5).forEach((category, index) => {
				if (index === 0) {
					item["item_category"] = category.categoryName;
				} else {
					item[`item_category${index + 1}`] = category.categoryName;
				}
			});
			return item;
		});
		gtag("event", "view_item_list", {
			item_list_id: pageName,
			item_list_name: listName,
			items: formattedData
		});
	}
}

function triggerEventViewItem(events) {
	if (Array.isArray(events) && events.length > 0) {
		var formattedData = events.map(event => {
			var item = {
				item_id: event.eventID,
				item_name: event.title,
			};
			var categories = event.categories || [];
			categories.slice(0, 5).forEach((category, index) => {
				if (index === 0) {
					item["item_category"] = category.categoryName;
				} else {
					item[`item_category${index + 1}`] = category.categoryName;
				}
			});
			return item;
		});
		gtag("event", "view_item", {
			currency: "USD",
			value: 0,
			items: formattedData
		});
	}
}

function triggerEventFileDownload(eventID, eventTitle, docTitle, fileExt) {
	gtag("event", "file_download", {
		item_id: eventID,
		item_name: eventTitle,
		file_extension: fileExt,
		file_name: docTitle
	});
}

function triggerEventsCart(events, totalAmount, typeName) {
	var tagName = "";
	if (typeName == "view"){
		tagName = "view_cart";
	} else if (typeName == "add"){
		tagName = "add_to_cart";
	} else if (typeName == "remove"){
		tagName = "remove_from_cart";
	}
	if (Array.isArray(events) && events.length > 0 && tagName.length) {
		var formattedData = events.map(event => {
			var item = {
				item_id: event.eventID,
				item_name: event.title,
				coupon: event.coupon,
				discount: event.discount,
				price: event.price
			};
			var categories = event.categories || [];
			categories.slice(0, 5).forEach((category, index) => {
				if (index === 0) {
					item["item_category"] = category.categoryName;
				} else {
					item[`item_category${index + 1}`] = category.categoryName;
				}
			});
			return item;
		});

		var adjustedValue = totalAmount;
		if (typeName === "remove") {
			adjustedValue = 0;
			events.forEach(event => {
				adjustedValue += event.price - event.discount;
			});
		}
		
		gtag("event", tagName, {
			currency: "USD",
			value: adjustedValue,
			items: formattedData
		});
	}
}

function triggerPurchaseEvent(eventPurchase) {
	if (typeof eventPurchase == 'object') {		
		gtag("event", "purchase", eventPurchase);
	}
}

function triggerAddToMyCalendar(eventID, eventTitle, calendarType){
	gtag("event", "calendar_subscribe", {
		item_id: eventID,
		item_name: eventTitle,
		service_name: calendarType
	});
}

function triggerSubscribeToMyCalendar(pageName, calendarType){
	gtag("event", "calendar_subscribe", {
		cal_id: pageName,
		service_name: calendarType
	});
}

