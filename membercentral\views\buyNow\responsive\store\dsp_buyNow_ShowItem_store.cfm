<cfquery name="local.qryItemCount" dbtype="query">
	select sum(quantity) as qty
	from [local].strItem.qryItems
</cfquery>
<cfoutput>
<div class="buyNow-card buyNow-mb-3">
	<div class="buyNow-card-header buyNow-bg-whitesmoke buyNow-pb-1">
		<div class="buyNow-font-size-lg buyNow-font-weight-bold">Order Summary</div>
	</div>
	<div class="buyNow-card-body buyNow-pb-2">
		<table class="buyNow-table buyNow-table-condensed buyNow-table-noborder">
			<tr class="buyNow-border-bottom">
				<th>Item <span class="buyNow-ml-2">(<a href="/?pg=store&sa=view">View Order</a>)</span></th>
				<th>Qty</th>
				<th>Item Total</th>
			</tr>
			<cfloop query="local.strItem.qryItems">
				<tr>
					<td>#local.strItem.qryItems.contentTitle# <span class="hidden-phone">(#local.strItem.qryItems.formatName#)</span></td>
					<td>#local.strItem.qryItems.quantity#</td>
					<td>
						<cfif val(local.strItem.qryItems.itemDiscountExcTax) gt 0>
							<span class="buyNow-strike">#DollarFormat(local.strItem.qryItems.quantity * val(local.strItem.qryItems.rate))##local.defaultCurrencyType#</span>
							&nbsp;
							<span class="buyNow-highlight">#DollarFormat(local.strItem.qryItems.quantity * val(local.strItem.qryItems.rate) - local.strItem.qryItems.itemDiscountExcTax)##local.defaultCurrencyType#</span>
						<cfelse>
							#DollarFormat(local.strItem.qryItems.quantity * val(local.strItem.qryItems.rate))##local.defaultCurrencyType#
						</cfif>
					</td>
				</tr>
			</cfloop>
		</table>
		<cfif isArray(local.strItem.arrOverSoldItems) AND arrayLen(local.strItem.arrOverSoldItems)>
			<div class="alert">
				<b>Note:</b> This cart was updated based on product availability.
				<div>
					<b>Message:</b>
					<hr style="margin:0;">
					<cfloop array="#local.strItem.arrOverSoldItems#" index="local.msg">
						#local.msg#<br/>
					</cfloop>
				</div>
			</div>
		</cfif>
		<div class="orderTotalInfo">
			<cfif local.strItem.qryItemsTotal.totalShipping gt 0>
				<p><strong>Shipping Total</strong> (#local.strItem.qryItemsTotal.shippingMethod#): #DollarFormat(local.strItem.qryItemsTotal.totalShipping)##local.defaultCurrencyType#</p>
			</cfif>
			<cfif structKeyExists(session,"strStoreTax") AND NOT structKeyExists(session.strStoreTax,local.strItem.ItemID)>
				<p><strong>Taxes</strong>: TBD</p>
			<cfelseif val(local.strItem.qryItemsTotal.totalTax) gt 0>
				<p><strong>Taxes</strong>: 
				<cfif val(local.strItem.qryItemsTotal.totalDiscount) gt 0>
					<cfset local.discountTax = local.strItem.qryItemsTotal.totalDiscount - local.strItem.qryItemsTotal.totalDiscountExcTax>
					<span class="buyNow-strike">#DollarFormat(local.strItem.qryItemsTotal.totalTax)##local.defaultCurrencyType#</span>
					&nbsp;
					<span class="buyNow-highlight">#DollarFormat(local.strItem.qryItemsTotal.totalTax - local.discountTax)##local.defaultCurrencyType#</span>
				<cfelse>
					#DollarFormat(local.strItem.qryItemsTotal.totalTax)##local.defaultCurrencyType#
				</cfif>
				</p>
			</cfif>
			<cfif NOT local.strItem.showShippingArea and val(local.strItem.qryItemsTotal.totalDiscount) gt 0>
				<p>
					<strong>Order Total</strong>:
					<span class="buyNow-strike">#dollarFormat(local.strItem.qryItemsTotal.grandTotal)##local.defaultCurrencyType#</span>
					&nbsp;
					<span class="buyNow-highlight">#DollarFormat(local.strItem.qryItemsTotal.actualTotal)##local.defaultCurrencyType#</span>
					<br/>
					<p><button type="button" name="btnRemoveCoupon" id="btnRemoveCoupon" class="buyNow-btn buyNow-btn-small buyNow-btn-warning" onclick="removeAppliedCoupon();">Remove Promo Code</button> #DollarFormat(local.strItem.qryItemsTotal.totalDiscount)##local.defaultCurrencyType# discount applied</p>
				</p>
				<p class="buyNow-alert-success buyNow-font-weight-bold buyNow-mb-2">#local.strItem.qryItemsTotal.redeemDetail#</p>
			<cfelse>
				<p>
					<strong>Order Total</strong>: #dollarFormat(local.strItem.qryItemsTotal.actualTotal)##local.defaultCurrencyType#
				</p>
			</cfif>
		</div>

		<cfif local.strItem.showPaymentArea and local.strItem.offerCoupon and NOT local.strItem.showShippingArea and val(local.strItem.qryItemsTotal.totalDiscount) eq 0>
			<div class="buyNow-mt-3 buyNow-text-right buyNow-mb-3">
				<div class="buyNow-input-append">
					<input type="text" name="couponCode" id="couponCode" class="buyNow-formcontrol" value="" size="18" value="" placeholder="Promo Code" maxlength="15" onkeypress="validateCouponCodeOnEnterKey(event);">
					<button type="button" name="btnApplyCouponCode" id="btnApplyCouponCode" class="buyNow-btn buyNow-add-on buyNow-font-size-md buyNow-unrounded" onclick="validateCouponCode();">Apply</button>
				</div>
				<div id="couponCodeResponse" class="buyNow-alert buyNow-p-1 buyNow-mt-2 buyNow-font-size-sm buyNow-mb-0 buyNow-text-center" style="display:none;"></div>
			</div>
		</cfif>
	</div>
</div>
</cfoutput>
<cfinclude template="../dsp_buyNow_eCommercePolicy.cfm">