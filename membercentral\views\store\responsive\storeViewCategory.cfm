<cfset local.strData = attributes.data>
<cfset local.productList = []>
<cfif StructKeyExists(local.strData,"qryProductsInCategory") AND local.strData.qryProductsInCategory.recordCount >
	<cfloop query="local.strData.qryProductsInCategory">
		<cfset arrayAppend(local.productList, {
			"productID": local.strData.qryProductsInCategory.productID,
			"productName": local.strData.qryProductsInCategory.contentTitle,
			"categoryName": local.strData.qryProductsInCategory.CategoryName
		})>
	</cfloop>
</cfif>
<!--- go right to the product if it's the only one in the category --->
<cfif local.strData.qryProductsInCategory.totalcount IS 1 and not arrayLen(local.strData.arrSubCategories)>
	<cflocation addtoken="no" url="#local.strData.mainurl#&sa=ViewDetails&ItemID=#local.strData.qryProductsInCategory.ItemID#&cat=#local.strData.cat#">
</cfif>

<style type="text/css">
	.rowcolor0, .rowcolor1 { border:2px solid #000; margin:5px 0px 5px 5px; padding:10px 0 10px 10px; min-height:35px; width:96%; }
</style>
<cfsavecontent variable="local.storeViewCategoryJS">
<cfoutput>
	<script language="javascript">
		$(function() {		
			<cfset local.productsJSON = SerializeJSON(local.productList)>
			<cfif IsJSON(local.productsJSON)>
				MCLoader.loadJS('/assets/common/javascript/mcapps/store/google-analytics.js#application.objCMS.getPlatformCacheBusterKey()#')
				.then( () => {
					try {
						triggerProductsViewItemList('#local.strData.pageName#',#local.productsJSON#, 'Store');
					} catch (error) {
						console.error("Error parsing JSON:", error.message);
					}
				});
			</cfif>
		});
	</script>	
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.storeViewCategoryJS)#">
<div class="container-fluid">
	<div class="row-fluid">
		<cfinclude template="topbar.cfm">
	</div>
	<div class="row-fuid">
		<div class="span3">
			<cfinclude template="categoryStart.cfm">
		</div>
		<div class="span9 container-fluid">
			<cfoutput>
				<div class="row-fluid">
					<div class="span12 well well-small">
						<span class="lead"><strong>Products in #local.strData.strCategory.CategoryName#</strong></span>
					</div>
				</div>
				<cfif not local.strData.qryProductsInCategory.recordCount>
					<div class="row-fluid">
						<div class="well well-small span12">
							<cfif arrayLen(local.strData.arrAllCategories)>
								<p>Please select a category with items on the left.</p>
							<cfelse>
								<p>This category doesn't have any products.</p>
							</cfif>
						</div>
					</div>
				<cfelse>
					<div class="row-fluid">
						<div class="well well-small span12">
							<p>Please select your product below. <cfif arrayLen(local.strData.arrSubCategories)>You may also select from the list of categories with items on the left.</cfif></p>
						</div>
					</div>

					<cfloop query="local.strData.qryProductsInCategory">
						<div class="row-fluid">
							<div class="well well-small span12">
								<div style="margin-bottom:10px;">
									<span class="lead">
										#htmleditformat(local.strData.qryProductsInCategory.contenttitle)#
									</span>
								</div>
								<div style="margin-bottom:10px;">
									<button class="btn btn-success btn-small" onclick="document.location.href='#local.strData.mainurl#&sa=ViewDetails&ItemID=#local.strData.qryProductsInCategory.itemID#&cat=#local.strData.cat#';">View Product</button>
								</div>
								<cfif LEN(local.strData.qryProductsInCategory.categoryList)>
									<div class="mb"><strong>Also Appears In:</strong> #local.strData.qryProductsInCategory.categoryList#</div>
								</cfif>
								<cfif local.strData.storeInfo.ShowProductID>
									<div style="margin-bottom:10px;"><strong>Product ID:</strong> #local.strData.qryProductsInCategory.productID#</div>
								</cfif>
								<div style="margin-bottom:10px;">#REReplaceNoCase(local.strData.qryProductsInCategory.rawcontent,"<[^>]*>"," ","ALL")#<cfif len(local.strData.qryProductsInCategory.rawcontent) gt 200>...</cfif>
								</div>
							</div>
						</div>	
					</cfloop>
				
					<cfif local.strData.start is not 1>
						<cfif local.strData.start GTE local.strData.disp>
							<cfset local.prev = local.strData.disp>
							<cfset local.prevrec = local.strData.start - local.strData.disp>
						<cfelse>
							<cfset local.prev = local.strData.start - 1>
							<cfset local.prevrec = 1>
						</cfif>
						<cfset local.prevLink = "#local.strData.mainurl#&sa=ViewCategory&cat=#local.strData.strCategory.CategoryID#&start=#local.prevrec#" />
					<cfelse>
						<cfset local.prevLink = ""/>
					</cfif>
					<cfif local.strData.end LT local.strData.qryProductsInCategory.totalcount>
						<cfif local.strData.start + (local.strData.disp * 2) GTE local.strData.qryProductsInCategory.totalcount>
							<cfset local.next = local.strData.qryProductsInCategory.totalcount - local.strData.start - local.strData.disp + 1>
						<cfelse>
							<cfset local.next = local.strData.disp>
						</cfif>
						<cfset local.nextLink = "#local.strData.mainurl#&sa=ViewCategory&cat=#local.strData.strCategory.CategoryID#&start=#local.strData.start + local.strData.disp#"/>
					<cfelse>
						<cfset local.nextLink = ""/>
					</cfif>
					<cfif len(local.nextLink) or len(local.prevLink)>
						<cfset local.PageCount = 1>

						<div class="pagination pagination-centered">
						  <ul>
						  	<cfif len(local.prevLink)>
						    	<li><a href="#local.prevLink#">Previous</a></li>
						    </cfif>
						    <cfloop index="local.PageStartRow" from="1" to="#local.strData.qryProductsInCategory.totalcount#" step="#local.strData.disp#">
						    	<li <cfif local.strData.start eq local.PageStartRow>class="active"</cfif>><a href="#local.strData.mainurl#&sa=ViewCategory&cat=#local.strData.strCategory.CategoryID#&start=#local.PageStartRow#">#local.pagecount#</a></li>
						    	<cfset local.PageCount = local.PageCount + 1>
						    </cfloop>
							<cfif len(local.nextLink)>
						    	<li><a href="#local.nextLink#">Next</a></li>
						    </cfif>
						  </ul>
						</div>

					</cfif>
				</cfif>
			</cfoutput>
		</div>
	</div>
</div>