<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = "controller">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// set rights into event
		local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
		arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

		// set links to different functions
		this.link.list = buildCurrentLink(arguments.event,"list");
		this.link.addProgram = buildCurrentLink(arguments.event,"addProgram");
		this.link.editProgram = buildCurrentLink(arguments.event,"editProgram");
		this.link.saveProgram = buildCurrentLink(arguments.event,"saveProgram");
		this.link.editProgramRate = buildCurrentLink(arguments.event,"editProgramRate") & "&mode=direct";
		this.link.saveProgramRate = buildCurrentLink(arguments.event,"saveProgramRate") & "&mode=stream";
		this.link.addProgramFrequency = buildCurrentLink(arguments.event,"addProgramFrequency") & "&mode=stream";
		this.link.editProgramDistribution = buildCurrentLink(arguments.event,"editProgramDistribution") & "&mode=direct";
		this.link.saveProgramDistribution = buildCurrentLink(arguments.event,"saveProgramDistribution") & "&mode=stream";
		this.link.editFrequency = buildCurrentLink(arguments.event,"editFrequency") & "&mode=direct";
		this.link.saveFrequency = buildCurrentLink(arguments.event,"saveFrequency") & "&mode=stream";
		this.link.listContributors = buildCurrentLink(arguments.event,"listContributors");
		this.link.editMember = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
		this.link.message = buildCurrentLink(arguments.event,"message");
		this.link.addContribution = buildCurrentLink(arguments.event,"addContribution");
		this.link.dtRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=contributionJSON&mode=stream";

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="listContributors" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.siteID = arguments.event.getValue('mc_siteinfo.siteid');
		local.objContributions = CreateObject('component','contributions');
		local.qryFrequencies = local.objContributions.getFrequencyOptions(siteID=local.siteID);
		local.qryCPStatuses = local.objContributions.getCPStatuses();
		local.qryProgramsForAdd = local.objContributions.getProgramsForAddBySite(siteID=local.siteID);
		local.qryProgramsForFilter = local.objContributions.getProgramsForFilterBySite(siteID=local.siteID);
	
		local.contributionListLink = "#this.link.dtRootLink#&meth=getContributionList&dtmode=manageContributions";
		local.contributionChangeLogsLink = "#this.link.dtRootLink#&meth=getContributionChangeLogList&dtmode=manageContributions";
		local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
		local.linkCCToContributorLink = buildCurrentLink(arguments.event,"linkCCToContributor") & "&mode=direct";
		local.viewContributionLink = buildCurrentLink(arguments.event,"viewContribution");
		local.massEmailContributionsLink = buildCurrentLink(arguments.event,"massEmailContributions") & "&mode=direct";
		local.exportContributionsPromptLink = buildCurrentLink(arguments.event,"exportContributionsPrompt") & "&mode=direct";
		local.cancelContributionLink = buildCurrentLink(arguments.event,"cancelContribution") & "&ret=list&mode=direct";
		local.showContribInstallmentDetailsLink = buildCurrentLink(arguments.event,"showContribInstallmentDetails") & "&mode=direct";
		local.addPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct";
		local.allocatePaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=allocatePayment&mode=direct";
		local.startExportContributionsChangeLogLink = buildCurrentLink(arguments.event, "startExportContributionsChangeLog") & "&mode=direct";
		local.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
		local.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
		local.deleteContributions = buildCurrentLink(arguments.event,"deleteContributions") & "&mode=direct";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_contributors.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = createObject("component", "contributions");

		local.qryContributionAppInstances = local.objContributions.getContributionAppInstances(siteID=arguments.event.getValue('mc_siteinfo.siteid'));

		local.programListLink = "#this.link.dtRootLink#&meth=getProgramList";
		local.programFrequencyListLink = "#this.link.dtRootLink#&meth=getProgramFrequencyList";
		
		local.arrGridData = [];
		local.arrGridData[1] = { 
			title="Cross-Program Contribution Fields", 
			intro="Fields defined here will be presented to each contributor on each program's intake form.",
			btnClassList="btn btn-sm btn-primary",
			gridext="#this.siteResourceID#_1",
			initGridOnLoad=false,
			controllingSRID=this.siteResourceID, 
			resourceType='ContributionAdmin', 
			areaName='Role' 
		};
		local.strCrossProgramFieldsGrid = createObject("component","model.admin.common.modules.customFields.customFields").getGridHTML(arrGridData=local.arrGridData);

		local.strETData = {
			siteID=arguments.event.getValue('mc_siteinfo.siteid'),
			treeCode='ETCONTRIBUTIONS',
			title="Contribution Email Templates", 
			intro="Here you manage email templates used by contributions.",
			gridext="#this.siteResourceID#_1",
			gridwidth=690,
			initGridOnLoad=false
		};
		local.strEmailTemplatesGrid = createObject("component","model.admin.emailTemplates.emailTemplateAdmin").manageEmailTemplates(strETData=local.strETData);
		local.listContribution = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=contributionJSON&meth=getContributionAppInstanceList&mode=stream";
		local.appTypeID = application.objApplications.getApplicationTypeIDFromName('Contributions');
		local.addpageLink = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='PageAdmin',mca_ta='addPage');
		local.canAddInstance = CreateObject("component","model.admin.pages.appCreationProcess").canAddAppInstance(siteID=arguments.event.getValue('mc_siteInfo.siteID'), applicationTypeID=local.appTypeID);
		local.editContributionAppInstanceLink = buildCurrentLink(arguments.event,"editContributionAppInstance") & "&mode=direct";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_contributionsSetup.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editContributionAppInstance" access="public" output="false" returnType="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objContributions = createObject("component", "contributions")>
		<cfset local.aID = arguments.event.getValue('aID',0)>
		<cfset local.updateContributionAppInstanceLink = buildCurrentLink(arguments.event,"updateContributionAppInstance") & "&mode=direct">
		<cfset local.qryContributionAppInstance = local.objContributions.getApplicationInstance(siteID=arguments.event.getValue('mc_siteinfo.siteid'), appInstanceID=local.aID)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_contributionAppInstanceEdit.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateContributionAppInstance" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('aID',0) gt 0 and len(trim(arguments.event.getValue('contributionAppInstanceName','')))> 
			<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
				UPDATE dbo.cms_applicationInstances 
				SET applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('contributionAppInstanceName')#">, 
					applicationInstanceDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('contributionAppInstanceName')#">
				WHERE siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
				AND applicationInstanceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('aID')#">;
			</cfquery>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadPageTable();
				top.reloadPage();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.permsGotoLink = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
		local.objContributions = createObject("component", "contributions");
		local.formLink = this.link.saveProgram;
		local.qryAppInstance = local.objContributions.getApplicationInstance(siteID=arguments.event.getValue('mc_siteinfo.siteid'), appInstanceID=arguments.event.getValue('aID',0));
		local.mhSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'));
		local.qryNoteCategories = CreateObject('component','model.admin.memberHistory.memberHistory').getParentChildCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
									siteResourceID=local.mhSRID, checkPermission='AddEntry');

		// param values
		arguments.event.setValue('programID',0);
		arguments.event.setValue('appInstanceID',val(local.qryAppInstance.applicationInstanceID));
		arguments.event.setValue('appInstanceName',local.qryAppInstance.applicationInstanceName);
		arguments.event.setValue('siteResourceID',0);
		arguments.event.setValue('programName','');
		arguments.event.setValue('intakeConfirmContentID',0);
		arguments.event.setValue('intakeConfirmContent','');
		arguments.event.setValue('intakeEmailFrom','');
		arguments.event.setValue('intakeEmailStaff','');
		arguments.event.setValue('allowAnonymous',0);
		arguments.event.setValue('anonymousContentID',0);
		arguments.event.setValue('anonymousContent','');
		arguments.event.setValue('noteCategoryID',0);

		appendBreadCrumbs(arguments.event,{ link='', text="New Program" });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_contributionProgram.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editProgram" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = createObject("component", "contributions");
		local.siteID = arguments.event.getValue('mc_siteinfo.siteid');

		local.permsGotoLink = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
		local.formLink = this.link.saveProgram;
		local.programID = arguments.event.getValue('pID',0);
		local.qryContributionProgram = local.objContributions.getProgramDetails(siteID=local.siteID, programID=local.programID);
		local.isProgramWithRate = local.objContributions.isProgramWithRate(programID=local.programID);
		local.qryDistribSettings = local.objContributions.getDistribSettings(programID=local.programID);
		local.qryFrequencies = local.objContributions.getFrequencyOptions(siteID=local.siteID);
		local.qryCPStatuses = local.objContributions.getCPStatuses();
		local.qryAllAFs = local.objContributions.getAdvanceFormula(siteID=local.siteID);
		local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
		local.linkCCToContributorLink = buildCurrentLink(arguments.event,"linkCCToContributor") & "&mode=direct";
		local.changeProgramInstanceLink = buildCurrentLink(arguments.event,"changeProgramInstance") & "&pid=#local.programID#&mode=direct";
		local.viewContributionLink = buildCurrentLink(arguments.event,"viewContribution");
		local.massEmailContributionsLink = buildCurrentLink(arguments.event,"massEmailContributions") & "&operationMode=programTab&mode=direct";
		local.exportContributionsPromptLink = buildCurrentLink(arguments.event,"exportContributionsPrompt") & "&mode=direct";
		local.cancelContributionLink = buildCurrentLink(arguments.event,"cancelContribution") & "&ret=cp&mode=direct";
		local.showContribInstallmentDetailsLink = buildCurrentLink(arguments.event,"showContribInstallmentDetails") & "&mode=direct";
		local.importContributorsPromptLink = buildCurrentLink(arguments.event,"importContributorsPrompt") & "&pid=#local.programID#&mode=direct";
		local.addPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct";
		local.allocatePaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=allocatePayment&mode=direct";
		local.editCampaignLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=contributions&mca_ajaxfunc=editCampaign&programID=#local.programID#&siteID=#local.siteID#&mode=stream";
		local.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
		local.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
		local.mhSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='HistoryAdmin', siteID=local.siteID);
		local.qryNoteCategories = CreateObject('component','model.admin.memberHistory.memberHistory').getParentChildCategories(siteID=local.siteID, siteResourceID=local.mhSRID, checkPermission='AddEntry');
		local.deleteContributions = buildCurrentLink(arguments.event,"deleteContributions") & "&mode=direct";

		local.programRateListLink = '#this.link.dtRootLink#&meth=getProgramRates&pID=#local.programID#';
		local.programDistribListLink = '#this.link.dtRootLink#&meth=getProgramDistributions&pID=#local.programID#';
		local.programFreqListLink = '#this.link.dtRootLink#&meth=getProgramFrequencies&pID=#local.programID#';
		local.campaignListLink = "#this.link.dtRootLink#&meth=getCampaignList&pID=#local.programID#";
		local.contributionListLink = "#this.link.dtRootLink#&meth=getContributionList&dtmode=programContribTab&fProgram=#local.programID#";
		local.contributionChangeLogsLink = "#this.link.dtRootLink#&meth=getContributionChangeLogList&dtmode=programContribTab&fProgram2=#local.programID#";
		local.startExportContributionsChangeLogLink = buildCurrentLink(arguments.event, "startExportContributionsChangeLog") & "&mode=direct";

		arguments.event.setValue('programID',local.programID);
		arguments.event.setValue('appInstanceID',local.qryContributionProgram.applicationInstanceID);
		arguments.event.setValue('appInstanceName',local.qryContributionProgram.applicationInstanceName);
		arguments.event.setValue('siteResourceID',local.qryContributionProgram.siteResourceID);
		arguments.event.setValue('programName',local.qryContributionProgram.programName);
		arguments.event.setValue('programStatus',local.qryContributionProgram.siteResourceStatusDesc);
		arguments.event.setValue('programUID',local.qryContributionProgram.uid);
		arguments.event.setValue('allowAnonymous',local.qryContributionProgram.allowAnonymous);
		arguments.event.setValue('anonymousContentID',local.qryContributionProgram.anonymousContentID);
		arguments.event.setValue('anonymousContent',local.qryContributionProgram.anonymousContent);
		arguments.event.setValue('noteCategoryID',local.qryContributionProgram.noteCategoryID);
		arguments.event.setValue('invoiceGenerateDays',local.qryContributionProgram.invoiceGenerateDays);
		arguments.event.setValue('distribSettingID',local.qryContributionProgram.distribSettingID);
		arguments.event.setValue('addProgramFrequencyURL',this.link.addProgramFrequency);
		arguments.event.setValue('intakeConfirmContentID',local.qryContributionProgram.intakeConfirmContentID);
		arguments.event.setValue('intakeConfirmContent',local.qryContributionProgram.programConfirmContent);
		arguments.event.setValue('intakeEmailFrom',local.qryContributionProgram.intakeEmailFrom);
		arguments.event.setValue('intakeEmailStaff',local.qryContributionProgram.intakeEmailStaff);
		arguments.event.setValue('isProgramEnabled',local.qryContributionProgram.isProgramEnabled);
		arguments.event.setValue('nextPaymentDate',local.qryContributionProgram.nextPaymentDate);
		arguments.event.setValue('nextPaymentDateAFID',local.qryContributionProgram.nextPaymentDateAFID);
		arguments.event.setValue('nextAdvancementDate',local.qryContributionProgram.nextAdvancementDate);
		arguments.event.setValue('nextAdvancementDateAFID',local.qryContributionProgram.nextAdvancementDateAFID);
		arguments.event.setValue('payThruDate',local.qryContributionProgram.payThruDate);
		arguments.event.setValue('payThruDateAFID',local.qryContributionProgram.payThruDateAFID);
		arguments.event.setValue('advanceAFDate',local.qryContributionProgram.advanceAFDate);
		arguments.event.setValue('advanceAFDateAFID',local.qryContributionProgram.advanceAFDateAFID);
		arguments.event.setValue('fieldsSectionTitle',local.qryContributionProgram.fieldsSectionTitle);
		arguments.event.setValue('defaultGLAccountID',val(local.qryContributionProgram.defaultGLAccountID));
		local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryContributionProgram.defaultGLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
		arguments.event.setValue('defaultGLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded);

		// Prepare GL Account widget data for Default Revenue GL
		local.strDefaultGLWidgetData = {
			label="Default Revenue GL for Contribution Fields *",
			btnTxt="Choose GL Account",
			glatid=3,
			widgetMode='GLSelector',
			idFldName="defaultGLAccountID",
			idFldValue=val(arguments.event.getValue('defaultGLAccountID',0)),
			pathFldValue=arguments.event.getValue('defaultGLAccountPath',''),
			pathNoneTxt="(No account selected)"
		};
		local.strDefaultGLWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strDefaultGLWidgetData);

		local.arrGridData = [];
		local.arrGridData[1] = {
			detailID=local.programID,
			gridext="#local.qryContributionProgram.siteResourceID#_1",
			initGridOnLoad=false,
			controllingSRID=local.qryContributionProgram.siteResourceID,
			resourceType='ContributionProgram',
			areaName='Contribution'
		};
		local.strProgramFieldsGrid = createObject("component","model.admin.common.modules.customFields.customFields").getGridHTML(arrGridData=local.arrGridData);

		appendBreadCrumbs(arguments.event,{ link='', text="Edit Program" });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_contributionProgram.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveProgram" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = createObject("component", "contributions");
		local.programID = arguments.event.getValue('programID',0);

		if (len(arguments.event.getValue('intakeEmailFrom','')) and not isValid("regex",arguments.event.getValue('intakeEmailFrom',''),application.regEx.email)) {
			arguments.event.setValue('intakeEmailFrom','<EMAIL>');
 		}

		if (len(arguments.event.getValue('intakeEmailStaff',''))) {
			local.stNotify = replace(replace(arguments.event.getValue('intakeEmailStaff'),',',';','ALL'),' ','','ALL');
			local.notifyEmailArr = listToArray(local.stNotify,';');
			for (local.i=1; local.i lte arrayLen(local.notifyEmailArr); local.i++) {
				if (len(local.notifyEmailArr[local.i]) and not isValid("regex",local.notifyEmailArr[local.i],application.regEx.email)) {
					arrayDeleteAt(local.notifyEmailArr,local.i);
				}
			}
			arguments.event.setValue('intakeEmailStaff',arrayToList(local.notifyEmailArr,'; '));
		}
		</cfscript>

		<cfif arguments.event.getValue('allowAnonymous',0) is 0>
			<cfset arguments.event.setValue('anonymousContent','')>
		</cfif>

		<cfif local.programID gt 0>
			<cfset local.objContributions.updateProgramInfo(siteID=arguments.event.getValue('mc_siteinfo.siteid'), programID=local.programID, 
				programName=arguments.event.getTrimValue('programName',''), uid=arguments.event.getTrimValue('programUID',''), 
				intakeConfirmContent=arguments.event.getValue('intakeConfirmContent',''), intakeEmailFrom=arguments.event.getValue('intakeEmailFrom',''), 
				intakeEmailStaff=arguments.event.getValue('intakeEmailStaff',''), allowAnonymous=arguments.event.getValue('allowAnonymous',0), 
				anonymousContent=arguments.event.getTrimValue('anonymousContent',''), status=arguments.event.getValue('programStatus',''), 
				noteCategoryID=arguments.event.getValue('noteCategoryID',0), enteredByMemberID=session.cfcuser.memberdata.memberID)>
		<cfelse>
			<cfset local.programID = local.objContributions.insertProgramDetails( siteID=arguments.event.getValue('mc_siteInfo.siteID'), 
				appInstanceID=arguments.event.getTrimValue('appInstanceID',0), programName=arguments.event.getTrimValue('programName',''), 
				intakeConfirmContent=arguments.event.getValue('intakeConfirmContent',''), intakeEmailFrom=arguments.event.getValue('intakeEmailFrom',''), 
				intakeEmailStaff=arguments.event.getValue('intakeEmailStaff',''), allowAnonymous=arguments.event.getTrimValue('allowAnonymous',0), 
				anonymousContent=arguments.event.getTrimValue('anonymousContent',''), noteCategoryID=arguments.event.getValue('noteCategoryID',0), 
				enteredByMemberID=session.cfcuser.memberdata.memberID)>
		</cfif>

		<cflocation url="#this.link.editProgram#&pID=#local.programID#" addtoken="no">
	</cffunction>

	<cffunction name="editProgramRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = createObject("component", "contributions");
		local.formLink = this.link.saveProgramRate;
		local.rateID = arguments.event.getValue('rID',0);
		local.programID = arguments.event.getValue('pID',0);
		local.qryProgramRate = local.objContributions.getProgramRate(rateID=local.rateID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_programRate.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveProgramRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = createObject("component", "contributions");

		if (arguments.event.getValue('rateID',0) gt 0)
			local.objContributions.updateProgramRate(rateID=arguments.event.getValue('rateID'), programID=arguments.event.getValue('programID',0),
				rateName=arguments.event.getValue('rateName',''), uid=arguments.event.getTrimValue('rateUID',''));
		else
			local.objContributions.insertProgramRate(programID=arguments.event.getValue('programID',0), rateName=arguments.event.getValue('rateName',''));
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadRateGrid();
				top.loadIntakeFormStatus();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addProgramFrequency" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryCPFrequency = CreateObject("component","contributions").getCPFrequency(siteID=arguments.event.getValue('mc_siteInfo.siteID'), programID=arguments.event.getValue('pid',0))>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div class="form-inline mt-2">
				<div class="form-group">
					<label for="pmtFrequency">Select a frequency to add to this program: </label>
					<select name="pmtFrequency" id="pmtFrequency" class="form-control form-control-sm mx-2">
						<option value=""></option>
						<cfloop query="local.qryCPFrequency">
							<option value="#local.qryCPFrequency.frequencyID#">#local.qryCPFrequency.frequency#</option>
						</cfloop>
					</select>
					<button type="button" name="btnSaveProgramFreq" class="btn btn-sm btn-secondary" onclick="saveProgramPmtFrequency();">Add</button>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editProgramDistribution" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objContributions = createObject("component", "contributions");
			local.formLink = this.link.saveProgramDistribution;
			local.distribID = arguments.event.getValue('dID',0);
			local.programID = arguments.event.getValue('pID',0);
			local.qryProgramDistribution = local.objContributions.getProgramDistribution(distribID=local.distribID, programID=local.programID);

			arguments.event.setValue('distDesc',local.qryProgramDistribution.distDesc);
			arguments.event.setValue('distPct',local.qryProgramDistribution.distPct);
			arguments.event.setValue('glAccountID',val(local.qryProgramDistribution.glAccountID));
			arguments.event.setValue('distCode',local.qryProgramDistribution.distCode);
			arguments.event.setValue('distUID',local.qryProgramDistribution.uid);
			local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryProgramDistribution.glAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			arguments.event.setValue('GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded);

			// Prepare GL Account widget data for Revenue GL Override
			local.strGLAcctWidgetData = {
				label="Revenue GL",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=val(arguments.event.getValue('GLAccountID',0)),
				pathFldValue=arguments.event.getValue('GLAccountPath',''),
				pathNoneTxt="(No account selected)"
			};
			local.strGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strGLAcctWidgetData);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_programDistribution.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveProgramDistribution" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = createObject("component", "contributions");
		local.programID = arguments.event.getValue('programID',0);

		if (arguments.event.getValue('distribID',0) gt 0) {
			local.objContributions.updateProgramDistribution(distribID=arguments.event.getValue('distribID'), programID=local.programID,
				distDesc=arguments.event.getTrimValue('distDesc',''), glAccountID=val(arguments.event.getValue('glAccountID',0)), 
				distPct=val(arguments.event.getValue('distPct',0)), distCode=arguments.event.getTrimValue('distCode',''),
				uid=arguments.event.getTrimValue('distUID',''));
		} else {
			local.distribID = local.objContributions.insertProgramDistribution(programID=local.programID, distDesc=arguments.event.getTrimValue('distDesc',''), 
				glAccountID=val(arguments.event.getValue('glAccountID',0)), distPct=val(arguments.event.getValue('distPct',0)), 
				distCode=arguments.event.getTrimValue('distCode',''));
		}
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadDistGrid();
				top.loadIntakeFormStatus();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editFrequency" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objContributions = createObject("component", "contributions");
			local.frequencyID = arguments.event.getValue('fID',0);
			local.qryFrequency = local.objContributions.getFrequency(siteID=arguments.event.getValue('mc_siteInfo.siteid'), frequencyID=local.frequencyID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_frequency.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveFrequency" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = createObject("component", "contributions");

		local.isRecurring = arguments.event.getTrimValue('isRecurring');
		local.numMonths = arguments.event.getTrimValue('numMonths');
		if (local.isRecurring is 1 and int(val(local.numMonths)) is 0) {
			local.isRecurring = 0;
			local.numMonths = 0;
		} else if (local.isRecurring is 0 and int(val(local.numMonths)) gt 0) {
			local.numMonths = 0;
		}

		if (arguments.event.getValue('frequencyID',0) gt 0) {
			local.objContributions.updateFrequency(siteID=arguments.event.getValue('mc_siteInfo.siteid'), frequencyID=arguments.event.getValue('frequencyID'),
				frequency=arguments.event.getTrimValue('frequency'), isRecurring=local.isRecurring, numMonths=local.numMonths, 
				uid=arguments.event.getTrimValue('freqUID',''));
		} else {
			local.objContributions.insertFrequency(siteID=arguments.event.getValue('mc_siteInfo.siteid'), frequency=arguments.event.getTrimValue('frequency',''),
				isRecurring=local.isRecurring, numMonths=local.numMonths);
		}
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.closeBox();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewContribution" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objContributions = CreateObject("component","model.contributions.contributions")>
		<cfset local.objAdminContributions = CreateObject("component","model.admin.contributions.contributions")>
		<cfset local.contributionID = arguments.event.getValue('cid',0)>
		<cfset local.qryContributionInfo = local.objContributions.getContributionInfo(contributionID=local.contributionID)>
		<cfset local.maxInstallmentDate = local.objAdminContributions.getMaxInstallmentDate(contributionID=local.contributionID)>
		
		<cfset local.strEmailConfirmation = local.objContributions.generateConfirmationEmail(contributionID=local.qryContributionInfo.contributionID, emailMode="staffprint")>
		<cfset local.addResourceNoteLink = "/?mode=stream&pg=admin&mca_ajaxlib=resourceNotes&mca_ajaxfunc=addResourceNote&rn_csrid=#local.qryContributionInfo.programSiteResourceID#&rn_rt=contributionProgram&rn_ut=contributionNote&rn_mid=#local.qryContributionInfo.memberID#&rn_id=#local.qryContributionInfo.contributionID#&rn_cid=#local.qryContributionInfo.noteCategoryID#&rn_vw=bs4&rn_descOnly=1&rn_ret=reloadContribNotes&rn_cancel=hideContribNotesForm">

		<cfset local.scheduleDatesJSONLink = "#this.link.dtRootLink#&meth=getContributionInstallmentDetails&cid=#local.contributionID#">
		<cfset local.cancelInstallScheduleLink = buildCurrentLink(arguments.event,"cancelInstallSchedule") & "&mode=direct">

		<cfset appendBreadCrumbs(arguments.event,{ link='', text="View Contribution" })>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_viewContribution.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEmailContributions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.strFilters = structNew()>
		<cfset structInsert(local.strFilters, 'memberID', arguments.event.getValue('memberID',0))>
		<cfset structInsert(local.strFilters, 'fProgram', arguments.event.getValue('fProgram',''))>
		<cfset structInsert(local.strFilters, 'fCampaign', arguments.event.getValue('fCampaign',''))>
		<cfset structInsert(local.strFilters, 'fFrequency', arguments.event.getValue('fFrequency',''))>
		<cfset structInsert(local.strFilters, 'fStatus', arguments.event.getValue('fStatus',''))>
		<cfset structInsert(local.strFilters, 'fRate', arguments.event.getValue('fRate',''))>
		<cfset structInsert(local.strFilters, 'fStartDateFrom', arguments.event.getValue('fStartDateFrom',''))>
		<cfset structInsert(local.strFilters, 'fStartDateTo', arguments.event.getValue('fStartDateTo',0))>
		<cfset structInsert(local.strFilters, 'fEndDateFrom', arguments.event.getValue('fEndDateFrom',0))>
		<cfset structInsert(local.strFilters, 'fEndDateTo', arguments.event.getValue('fEndDateTo',''))>
		<cfset structInsert(local.strFilters, 'fHasCardOnFile', arguments.event.getValue('fHasCardOnFile',''))>
		<cfset structInsert(local.strFilters, 'fLastModifiedFrom', arguments.event.getValue('fLastModifiedFrom',''))>
		<cfset structInsert(local.strFilters, 'fLastModifiedTo', arguments.event.getValue('fLastModifiedTo',''))>
		<cfset structInsert(local.strFilters, 'associatedMemberID', arguments.event.getValue('associatedMemberID',0))>
		<cfset structInsert(local.strFilters, 'associatedGroupID', arguments.event.getValue('associatedGroupID',0))>
		<cfset structInsert(local.strFilters, 'linkedRecords', arguments.event.getValue('linkedRecords',''))>
		<cfset structInsert(local.strFilters, 'operationMode', arguments.event.getValue('operationMode',''))>
		<cfset structInsert(local.strFilters, 'chkAll', arguments.event.getValue('chkAll',0))>
		<cfset structInsert(local.strFilters, 'contributionIDList', arguments.event.getValue('contributionIDList',''))>
		<cfset structInsert(local.strFilters, 'notContributionIDList', arguments.event.getValue('notContributionIDList',''))>

		<cfset local.strResourceTitle = { resourceTitle='Contributions', resourceTitleDesc='', templateEditorLabel='Compose your message.' }>
		
		<cfset local.argumentCollection = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), resourceType='Contributions',
				recipientType='Contributors', strResourceTitle=local.strResourceTitle, strFilters=local.strFilters, arrRecipientModes=arrayNew(1),
				mergeCodeInstructionsLink="#buildCurrentLink(arguments.event,'showMergeCodeInstructions')#&mode=stream",
				emailTemplateTreeCode="ETCONTRIBUTIONS" }>

		<cfset local.data = CreateObject("component","model.admin.common.modules.massEmails.massEmails").prepMassEmails(argumentCollection=local.argumentCollection)>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportContributionsPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.exportContributionsLink = buildCurrentLink(arguments.event,"exportContributions") & "&memberID=#arguments.event.getValue('memberID',0)#&mode=stream">

		<cfset local.strFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="mc_cp_fsid", allowBlankOption=false, inlinePreviewSectionID="divExportFormContainer")>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_exportContributions.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportContributions" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.success = false>
		<cfset local.data = "The export file could not be generated. Contact MemberCentral for assistance.">

		<cfset local.memberID = arguments.event.getValue('memberID',0)>
		<cfset local.programIDList = arguments.event.getValue('fProgram','')>
		<cfset local.campaignIDList = arguments.event.getValue('fCampaign','')>
		<cfset local.frequencyIDList = arguments.event.getValue('fFrequency','')>
		<cfset local.statusIDList = arguments.event.getValue('fStatus','')>
		<cfset local.rateIDList = arguments.event.getValue('fRate','')>
		<cfset local.fStartDateFrom = arguments.event.getValue('fStartDateFrom','')>
		<cfset local.fStartDateTo = arguments.event.getValue('fStartDateTo','')>
		<cfset local.fEndDateFrom = arguments.event.getValue('fEndDateFrom','')>
		<cfset local.fEndDateTo = arguments.event.getValue('fEndDateTo','')>
		<cfset local.fHasCardOnFile = arguments.event.getValue('fHasCard','')>

		<cfif len(local.fStartDateFrom)>
			<cfset local.fStartDateFrom = DateFormat(local.fStartDateFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fStartDateTo)>
			<cfset local.fStartDateTo = DateFormat(local.fStartDateTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>
		<cfif len(local.fEndDateFrom)>
			<cfset local.fEndDateFrom = DateFormat(local.fEndDateFrom, "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.fEndDateTo)>
			<cfset local.fEndDateTo = DateFormat(local.fEndDateTo, "mm/dd/yyyy") & " 23:59:59.997">
		</cfif>
		<cfset local.chkAll = arguments.event.getValue('chkAll',0)>
		<cfset local.contributionIDList = arguments.event.getValue('contributionIDList','')>
		<cfset local.notContributionIDList = arguments.event.getValue('notContributionIDList','')>
		
		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "Contributions.csv">
		<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>

		<cftry>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExportContributions">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpContributions') IS NOT NULL
					DROP TABLE ##tmpContributions;
				IF OBJECT_ID('tempdb..##tmpProgramDistCodes') IS NOT NULL
					DROP TABLE ##tmpProgramDistCodes;
				IF OBJECT_ID('tempdb..##tmpContributionsPledged') IS NOT NULL
					DROP TABLE ##tmpContributionsPledged;
				IF OBJECT_ID('tempdb..##tmpPrepContributions') IS NOT NULL
					DROP TABLE ##tmpPrepContributions;
				IF OBJECT_ID('tempdb..####tmpContributionsFunds#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpContributionsFunds#local.tmpSuffix#;
				IF OBJECT_ID('tempdb..##tmpContributionCustom') IS NOT NULL
					DROP TABLE ##tmpContributionCustom;
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
					DROP TABLE ##tmp_membersForFS;
				IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
					DROP TABLE ##tmp_CF_ItemIDs;
				IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
					DROP TABLE ##tmp_CF_FieldData;
				IF OBJECT_ID('tempdb..####tmpContributionCustom#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpContributionCustom#local.tmpSuffix#;
				IF OBJECT_ID('tempdb..####tmpFinalContributions#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpFinalContributions#local.tmpSuffix#;
				CREATE TABLE ##tmpContributions (contributionID int PRIMARY KEY, programID int);
				CREATE TABLE ##tmpProgramDistCodes (distribID int, distCode varchar(20));
				CREATE TABLE ##tmpContributionsPledged (contributionID int, totalPledgeFirst decimal(18,2), totalPledgeRecurring decimal(18,2), pledgedValue decimal(18,2));
				CREATE TABLE ##tmpPrepContributions ([ContributionID] int, [MCMemberID] int, [Contribution Program] varchar(200), [Campaign Name] varchar(400),
					[Contributor First Name] varchar(75), [Contributor Last Name] varchar(75), [Contributor MemberNumber] varchar(50),
					[Contributor Company] varchar(200), [Contribution Date] datetime, [Contribution Amount] decimal(18,2),
					[Frequency] varchar(20), [Start Date] date, [End Date] date, [Perpetual] char(3), [Status] varchar(30),
					[Number of Installments] int, [Rate Name] varchar(200), [Anonymous] char(3), [First Installment] decimal(18,2),
					[Recurring Installment] decimal(18,2), [Pledge Value] decimal(18,2));
				CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
				CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY);
				CREATE TABLE ##tmp_CF_ItemIDs (itemID int, itemType varchar(20));
				CREATE TABLE ##tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

				declare @siteID int, @orgID int, @fieldSetID int, @contributionIDList varchar(max), @dynSQL varchar(max), @fundList varchar(max),
					@crossProgramColList varchar(max), @duplicateDistCode varchar(20), @outputFieldsXML xml;
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				set @fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('fsid',0))#">;

				<cfif arguments.event.getValue('associatedMemberID',0) gt 0>
					DECLARE @memberID int;
					DECLARE @tblMembers as TABLE (memberID int PRIMARY KEY);

					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('associatedMemberID')#">;

					INSERT INTO @tblMembers
					SELECT @memberID as memberID
					FROM dbo.ams_members as m 
					WHERE memberID = @memberID
					
					<cfif arguments.event.getValue('linkedRecords','') is "all">
						UNION
						
						SELECT allchildMember.memberID
						FROM dbo.ams_members as m WITH(NOLOCK)
						INNER JOIN dbo.ams_recordRelationships as rr WITH(NOLOCK) ON rr.orgID = @orgID
							AND rr.masterMemberID = m.memberID
							AND rr.isActive = 1
						INNER JOIN dbo.ams_members as childMember WITH(NOLOCK) ON rr.childMemberID = childMember.memberID
						INNER JOIN dbo.ams_members as allchildMember WITH(NOLOCK) ON allchildMember.activeMemberID = childMember.memberID
						WHERE childMember.status <> 'D'
						AND m.memberID = @memberID
					</cfif>
				</cfif>

				INSERT INTO ##tmpContributions (contributionID, programID)
				select c.contributionID, c.programID
				from dbo.sites as s
				inner join dbo.cms_applicationInstances as ai on ai.siteID = s.siteID
				inner join dbo.cp_programs as p on p.applicationInstanceID = ai.applicationInstanceID
				inner join dbo.cp_contributions as c on c.programID = p.programID
				left outer join dbo.cp_campaigns as cpc on cpc.campaignID = c.campaignID
				inner join dbo.ams_members as m on m.memberID = c.memberID
				inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				<cfif arguments.event.getValue('associatedMemberID',0) gt 0>
					inner join @tblMembers as m2 on m2.memberID = mActive.memberID
				</cfif>
				<cfif arguments.event.getValue('associatedGroupID',0) gt 0>
					inner join dbo.cache_members_groups mg ON mg.orgID = @orgID
						and mg.memberID = mActive.memberID 
						and mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('associatedGroupID')#">
				</cfif>
				where s.siteID = @siteID
				<cfif local.chkAll is 0 and len(local.contributionIDList) and arrayLen(reMatch("[^0-9,]",local.contributionIDList)) is 0>
					and c.contributionID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.contributionIDList#" list="yes">)
				<cfelse>
					<cfif local.chkAll is 1 and len(local.notContributionIDList) and arrayLen(reMatch("[^0-9,]",local.notContributionIDList)) is 0>
						and c.contributionID not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.notContributionIDList#" list="yes">)
					</cfif>
					<cfif local.memberID gt 0>
						and m.activeMemberID = <cfqueryparam value="#local.memberID#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
					<cfif ListLen(local.programIDList)>
						and p.programID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.programIDList#" list="true">)
					</cfif>
					<cfif ListLen(local.campaignIDList)>
						and cpc.campaignID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.campaignIDList#" list="true">)
					</cfif>
					<cfif len(local.fStartDateFrom)>
						and c.startdate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fStartDateFrom#">
					</cfif>
					<cfif len(local.fStartDateTo)>
						and c.startdate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fStartDateTo#">
					</cfif>
					<cfif len(local.fEndDateFrom)>
						and c.enddate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.fEndDateFrom#">
					</cfif>
					<cfif len(local.fEndDateTo)>
						and c.enddate <= <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.fEndDateTo#">
					</cfif>
					<cfif ListLen(local.frequencyIDList)>
						and c.frequencyID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.frequencyIDList#" list="true">)
					</cfif>
					<cfif ListLen(local.statusIDList)>
						and c.statusID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.statusIDList#" list="true">)
					</cfif>
					<cfif ListLen(local.rateIDList)>
						and c.rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateIDList#" list="true">)
					</cfif>
					<cfif len(local.fHasCardOnFile)>
						<cfif local.fHasCardOnFile eq 'Y'>
							and exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
						<cfelseif local.fHasCardOnFile eq 'N'>
							and not exists (select 1 from dbo.cp_contributionPayProfiles where contributionID = c.contributionID)
						</cfif>
					</cfif>
				</cfif>;

				INSERT INTO ##tmpProgramDistCodes (distribID, distCode)
				select distinct cd.distribID, cd.distCode
				from ##tmpContributions as tmp
				inner join dbo.cp_distributions as cd on cd.programID = tmp.programID;

				select @contributionIDList = coalesce(@contributionIDList + ',','') + cast(contributionID as varchar(10)) from ##tmpContributions;
				INSERT INTO ##tmpContributionsPledged (contributionID, totalPledgeFirst, totalPledgeRecurring, pledgedValue)
				EXEC dbo.cp_getContributionAmountSplit @contributionIDList=@contributionIDList;

				INSERT INTO ##tmpPrepContributions ([ContributionID], [MCMemberID], [Contribution Program], [Campaign Name], [Contribution Date], [Contribution Amount],
					[Frequency], [Start Date], [End Date], [Number of Installments], [Contributor Last Name], [Contributor First Name],
					[Contributor MemberNumber], [Contributor Company], [Status], [Perpetual], [Anonymous], [Rate Name], [First Installment],
					[Recurring Installment], [Pledge Value])
				select tmp.contributionID, mActive.memberID, p.programName, ISNULL(cpc.campaignName,''), c.contribDate, c.installmentAmt, f.frequency, c.startdate, c.endDate, c.installmentCount,
					mActive.lastname, mActive.firstname, mActive.membernumber, mActive.company, cps.statusName,
					case when c.isPerpetual = 1 then 'Yes' else 'No' end, case when c.isAnonymous = 1 then 'Yes' else 'No' end,
					ISNULL(r.rateName,''), tmpPledged.totalPledgeFirst, tmpPledged.totalPledgeRecurring, tmpPledged.pledgedValue
				from ##tmpContributions as tmp
				inner join ##tmpContributionsPledged as tmpPledged on tmpPledged.contributionID = tmp.contributionID
				inner join dbo.cp_contributions as c on c.contributionID = tmp.contributionID
				left outer join dbo.cp_campaigns as cpc on cpc.campaignID = c.campaignID
				inner join dbo.cp_programs as p on p.programID = c.programID
				inner join dbo.cp_statuses as cps on cps.statusID = c.statusID
				inner join dbo.cp_frequencies as f on f.frequencyID = c.frequencyID
				left outer join dbo.cp_rates as r on r.rateID = c.rateID
				inner join dbo.ams_members as m on m.memberID = c.memberID
				inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID;

				-- get fieldset data
				INSERT INTO ##tmp_membersForFS (memberID)
				select distinct MCMemberID
				from ##tmpPrepContributions;

				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
					@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

				SELECT TOP 1 @duplicateDistCode = distCode FROM ##tmpProgramDistCodes GROUP BY distCode HAVING COUNT(*) > 1;
				IF @duplicateDistCode IS NOT NULL
					RAISERROR('The same distribution code ''%s'' was used in different programs.',16,1,@duplicateDistCode);

				select @fundList = coalesce(@fundList + ',','') + quoteName(distCode) from ##tmpProgramDistCodes;
				if len(@fundList) > 0 begin
					set @dynSQL = 'select contributionID as fcontributionID, ' + @fundList + '
						into ####tmpContributionsFunds#local.tmpSuffix#
						from (
							select tmp.contributionID, tmppdc.distCode, cd.distAmount
							from ##tmpContributions as tmp
							inner join dbo.cp_contributionDistributions as cd on cd.contributionID = tmp.contributionID
							inner join ##tmpProgramDistCodes as tmppdc on tmppdc.distribID = cd.distribID
						) as outerTmp
						PIVOT (sum(distAmount) FOR distCode in (' + @fundList + ')) as pvt;';
					EXEC(@dynSQL);
				end else
					select contributionID as fcontributionID
					into ####tmpContributionsFunds#local.tmpSuffix#
					from ##tmpContributions;

				-- cross program custom fields
				INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
				SELECT contributionID, 'ContributionRole'
				FROM ##tmpContributions;

				-- get fieldData
				EXEC dbo.cf_getFieldData;

				SELECT fd.itemID AS contributionID, replace(f.fieldReference,',','') as titleOnInvoice, fd.fieldValue AS answer
				INTO ##tmpContributionCustom
				FROM ##tmp_CF_FieldData AS fd
				INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID;

				-- contribution custom fields pivoted
				set @crossProgramColList = '';
				select @crossProgramColList = COALESCE(@crossProgramColList + ',', '') + quoteName(titleonInvoice)
				from ##tmpContributionCustom 
				group by titleOnInvoice;

				IF left(@crossProgramColList,1) = ','
					select @crossProgramColList = right(@crossProgramColList,len(@crossProgramColList)-1);

				IF len(@crossProgramColList) > 0 BEGIN
					set @dynSQL = '';
					select @dynSQL = '
						select * 
						into ####tmpContributionCustom#local.tmpSuffix#
						from (
							select contributionID, titleOnInvoice, answer
							from ##tmpContributionCustom
						) as reg
						PIVOT (min(answer) for titleonInvoice in (' + @crossProgramColList + ')) as p ';
					EXEC(@dynSQL);
				END
				ELSE
					SELECT contributionID 
					INTO ####tmpContributionCustom#local.tmpSuffix#
					FROM ##tmpContributionCustom
					WHERE 0=1;

				set @dynSQL = 'select tmp.[ContributionID], tmp.[Contribution Program], tmp.[Campaign Name], tmp.[Contribution Date], tmp.[Contribution Amount], ';
				
				set @dynSQL = @dynSQL + case when len(@fundList)>0 then 'tmpF.' + replace(@fundList,',',',tmpF.') + ', ' else '' end;
				
				set @dynSQL = @dynSQL + 'tmp.[Frequency], tmp.[Start Date], tmp.[End Date], tmp.[Number of Installments], tmp.[Contributor Last Name], 
					tmp.[Contributor First Name], tmp.[Contributor MemberNumber], tmp.[Contributor Company], tmp.[Status], 
					tmp.[Perpetual], tmp.[Anonymous], tmp.[Rate Name], tmp.[First Installment], tmp.[Recurring Installment], 
					tmp.[Pledge Value], m.*';

				set @dynSQL = @dynSQL + case when len(@crossProgramColList)>0 then ',tmpC.' + replace(@crossProgramColList,',',',tmpC.') else '' end;
				
				set @dynSQL = @dynSQL + ' into ####tmpFinalContributions#local.tmpSuffix#
					from ##tmpPrepContributions as tmp
					inner join ##tmpMembers as m ON m.memberID = tmp.MCMemberID
					left outer join ####tmpContributionsFunds#local.tmpSuffix# as tmpF on tmpF.fcontributionID = tmp.contributionID
					left outer join ####tmpContributionCustom#local.tmpSuffix# as tmpC on tmpC.contributionID = tmp.contributionID';
			
				EXEC(@dynSQL);

				EXEC tempdb..sp_rename '####tmpFinalContributions#local.tmpSuffix#.memberID', 'MemberCentralID', 'COLUMN';

				DECLARE @selectsql varchar(max) = '
					SELECT *, ROW_NUMBER() OVER(order by [Contribution Date] desc) as mcCSVorder 
					*FROM* ####tmpFinalContributions#local.tmpSuffix#';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

				IF OBJECT_ID('tempdb..##tmpContributions') IS NOT NULL
					DROP TABLE ##tmpContributions;
				IF OBJECT_ID('tempdb..##tmpProgramDistCodes') IS NOT NULL
					DROP TABLE ##tmpProgramDistCodes;
				IF OBJECT_ID('tempdb..##tmpContributionsPledged') IS NOT NULL
					DROP TABLE ##tmpContributionsPledged;
				IF OBJECT_ID('tempdb..##tmpPrepContributions') IS NOT NULL
					DROP TABLE ##tmpPrepContributions;
				IF OBJECT_ID('tempdb..####tmpContributionsFunds#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpContributionsFunds#local.tmpSuffix#;
				IF OBJECT_ID('tempdb..##tmpContributionCustom') IS NOT NULL
					DROP TABLE ##tmpContributionCustom;
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
					DROP TABLE ##tmp_membersForFS;
				IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
					DROP TABLE ##tmp_CF_ItemIDs;
				IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
					DROP TABLE ##tmp_CF_FieldData;
				IF OBJECT_ID('tempdb..####tmpContributionCustom#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpContributionCustom#local.tmpSuffix#;
				IF OBJECT_ID('tempdb..####tmpFinalContributions#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpFinalContributions#local.tmpSuffix#;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		<cfset local.success = true>
		<cfcatch type="Any">
			<cfset local.success = false>
			<cfif FindNoCase("was used in different programs",cfcatch.detail)>
				<cfset local.data = "The export file could not be generated. "& ReReplaceNoCase(cfcatch.detail,"(\*\*\*).*?(Errno 50000: )","","ALL")>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
		</cfcatch>
		</cftry>

		<cfif local.success>
			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

			<cfsavecontent variable="local.data">
				<cfoutput>
				<script type="text/javascript">doDownloadContributors('#local.stDownloadURL#');</script>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addContribution" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objContributions = CreateObject("component","model.contributions.contributions");

			arguments.event.paramValue('pid',0);
			arguments.event.paramValue('cpnid',0);
			arguments.event.paramValue('cid',0);
			local.strProgram = local.objContributions.getProgram(siteid=arguments.event.getValue('mc_siteInfo.siteid'),
				programID=val(arguments.event.getValue('pid')), 
				campaignID=arguments.event.getValue('cpnid'), 
				languageid=session.mcstruct.languageID, 
				hideAdminOnly=0);

			local.urlString = "&pid=" & arguments.event.getValue('pid') & "&cpnid=" & arguments.event.getValue('cpnid');

			if (arguments.event.getValue('ret','') eq 'member' and arguments.event.getValue('mid',0) gt 0) {
				local.urlString = local.urlString & "&ret=member";
				arguments.event.setValue('viewContributor',this.link.editMember & "&memberID=" & arguments.event.getValue('mid') & "&tab=contributions");
			}
			else
				arguments.event.setValue('viewContributor',this.link.listContributors);

			arguments.event.setValue('mainurl',this.link.addContribution & local.urlString);
			arguments.event.setValue('mainregurl',this.link.addContribution & local.urlString);
			arguments.event.setValue('errorurl',this.link.message);

			// Build breadCrumb Trail
			if (arguments.event.getValue('cid'))
				appendBreadCrumbs(arguments.event,{ link='', text="Clone Contribution" });
			else 
				appendBreadCrumbs(arguments.event,{ link='', text="Add Contribution" });
		</cfscript>

		<cfset local.data = CreateObject("component","contributionReg").doContributeProgram(event=arguments.event,strProgram=local.strProgram)>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="checkContributeRights" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.strProgramRights = buildRightAssignments(siteResourceID=arguments.siteResourceID, memberID=arguments.memberID, siteID=arguments.siteID)>
		<cfreturn val(local.strProgramRights.contribute)>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('errcode')>
					<p>
						<cfif arguments.event.getTrimValue('errcode') eq "nomatch">
							That program does not exist.
						<cfelseif arguments.event.getTrimValue('errcode') eq "notready">
							The contribution program you are trying to contribute is not yet ready.
						<cfelseif listFindNoCase("noright,nonqualify", arguments.event.getTrimValue('errcode'))>
							Selected member have not been granted access to contribute to the program.
						<cfelseif arguments.event.getTrimValue('errcode') eq "invalidamt">
							Select a valid contribution amount.
						<cfelse>
							An error occurred. Please contact the association or try again later.
						</cfif>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cancelContribution" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.type = arguments.event.getValue('type','single')>
		<cfset local.chkAll = arguments.event.getValue('chkAll',0)>
		<cfset local.contributionIDList = arguments.event.getValue('contributionIDList','')>
		<cfset local.notContributionIDList = arguments.event.getValue('notContributionIDList','')>
		<cfset local.doCancelContributionLink = buildCurrentLink(arguments.event,"doCancelContribution") & 
			"&type=#local.type#&chkAll=#local.chkAll#&contributionIDList=#local.contributionIDList#&notContributionIDList=#local.notContributionIDList#&mode=stream">
		<cfset local.startDate = NOW()>

		<cfif local.type EQ "single" AND ListLen(local.contributionIDList) EQ 1>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryContribStartDate">
				select startDate 
				from dbo.cp_contributions
				where contributionID = <cfqueryparam value="#local.contributionIDList#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfset local.startDate = local.qryContribStartDate.startDate>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_AROptions.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="deleteContributions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset local.contributionID = arguments.event.getValue('cid')>
		<cfset local.doDeleteContributionsLink = buildCurrentLink(arguments.event,"doDeleteContributions")>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_deleteContributions.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doDeleteContributions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();			
			local.contributionID = arguments.event.getValue('contributionID');
		</cfscript>

		<cfset CreateObject('component','contributions').deleteContribution(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
			contributionID=local.contributionID)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.reloadContributionsGrid();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>

	</cffunction>

	<cffunction name="doCancelContribution" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();			
			local.type = arguments.event.getValue('type','single');
			local.AROption = arguments.event.getValue('rdoAR');
			if (local.AROption eq 'A')
				local.cancellationDate = now();
			else
				local.cancellationDate = arguments.event.getValue('cancelDate');
		</cfscript>

		<cfif local.type eq "mass">
			<cfset local.objContributions = CreateObject('component', 'model.admin.contributions.contributions')>
			<cfset local.qryContributions = local.objContributions.getContributionsFromFilters(event=arguments.event, mode='massCancel')>
			<cfset local.contributionIDList = valueList(local.qryContributions.contributionID)>
		<cfelse>
			<cfset local.contributionIDList = arguments.event.getValue('contributionIDList','')>
		</cfif>

		<cfset CreateObject('component','contributions').cancelContribution(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				contributionIDList=local.contributionIDList, AROption=local.AROption, cancellationDate=local.cancellationDate)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.resetAllRecordSelections(false);
				top.reloadContributionsGrid();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showContribInstallmentDetails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objContributions = CreateObject('component','contributions');
			local.contributionID = arguments.event.getValue('cid',0);
			local.formlink = buildCurrentLink(arguments.event,"preprocessPaymentDetails") & "&mode=stream";;
		</cfscript>

		<cfset local.qryContributionDetails = local.objContributions.getContributionDetails(contributionID=local.contributionID)>
		<cfset local.totalInvAmtDue = local.objContributions.getContributionInvDueAmount(contributionID=local.contributionID)>
		<cfset local.totalAllocatedFutureInstallAmt = local.objContributions.getAllocatedInstallAmt(contributionID=local.contributionID)>
		
		<cfif local.qryContributionDetails.isPerpetual is 0>
			<cfset local.totalMoneyFieldInstallAmt = local.objContributions.getTotalMoneyFieldInstallments(contributionID=local.contributionID, installmentCount=local.qryContributionDetails.installmentCount)>
			<cfset local.qryTotalFeesAndPaid = local.objContributions.getTotalFeesAndPaidByContribution(orgID=arguments.event.getValue('mc_siteInfo.orgID'), contributionID=local.contributionID)>

			<cfset local.totalAmountPaid = val(local.qryTotalFeesAndPaid.totalPaid)>
			<cfset local.totalInstallmentAmount = val(local.qryContributionDetails.installmentCount) * val(local.qryContributionDetails.installmentAmt)>
			<cfset local.totalInstallAmtNotYetConvToSchedule = (local.totalInstallmentAmount + local.totalMoneyFieldInstallAmt) - (local.totalAmountPaid + local.totalAllocatedFutureInstallAmt + local.totalInvAmtDue)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_contributionInstallmentDetails.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="preprocessPaymentDetails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.objContributions = CreateObject('component','contributions')>
			<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>

			<cfset local.contributionID = arguments.event.getValue('contributionID',0)>
			<cfset local.paymentAmount = val(arguments.event.getValue('amount',0))>
			<cfset local.addPaymentLink = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct">

			<cfset local.qryContributionDetails = local.objContributions.getContributionDetails(contributionID=local.contributionID)>
			<cfset local.totalInvAmtDue = local.objContributions.getContributionInvDueAmount(contributionID=local.contributionID)>
			<cfif local.paymentAmount gt local.totalInvAmtDue>
				<cfif local.qryContributionDetails.isPerpetual is 1>
					<cfset local.installmentCount = 100>
				<cfelse>
					<cfset local.installmentCount = val(local.qryContributionDetails.installmentCount)>
				</cfif>
				<cfset local.totalAllocatedFutureInstallAmt = local.objContributions.getAllocatedInstallAmt(contributionID=local.contributionID)>
				<cfset local.totalMoneyFieldInstallAmt = local.objContributions.getTotalMoneyFieldInstallments(contributionID=local.contributionID, installmentCount=local.installmentCount)>
				<cfset local.qryTotalFeesAndPaid = local.objContributions.getTotalFeesAndPaidByContribution(orgID=arguments.event.getValue('mc_siteInfo.orgID'), contributionID=local.contributionID)>
				<cfset local.totalAmountPaid = val(local.qryTotalFeesAndPaid.totalPaid)>
				<cfset local.totalInstallmentAmount = local.installmentCount * val(local.qryContributionDetails.installmentAmt)>
				<cfset local.totalInstallAmtNotYetConvToInstallments = (local.totalInstallmentAmount + local.totalMoneyFieldInstallAmt) - (local.totalAmountPaid + local.totalAllocatedFutureInstallAmt + local.totalInvAmtDue)>
			</cfif>

			<cfif local.paymentAmount gt 0>
				<!--- payment amount equal to or less than the invoice dues, do nothing special --->
				<cfif local.paymentAmount lte local.totalInvAmtDue>

				<!--- payment amount can be covered by converting some of the existing installments to sales --->
				<cfelseif local.paymentAmount lte (local.totalInvAmtDue + local.totalAllocatedFutureInstallAmt)>
					<cfset local.amountToBeConvertedToSale = local.paymentAmount - local.totalInvAmtDue>

					<cfstoredproc procedure="cp_convertFutureInstallmentsToSale" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.contributionID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.amountToBeConvertedToSale#">
					</cfstoredproc>

				<!--- payment amount can be covered by converting schedules not yet converted to installments and then sale --->
				<cfelseif local.paymentAmount lte (local.totalInvAmtDue + local.totalAllocatedFutureInstallAmt + local.totalInstallAmtNotYetConvToInstallments)>
					<cfset local.amountToBeConvertedToInstallments = local.paymentAmount - (local.totalInvAmtDue + local.totalAllocatedFutureInstallAmt)>
					<cfset local.amountToBeConvertedToSale = local.paymentAmount - local.totalInvAmtDue>

					<cfstoredproc procedure="cp_convertFutureSchedulesToInstallments" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.contributionID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.amountToBeConvertedToInstallments#">
					</cfstoredproc>

					<cfstoredproc procedure="cp_convertFutureInstallmentsToSale" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.contributionID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.amountToBeConvertedToSale#">
					</cfstoredproc>
				</cfif>

				<cfset local.qryContributorDues = local.objContributions.getContributorDues(contributionID=local.contributionID)>
				<cfquery name="local.qryTotalDues" dbtype="query">
					select sum(amountDue) as totalInvDue
					from [local].qryContributorDues
				</cfquery>

				<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryContributionDetails.memberID, t="#local.qryContributionDetails.programName# - #local.qryContributionDetails.frequency#", ta=local.qryTotalDues.totalInvDue, tmid=local.qryContributionDetails.memberID, ad="v|#valueList(local.qryContributorDues.invoiceID)#", pa=local.paymentAmount)>
				<cflocation url="#local.addPaymentLink#&pa=pay&po=#local.addPaymentEncString#" addtoken="false">
			</cfif>

			<cfthrow message="Not directed to pay screen">
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cflocation url="#this.link.message#&errcode=nopay&mode=direct" addtoken="false">
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="linkCCToContributor" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = CreateObject('component','contributions');
		local.contributionID = arguments.event.getValue('cid',0);
		local.extrapayJS = "";

		local.qryContributionDetails = local.objContributions.getContributionDetails(contributionID=local.contributionID);
		local.formlink = buildCurrentLink(arguments.event,"saveCCToContributor") & "&mode=stream";
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryContributorPayProfile">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
				@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">,
				@contributionID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.contributionID#">,
				@eligibleProfileIDList varchar(200);
			DECLARE @tmpInvProfiles TABLE (invoiceProfileID int);

			INSERT INTO @tmpInvProfiles (invoiceProfileID)
			SELECT ip.profileID
			FROM dbo.cp_contributions AS c
			INNER JOIN dbo.cp_distributions AS cpd ON cpd.programID = c.programID
				AND c.contributionID = @contributionID
			INNER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = cpd.GLAccountID
			INNER JOIN dbo.tr_invoiceProfiles AS ip ON ip.orgID = @orgID AND ip.profileID = gl.invoiceProfileID
				UNION
			SELECT ip.profileID
			FROM dbo.cp_contributions AS c
			INNER JOIN dbo.cp_programs AS cp ON cp.programID = c.programID
				AND c.contributionID = @contributionID
			INNER JOIN dbo.tr_GLAccounts AS gl ON gl.orgID = @orgID AND gl.GLAccountID = cp.defaultGLAccountID
			INNER JOIN dbo.tr_invoiceProfiles AS ip ON ip.orgID = @orgID AND ip.profileID = gl.invoiceProfileID;

			SELECT @eligibleProfileIDList = STRING_AGG(p.profileID, ',')
			FROM @tmpInvProfiles AS tmp
			INNER JOIN dbo.tr_invoiceProfiles AS ip ON ip.orgID = @orgID AND ip.profileID = tmp.invoiceProfileID
			INNER JOIN dbo.tr_invoiceProfilesMerchantProfiles AS invP ON invP.invoiceProfileID = ip.profileID
			INNER JOIN dbo.mp_profiles AS p ON p.profileID = invP.merchantProfileID
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = p.gatewayID
			WHERE p.[status] = 'A'
			AND p.siteID = @siteID
			AND g.isActive = 1;

			SELECT DISTINCT cpp.MPProfileID, cpp.payProfileID, cpp.payProcessFee, ISNULL(@eligibleProfileIDList,'') AS eligibleProfileIDList
			FROM dbo.cp_contributions AS c
			LEFT OUTER JOIN dbo.cp_contributionPayProfiles AS cpp ON cpp.contributionID = c.contributionID
			WHERE c.contributionID = @contributionID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<!--- get a site's merchant profiles --->
		<cfquery name="local.qryMerchantProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			select p.profileID, p.profileName, p.profileCode, g.gatewayClass, g.gatewayID, p.enableProcessingFeeDonation, 
				p.processFeeDonationFeePercent, pfm.title as processFeeDonationFETitle, pfm.message as processFeeDonationFEMsg, 
				p.processFeeContributionsFELabel, p.processFeeContributionsFEDenyLabel
			from dbo.mp_profiles p
			inner join dbo.mp_gateways g on g.gatewayID = p.gatewayID
				and g.isActive = 1
			left outer join dbo.tr_solicitationMessages as pfm on pfm.siteID = @siteID
				and pfm.messageID = p.solicitationMessageID
			where p.siteID = @siteID
			and p.status in ('A','I')
			and p.profileID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#local.qryContributorPayProfile.eligibleProfileIDList#">);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_contributionCCSelect.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCCToContributor" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		
		<cfset local.objContributions = CreateObject('component','contributions')>
		<cfset local.paySource = arguments.event.getValue('paySource','0')>
		<cfset local.payProfileID = arguments.event.getValue('p_#local.paySource#_mppid','0')>
		<cfset local.contributionID = arguments.event.getValue('cid','0')>
		<cfset local.payProcessFee = arguments.event.getValue('processFeeDonation#local.paySource#',0)>

		<cfquery name="local.qryUpdateCPPayProfile" datasource="#application.dsn.membercentral.dsn#">
			EXEC dbo.cp_updatePaymentProfile
				@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">,
				@contributionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.contributionID#">,
				<cfif local.payProfileID>
					@MPProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.paySource#">,
					@payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.payProfileID#">,
					@payProcessFee = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.payProcessFee#">,
				<cfelse>
					@MPProfileID = NULL,
					@payProfileID = NULL,
					@payProcessFee = 0,
				</cfif>
				@retainCurrentFeePercent = 0,
				@recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>
					top.dofilterCont();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cancelInstallSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.scheduleID = arguments.event.getValue('sid');
			local.doCancelInstallScheduleLink = buildCurrentLink(arguments.event,"doCancelInstallSchedule") & "&mode=stream";
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDueDate">
			select dueDate
			from dbo.cp_contributionSchedule
			where scheduleID = <cfqueryparam value="#local.scheduleID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_scheduleAROptions.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doCancelInstallSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objContributions = CreateObject('component','contributions')>
		<cfset local.objContributions.cancelInstallmentSchedule(mcproxy_siteID=arguments.event.getValue('mc_siteInfo.siteid'), 
					scheduleID=arguments.event.getValue('scheduleID',0), AROption=arguments.event.getValue('rdoAR',''))>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>
					top.scheduleDatesTable.draw();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="changeProgramInstance" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objContributions = CreateObject('component','contributions');
			local.programID = int(val(arguments.event.getValue('pid',0)));
			local.qryContributionProgram = local.objContributions.getProgramDetails(siteID=arguments.event.getValue('mc_siteinfo.siteid'), programID=local.programID);
			local.qryContributionAppInstances = local.objContributions.getContributionAppInstances(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			local.saveChangeInstanceLink = buildCurrentLink(arguments.event,"saveProgramInstanceChange") & "&pid=#local.programID#&mode=direct";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_changeProgramInstance.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveProgramInstanceChange" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.programID = int(val(arguments.event.getValue('pid',0)));
			CreateObject('component','contributions').doChangeProgramInstance(siteID=arguments.event.getValue('mc_siteinfo.siteid'), programID=local.programID, newAppInstanceID=arguments.event.getValue('newAppInstanceID'));
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">
				top.MCModalUtils.hideModal();
				top.location.reload();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="importContributorsPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objContributions = CreateObject('component','contributions');

		local.programID = arguments.event.getValue('pID',0);
		local.qryContributionProgram = local.objContributions.getProgramDetails(siteID=arguments.event.getValue('mc_siteinfo.siteid'), programID=local.programID);
		local.qryCPStatuses = local.objContributions.getCPStatuses()

		local.sampleImportContributorTemplateLink = buildCurrentLink(arguments.event,"sampleImportContributorTemplate") & "&pID=#local.programID#&mode=direct";
		local.importContributorLink = buildCurrentLink(arguments.event,"importContributors") & "&pID=#local.programID#&mode=direct";

		local.sampleAssociateCOFImportTemplateLink = buildCurrentLink(arguments.event,"sampleAssociateCOFImportTemplate") & "&mode=direct";
		local.processAssociateCardsOnFileLink = buildCurrentLink(arguments.event,"processAssociateCardsOnFile") & "&pID=#local.programID#&mode=direct";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_importContributors.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sampleImportContributorTemplate" access="package" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='cp')>
		<cfset local.reportFileName = "ContributorImportTemplate.csv">

		<cfset local.arrFields = []>

		<cfquery name="local.qryDistribCodes" datasource="#application.dsn.membercentral.dsn#">
			select distCode
			from dbo.cp_distributions
			where programID = <cfqueryparam value="#arguments.event.getValue('pID',0)#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.qryDistribCodes.recordcount>
			<cfset arrayAppend(local.arrFields, "RateName")>
			<cfloop query="local.qryDistribCodes">
				<cfset arrayAppend(local.arrFields, local.qryDistribCodes.distCode)>
			</cfloop>
		</cfif>
		<cfset arrayAppend(local.arrFields, "StartDate")>
		<cfset arrayAppend(local.arrFields, "EndDate")>
		<cfset arrayAppend(local.arrFields, "Frequency")>
		<cfset arrayAppend(local.arrFields, "Status")>
		<cfset arrayAppend(local.arrFields, "isAnonymous")>
		<cfset arrayAppend(local.arrFields, "AmountAlreadyPaid")>

		<cfquery name="local.qryProgramCustomFields" datasource="#application.dsn.membercentral.dsn#">
			select f.fieldReference
			from dbo.cf_fields as f 
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID 
			inner join dbo.cp_programs as cp on cp.programID = f.detailID and f.controllingSiteResourceID = cp.siteResourceID
			where cp.programID = <cfqueryparam value="#arguments.event.getValue('pID',0)#" cfsqltype="CF_SQL_INTEGER">
			and f.isActive = 1
			and ft.displayTypeCode <> 'LABEL'
			and 1 = case when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then 
							case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
						else 1 end;
		</cfquery>
		<cfif local.qryProgramCustomFields.recordcount>
			<cfloop query="local.qryProgramCustomFields">
				<cfset arrayAppend(local.arrFields, local.qryProgramCustomFields.fieldReference)>
			</cfloop>
		</cfif>

		<cfquery name="local.qryCrossProgramCustomField" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @usageID int, @controllingSiteResourceID int;
			set @usageID = dbo.fn_cf_getUsageID('ContributionAdmin','Role',NULL);
			set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.siteResourceID#">;

			select f.fieldReference
			from dbo.cf_fields as f 
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			where f.controllingSiteResourceID = @controllingSiteResourceID
			and f.usageID = @usageID
			and f.isActive = 1
			and ft.displayTypeCode <> 'LABEL'
			and 1 = case when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then 
							case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
						else 1 end; 
		</cfquery>
		<cfif local.qryCrossProgramCustomField.recordcount>
			<cfloop query="local.qryCrossProgramCustomField">
				<cfset arrayAppend(local.arrFields, local.qryCrossProgramCustomField.fieldReference)>
			</cfloop>
		</cfif>

		<cfquery name="local.qryExport" datasource="#application.dsn.membercentral.dsn#" result="local.qryExportResult">
			set nocount on;

			-- create temp table
			IF OBJECT_ID('tempdb..##tmpCPContributorTemplate') IS NOT NULL
				DROP TABLE ##tmpCPContributorTemplate;
			CREATE TABLE ##tmpCPContributorTemplate (
				MemberNumber varchar(30),
				CampaignName varchar(30)
				<cfloop array="#local.arrFields#" index="local.thisCol">
					, [#local.thisCol#] varchar(10)
				</cfloop>
			);

			DECLARE @selectsql varchar(max) = '
				SELECT *, ROW_NUMBER() OVER(order by MemberNumber) as mcCSVorder 
				*FROM* ##tmpCPContributorTemplate';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpCPContributorTemplate') IS NOT NULL 
				DROP TABLE ##tmpCPContributorTemplate;
		</cfquery>
				
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sampleAssociateCOFImportTemplate" access="public" output="false" returntype="struct" hint="Sample Import Template">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">

		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='cp')>
		<cfset local.reportFileName = "AssociateCOFImportTemplate.csv">

		<cfset local.fieldNames = "MemberNumber,ProfileCode,Last4">
		<cfset local.arrFields = listToArray(local.fieldNames)>

		<cfquery name="local.qryExport" datasource="#Application.dsn.membercentral.dsn#" result="local.qryExportResult">
			set nocount on;

			IF OBJECT_ID('tempdb..##tmpACOFTemplate') IS NOT NULL 
				DROP TABLE ##tmpACOFTemplate;
			CREATE TABLE ##tmpACOFTemplate (
				autoID int
				<cfloop array="#local.arrFields#" index="local.thisCol">
					, #local.thisCol# varchar(30)
				</cfloop>
			);
			insert into ##tmpACOFTemplate (#local.fieldNames#)
			values ('Req', 'Req', 'Req');

			DECLARE @selectsql varchar(max) = '
				SELECT #local.fieldNames#, ROW_NUMBER() OVER(order by autoID) as mcCSVorder 
				*FROM* ##tmpACOFTemplate';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpACOFTemplate') IS NOT NULL 
				DROP TABLE ##tmpACOFTemplate;
		</cfquery>
		
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="importContributors" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>
		<cfset local.programID = arguments.event.getValue('pID',0)>
		<cfset local.importContributorsLink = buildCurrentLink(arguments.event,"importContributorsPrompt") & "&pid=#local.programID#&mode=direct">

		<!--- extend timeout --->
		<cfsetting requesttimeout="500">
		
		<cfset local.completeResult = processImportContributors(event=arguments.event)>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<h4>Import Contributor Results</h4>
			
			<cfif not local.completeResult.success>
				<div class="alert alert-danger">
					<div class="font-weight-bold mb-3">The update was stopped and requires your attention.</div>

					<cfif local.completeResult.errorCode eq 105>
						<cfset local.arrErrors = XMLSearch(local.completeResult.importResultXML,"/import/errors/error")>
						<div>
							<cfif arrayLen(local.arrErrors) gt 300>
								<b>Only the first 300 errors are shown.</b><br/><br/>
							</cfif>
							<cfset local.thisErrNum = 0>
							<cfloop array="#local.arrErrors#" index="local.thisErr">
								<cfset local.thisErrNum = local.thisErrNum + 1>
								#local.thisErr.xmlAttributes.msg#<br/>
								<cfif local.thisErrNum is 300>
									<cfbreak>
								</cfif>
							</cfloop>
						</div>
					<cfelse>
						<div>#local.completeResult.errorInfo[local.completeResult.errorCode]#</div>
					</cfif>
					<button type="button" name="btnDoOver" class="btn btn-sm btn-secondary mt-2" onclick="self.location.href='#local.importContributorsLink#';">Try upload again</button>
				</div>
			<cfelse>
				<div class="alert alert-success">
					<div>The import has been scheduled and will begin soon.</div>
					<button type="button" class="btn btn-sm btn-secondary mt-2" onclick="top.reloadContributionsGrid();top.MCModalUtils.hideModal();">Close</button>
				</div>
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="processImportContributors" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=true, errorCode=999, errorInfo=StructNew() }>
		
		<!--- Attempt upload --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteinfo.orgCode'))>

			<cffile action="upload" filefield="uploadFileName" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">

			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>

			<cfif listFindNoCase("csv",local.strImportFile.uploadFilenameExt) is 0>
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 1>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.errMsg)>
				<cfreturn local.returnStruct>
			</cfif> 
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem uploading the selected file. Only comma separated value files (CSV) files can be uploaded. Try the upload again or contact us for assistance.")>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>

		<!--- parse CSV --->
		<cfset local.parseResult = CreateObject("component","model.admin.common.modules.import.import").parseCSV(stFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#", stFilePathTmp="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#Parsed.csv")>
		<cfset local.returnStruct.success = local.parseResult.isErr is 0>
		<cfif NOT local.returnStruct.success>
			<cfset local.returnStruct.errorCode = 101>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfreturn local.returnStruct>
		</cfif>

		<cftry>
			<!--- had to do it in a cfquery because cfstoredproc kept truncating the result --->
			<cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.importResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @siteID int, @programID int, @runByMemberID int, @importResult xml, @errCount int;

					set @siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
					set @programID = <cfqueryparam value="#arguments.event.getValue('pID',0)#" cfsqltype="CF_SQL_INTEGER">;
					set @runByMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">;
					
					-- bulk insert from file
					BEGIN TRY
						IF OBJECT_ID('tempdb..##cp_ContributorImport') IS NOT NULL 
							DROP TABLE ##cp_ContributorImport;
						CREATE TABLE ##cp_ContributorImport (
							<cfloop list="#local.parseResult.strTableColumnNames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max)<cfif local.thisCol neq listLast(local.parseResult.strTableColumnNames,chr(7))>, </cfif>
							</cfloop>
						);
						BULK INSERT ##cp_ContributorImport FROM '#local.strImportFile.strFolder.folderPathUNC#\#local.strImportFile.uploadFilenameWithoutExt#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);
					END TRY
					BEGIN CATCH
						set @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.cp_importContributors @siteID=@siteID, @programID=@programID, @runByMemberID=@runByMemberID, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						set @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					set @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;

					IF OBJECT_ID('tempdb..##cp_ContributorImport') IS NOT NULL 
						DROP TABLE ##cp_ContributorImport;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.returnStruct.numFatalErrors = local.qryImport.errCount>

			<cfif local.returnStruct.numFatalErrors gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 105>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'')>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfif findNoCase("An object or column name is missing or empty.",cfcatch.detail)>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the files.<br/>It looks like there is a data column with a blank column heading. This can happen if there is any data in any column to the right of your last column in the uploaded file.")>
			<cfelse>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the files.")>
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="processAssociateCardsOnFile" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objContributions = CreateObject("component","contributions")>

		<!--- extend CF timeout --->
		<cfsetting requesttimeout="500">

		<cfset local.programID = arguments.event.getValue('pID',0)>
		<cfset local.importContributorsLink = buildCurrentLink(arguments.event,"importContributorsPrompt") & "&pid=#local.programID#&mode=direct">

		<cfset local.processResult = local.objContributions.importCPCardsOnFile(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), siteID=arguments.event.getValue('mc_siteinfo.siteID'),
			programID=local.programID, cpStatusCodeList=arguments.event.getValue('cpStatus','P,A'))>
		<cfset local.data = local.objContributions.showImportResults(strResult=local.processResult, doAgainURL=local.importContributorsLink)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editCampaign" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="campaignID" type="numeric" required="true">
		<cfargument name="ahrLink" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.objContributions = CreateObject('component','contributions')>
		<cfset local.strCampaign = local.objContributions.getCampaignDetails(programID=arguments.programID, campaignID=arguments.campaignID)>
		<cfset local.qryProgramRates = local.objContributions.getAllProgramRates(programID=arguments.programID)>
		<cfset local.qryProgramDistributions = local.objContributions.getAllProgramDistributions(programID=arguments.programID)>
		<cfset local.qryProgramFrequencies = local.objContributions.getAllProgramFrequencies(programID=arguments.programID)>

		<cfset local.campaignMatrixListLink = "#arguments.ahrLink#&mca_jsonlib=mcdatatable&com=contributionJSON&meth=getCampaignMatrix&mode=stream&cid=#arguments.campaignID#">

		<cfset arguments.campaignID = val(local.strCampaign.qryCampaign.campaignID)>

		<cfif arguments.campaignID gt 0>
			<cfset local.baseTestLink = getAppBaseLink(applicationInstanceID=val(local.strCampaign.qryCampaign.applicationInstanceID))>
			<cfset local.mainhostname = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).mainhostname>
			<cfif local.strCampaign.qryCampaign.status is 'I'>
				<cfset local.arrCampaignIssues = arrayNew(1)>
				<cfif NOT (len(local.strCampaign.qryCampaign.campaignFormInstContent) and len(local.strCampaign.qryCampaign.campaignFormNonQualContent))>
					<cfif not len(local.strCampaign.qryCampaign.campaignFormInstContent)>
						<cfset arrayAppend(local.arrCampaignIssues,"Form Instructions must be defined.")>
					</cfif>
					<cfif not len(local.strCampaign.qryCampaign.campaignFormNonQualContent)>
						<cfset arrayAppend(local.arrCampaignIssues,"Non-Qualifying Member Selected must be defined.")>
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfset local.arrProgramFrequencies = arrayNew(1)>
		<cfloop query="local.qryProgramFrequencies">
			<cfset local.strTempFreq = { progFreqID=local.qryProgramFrequencies.progFreqID, isRecurring=local.qryProgramFrequencies.isRecurring, frequency=local.qryProgramFrequencies.frequency }>
			<cfset arrayAppend(local.arrProgramFrequencies, duplicate(local.strTempFreq))>
		</cfloop>

		<cfset local.strNewAcctFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.siteID, selectorID="NewAccFieldset", selectedValue=val(local.strCampaign.qryCampaign.newAcctFieldsetID))>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editCampaign.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="startExportContributionsChangeLog" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.exportContributionsChangeLog = buildCurrentLink(arguments.event,"exportContributionsChangeLog") & "&mode=stream">
		<cfset local.strMemberDataFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'),
			selectorID="fsid", inlinePreviewSectionID="exportFormContainer")>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_contributionChangeLogExport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="exportContributionsChangeLog" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfsetting requesttimeout="500">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=application.objSiteInfo.getSiteInfo(arguments.event.getValue('mc_siteInfo.sitecode')).siteID)>
		<cfset local.reportFileName = "ContributionChangeLog-#DateFormat(Now(),'yyyymmdd')#.csv">

		<cfset CreateObject("component","contributions").getContributionsChangeLogFromFilters(event=arguments.event, mode='export', fieldsetID=arguments.event.getValue("fsid",0), folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName)>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.location.href="/tsdd/#local.stDownloadURL#";
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
</cfcomponent>